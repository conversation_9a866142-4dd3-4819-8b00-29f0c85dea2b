"""
Sistema para testar contas novas da ElevenLabs SEMPRE com proxy
"""

import sys
from pathlib import Path
import time
import json

sys.path.append(str(Path(__file__).parent))

from proxy_manager import WebshareProxyManager

def test_new_key_with_proxy(api_key, proxy_manager, country=None):
    """Testa uma nova chave API SEMPRE com proxy"""
    
    key_preview = f"{api_key[:8]}..."
    print(f"\n🧪 TESTANDO NOVA CHAVE: {key_preview}")
    
    results = {
        "key": key_preview,
        "voices_test": False,
        "tts_test": False,
        "country": country,
        "errors": []
    }
    
    # 1. Teste de listagem de vozes
    print(f"   1️⃣ Testando listagem de vozes com proxy...")
    try:
        voices_success, voices_result = test_voices_with_proxy(api_key, proxy_manager, country)
        results["voices_test"] = voices_success
        
        if voices_success:
            print(f"      ✅ Vozes: {voices_result}")
        else:
            print(f"      ❌ Vozes: {voices_result}")
            results["errors"].append(f"Voices: {voices_result}")
            
    except Exception as e:
        print(f"      ❌ Vozes: Erro {str(e)}")
        results["errors"].append(f"Voices: {str(e)}")
    
    # 2. Teste de TTS (só se vozes funcionaram)
    if results["voices_test"]:
        print(f"   2️⃣ Testando TTS com proxy...")
        try:
            tts_success, tts_result = test_tts_with_proxy_only(api_key, proxy_manager, country)
            results["tts_test"] = tts_success
            
            if tts_success:
                print(f"      ✅ TTS: {tts_result}")
            else:
                print(f"      ❌ TTS: {tts_result}")
                results["errors"].append(f"TTS: {tts_result}")
                
        except Exception as e:
            print(f"      ❌ TTS: Erro {str(e)}")
            results["errors"].append(f"TTS: {str(e)}")
    else:
        print(f"   2️⃣ Pulando TTS (vozes falharam)")
    
    # 3. Resultado final
    if results["voices_test"] and results["tts_test"]:
        print(f"   🎉 CHAVE VÁLIDA! Funcionando 100%")
        results["status"] = "working"
    elif results["voices_test"]:
        print(f"   ⚠️  Chave parcial (só vozes)")
        results["status"] = "partial"
    else:
        print(f"   ❌ Chave inválida")
        results["status"] = "blocked"
    
    return results

def test_voices_with_proxy(api_key, proxy_manager, country=None):
    """Testa listagem de vozes com proxy"""
    url = "https://api.elevenlabs.io/v1/voices"
    
    headers = {
        "xi-api-key": api_key,
        "Content-Type": "application/json"
    }
    
    try:
        response = proxy_manager.make_request_with_proxy(
            method="GET",
            url=url,
            headers=headers,
            use_country_proxy=country,
            max_retries=1
        )
        
        if response.status_code == 200:
            voices_data = response.json()
            voice_count = len(voices_data.get("voices", []))
            return True, f"{voice_count} vozes disponíveis"
        else:
            try:
                error_data = response.json()
                error_msg = error_data.get("detail", {}).get("message", "Erro")
                return False, f"HTTP {response.status_code}: {error_msg[:50]}"
            except:
                return False, f"HTTP {response.status_code}"
            
    except Exception as e:
        return False, str(e)

def test_tts_with_proxy_only(api_key, proxy_manager, country=None):
    """Testa TTS APENAS com proxy"""
    voice_id = "9BWtsMINqrJLrRacOk9x"  # Aria
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
    
    headers = {
        "xi-api-key": api_key,
        "Content-Type": "application/json",
        "Accept": "audio/mpeg"
    }
    
    payload = {
        "text": "Teste de nova conta com proxy.",
        "model_id": "eleven_multilingual_v2"
    }
    
    params = {"output_format": "mp3_44100_128"}
    
    try:
        response = proxy_manager.make_request_with_proxy(
            method="POST",
            url=url,
            headers=headers,
            json=payload,
            params=params,
            use_country_proxy=country,
            max_retries=1
        )
        
        if response.status_code == 200:
            # Sucesso! Salva arquivo de teste
            output_path = f"teste_nova_conta_{api_key[:8]}.mp3"
            with open(output_path, "wb") as f:
                f.write(response.content)
            
            file_size = len(response.content)
            return True, f"Áudio gerado: {output_path} ({file_size} bytes)"
        else:
            try:
                error_data = response.json()
                error_msg = error_data.get("detail", {}).get("message", "Erro")
                return False, f"HTTP {response.status_code}: {error_msg[:50]}"
            except:
                return False, f"HTTP {response.status_code}"
            
    except Exception as e:
        return False, str(e)

def test_different_countries(api_key, proxy_manager):
    """Testa a mesma chave com proxies de países diferentes"""
    print(f"\n🌍 TESTANDO DIFERENTES PAÍSES")
    
    # Países disponíveis nos seus proxies
    countries = ["HR", "US"]  # Croácia e EUA
    
    results = {}
    
    for country in countries:
        print(f"\n   🇺🇸 Testando com proxy de {country}...")
        
        try:
            result = test_new_key_with_proxy(api_key, proxy_manager, country)
            results[country] = result
            
        except Exception as e:
            print(f"      ❌ Erro com {country}: {e}")
            results[country] = {"status": "error", "error": str(e)}
        
        # Pausa entre países
        time.sleep(2)
    
    return results

def main():
    print("🆕 TESTADOR DE CONTAS NOVAS COM PROXY")
    print("="*50)
    print("⚠️  IMPORTANTE: Sempre use proxy desde o primeiro momento!")
    print()
    
    webshare_token = "hceyn6sv553b9x1xieu5krpjolq1r1l33lu0wkhb"
    
    try:
        # 1. Inicializa proxy
        print("1️⃣ Inicializando proxy manager...")
        proxy_manager = WebshareProxyManager(
            api_token=webshare_token,
            use_rotating_endpoint=True,
            enable_fallback=True
        )
        
        # Status do proxy
        proxy_status = proxy_manager.get_proxy_status()
        print(f"   ✅ {proxy_status['proxy_config']['total_proxies']} proxies ativos")
        print(f"   🌍 Países: {', '.join(proxy_status['proxy_config']['countries'])}")
        
        # 2. Solicita chaves para testar
        print(f"\n2️⃣ INSIRA CHAVES NOVAS PARA TESTAR:")
        print(f"   💡 Dica: Cole uma chave por vez, Enter para próxima, 'fim' para terminar")
        
        new_keys = []
        while True:
            key_input = input(f"\n   Chave {len(new_keys) + 1} (ou 'fim'): ").strip()
            
            if key_input.lower() in ['fim', 'end', 'done', '']:
                break
            
            if key_input.startswith('sk_') and len(key_input) > 20:
                new_keys.append(key_input)
                print(f"      ✅ Chave {len(new_keys)} adicionada: {key_input[:8]}...")
            else:
                print(f"      ❌ Chave inválida (deve começar com 'sk_')")
        
        if not new_keys:
            print(f"\n❌ Nenhuma chave fornecida. Exemplo de uso:")
            print(f"   1. Crie 1-2 contas novas na ElevenLabs")
            print(f"   2. Execute este script")
            print(f"   3. Cole as chaves novas uma por vez")
            print(f"   4. Teste com proxy desde o primeiro momento")
            return 1
        
        # 3. Testa cada chave
        print(f"\n3️⃣ TESTANDO {len(new_keys)} CHAVES COM PROXY:")
        
        all_results = []
        working_keys = []
        
        for i, api_key in enumerate(new_keys, 1):
            print(f"\n{'='*60}")
            print(f"TESTE {i}/{len(new_keys)}")
            print(f"{'='*60}")
            
            # Testa com rotação automática primeiro
            result = test_new_key_with_proxy(api_key, proxy_manager)
            all_results.append(result)
            
            if result["status"] == "working":
                working_keys.append(api_key)
            
            # Se falhou, tenta países específicos
            elif result["status"] == "blocked":
                print(f"\n   🔄 Tentando países específicos...")
                country_results = test_different_countries(api_key, proxy_manager)
                
                # Verifica se algum país funcionou
                for country, country_result in country_results.items():
                    if country_result.get("status") == "working":
                        working_keys.append(api_key)
                        print(f"      🎉 Funcionou com proxy de {country}!")
                        break
            
            # Pausa entre chaves
            time.sleep(5)
        
        # 4. Resultado final
        print(f"\n{'='*60}")
        print(f"🏁 RESULTADO FINAL")
        print(f"{'='*60}")
        print(f"   ✅ Chaves funcionais: {len(working_keys)}/{len(new_keys)}")
        
        if working_keys:
            print(f"\n🎉 CHAVES FUNCIONAIS:")
            for key in working_keys:
                print(f"   • {key[:8]}...")
            
            # Salva chaves funcionais
            working_config = {
                "working_keys": working_keys,
                "proxy_always_enabled": True,
                "voice_id": "9BWtsMINqrJLrRacOk9x",
                "voice_name": "Aria",
                "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "countries_available": proxy_status['proxy_config']['countries']
            }
            
            with open("chaves_funcionais_com_proxy.json", "w") as f:
                json.dump(working_config, f, indent=2)
            
            print(f"\n💾 Configuração salva em: chaves_funcionais_com_proxy.json")
            
            print(f"\n🚀 PRÓXIMOS PASSOS:")
            print(f"   1. Criar arquivo de chaves apenas com as funcionais")
            print(f"   2. Executar sistema completo com proxy habilitado")
            print(f"   3. Processar roteiros!")
            
        else:
            print(f"\n😞 Nenhuma chave funcionou.")
            print(f"💡 Sugestões:")
            print(f"   - Aguardar algumas horas")
            print(f"   - Tentar criar contas em horários diferentes")
            print(f"   - Considerar plano pago da ElevenLabs")
        
        # Salva todos os resultados para análise
        with open("teste_contas_novas_results.json", "w") as f:
            json.dump(all_results, f, indent=2)
        
        print(f"\n📊 Resultados detalhados salvos em: teste_contas_novas_results.json")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ ERRO: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
