import openai_batch_script_v3
import gerador_thumbnails_lote_v2
import os

# Determina o diretório do script atual
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# Determina a raiz do projeto (dois níveis acima do diretório do script)
PROJECT_ROOT = os.path.abspath(os.path.join(SCRIPT_DIR, "..", ".."))

if __name__ == "__main__":
    print("--- Iniciando Sistema Unificado de Geração de Thumbnails ---")
    
    # Etapa 1: Geração de Textos com OpenAI
    print("\n--- Etapa 1: Gerando Textos para Thumbnails ---")
    
    # O script openai_batch_script_v3 agora lida com a leitura da chave API do local correto.
    # Mantemos uma verificação aqui apenas para feedback inicial, se desejado.
    api_key_path_expected = os.path.join(PROJECT_ROOT, "config", "chaves-api-openai.txt")
    if not os.path.exists(api_key_path_expected):
        print(f"ALERTA: O arquivo de chave da API OpenAI esperado em ({api_key_path_expected}) não foi encontrado.")
        print("A etapa de geração de textos provavelmente falhará.")
        print("Certifique-se de que o arquivo existe e contém sua chave da API OpenAI.")
        # Decide se quer parar ou continuar. Por ora, continua para permitir teste da parte de geração de thumbnail se o texto já existir.

    # Chama a função do script de geração de texto. 
    # Este script já foi atualizado para usar os caminhos corretos relativos a PROJECT_ROOT.
    sucesso_texto = openai_batch_script_v3.executar_geracao_textos()
    
    if sucesso_texto:
        print("\n--- Geração de Textos concluída com sucesso. ---")
    else:
        print("\n--- Geração de Textos falhou ou foi pulada. Verifique os logs acima. ---")
        # Verifica se o arquivo de texto existe na raiz para tentar prosseguir
        input_text_file = os.path.join(PROJECT_ROOT, "texto-thumbnails.txt")
        if os.path.exists(input_text_file):
             print(f"Tentando prosseguir para a geração de thumbnails com o arquivo existente: {input_text_file}")
        else:
             print(f"Arquivo {input_text_file} também não encontrado. Não é possível gerar thumbnails.")
             print("\n--- Sistema Unificado de Geração de Thumbnails Concluído (com falha) ---")
             exit() # Sai se não puder gerar texto nem encontrar arquivo existente

    # Etapa 2: Geração das Thumbnails
    print("\n--- Etapa 2: Gerando Thumbnails a partir dos Textos ---")
    # Chama a função do script de geração de thumbnails.
    # Este script também já foi atualizado para usar os caminhos corretos relativos a PROJECT_ROOT.
    gerador_thumbnails_lote_v2.main_batch()
    
    print("\n--- Sistema Unificado de Geração de Thumbnails Concluído ---")
