"""
Módulo para coleta ampla inicial de vídeos do YouTube.

Este módulo implementa estratégias para coletar um grande volume de vídeos (500-1000)
antes da filtragem final, com foco no nicho de histórias de vingança e vídeos longos.
"""

import os
import time
import logging
import gc
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional, Set

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("youtube_bulk_collector")

class YouTubeBulkCollector:
    """
    Classe para coleta ampla inicial de vídeos do YouTube.
    """
    
    def __init__(self, search_algorithm, channel_finder, pattern_extractor):
        """
        Inicializa o coletor de vídeos em massa.

        Args:
            search_algorithm: Algoritmo de busca do YouTube
            channel_finder: Buscador de canais semelhantes
            pattern_extractor: Extrator de padrões de títulos
            # output_dir: Diretório para salvar os resultados (Removido - saída gerenciada externamente)
        """
        self.search_algorithm = search_algorithm
        self.channel_finder = channel_finder
        self.pattern_extractor = pattern_extractor
        # self.output_dir = output_dir # Removido

        # Diretório de saída não é mais criado aqui # Removido
        # if not os.path.exists(output_dir):
        #     os.makedirs(output_dir)
        
        # Conjunto para rastrear vídeos já coletados (evitar duplicatas)
        self.collected_video_ids = set()
        
        # Parâmetros de coleta
        self.min_duration_minutes = 20  # Duração mínima em minutos
        self.max_age_months = 3  # Idade máxima em meses
        self.target_video_count = 1000  # Número alvo de vídeos a coletar
        
        logger.info(f"YouTubeBulkCollector inicializado com alvo de {self.target_video_count} vídeos")
    
    def _parse_duration(self, duration_str: str) -> int:
        """
        Converte a duração no formato ISO 8601 para minutos.
        
        Args:
            duration_str: String de duração no formato ISO 8601 (PT1H30M15S)
            
        Returns:
            Duração em minutos
        """
        if not duration_str or not isinstance(duration_str, str):
            return 0
        
        # Remover o prefixo PT
        duration = duration_str.replace("PT", "")
        
        # Inicializar variáveis
        hours = 0
        minutes = 0
        seconds = 0
        
        # Extrair horas, minutos e segundos
        if "H" in duration:
            hours_part = duration.split("H")[0]
            duration = duration.split("H")[1]
            try:
                hours = int(hours_part)
            except ValueError:
                hours = 0
        
        if "M" in duration:
            minutes_part = duration.split("M")[0]
            duration = duration.split("M")[1]
            try:
                minutes = int(minutes_part)
            except ValueError:
                minutes = 0
        
        if "S" in duration:
            seconds_part = duration.split("S")[0]
            try:
                seconds = int(seconds_part)
            except ValueError:
                seconds = 0
        
        # Calcular duração total em minutos
        total_minutes = hours * 60 + minutes + seconds / 60
        
        return total_minutes
    
    def _filter_by_duration(self, videos: List[Dict]) -> List[Dict]:
        """
        Filtra vídeos com base na duração mínima.
        
        Args:
            videos: Lista de vídeos a filtrar
            
        Returns:
            Lista de vídeos filtrados
        """
        filtered_videos = []
        
        for video in videos:
            # Verificar se o vídeo tem informações de contentDetails
            if 'contentDetails' in video:
                duration_str = video['contentDetails'].get('duration', '')
                duration_minutes = self._parse_duration(duration_str)
                
                # Filtrar por duração mínima
                if duration_minutes >= self.min_duration_minutes:
                    filtered_videos.append(video)
        
        logger.info(f"Filtro por duração: {len(filtered_videos)} de {len(videos)} vídeos têm pelo menos {self.min_duration_minutes} minutos")
        
        return filtered_videos
    
    def _filter_by_date(self, videos: List[Dict]) -> List[Dict]:
        """
        Filtra vídeos com base na data de publicação.
        
        Args:
            videos: Lista de vídeos a filtrar
            
        Returns:
            Lista de vídeos filtrados
        """
        filtered_videos = []
        
        # Calcular data limite
        cutoff_date = datetime.now() - timedelta(days=30 * self.max_age_months)
        cutoff_date_str = cutoff_date.isoformat("T") + "Z"
        
        for video in videos:
            # Verificar se o vídeo tem informações de snippet
            if 'snippet' in video:
                published_at = video['snippet'].get('publishedAt', '')
                
                # Filtrar por data de publicação
                if published_at and published_at >= cutoff_date_str:
                    filtered_videos.append(video)
        
        logger.info(f"Filtro por data: {len(filtered_videos)} de {len(videos)} vídeos foram publicados nos últimos {self.max_age_months} meses")
        
        return filtered_videos
    
    def collect_from_reference_channels(self, reference_channels: List[Dict], 
                                      max_videos_per_channel: int = 50) -> List[Dict]:
        """
        Coleta vídeos dos canais de referência.
        
        Args:
            reference_channels: Lista de canais de referência
            max_videos_per_channel: Número máximo de vídeos a coletar por canal
            
        Returns:
            Lista de vídeos coletados
        """
        all_videos = []
        
        logger.info(f"Coletando vídeos de {len(reference_channels)} canais de referência...")
        
        for channel in reference_channels:
            channel_id = channel['id']
            channel_title = channel['title']
            
            # Buscar vídeos recentes do canal
            videos = self.search_algorithm.get_channel_videos(
                channel_id, 
                max_results=max_videos_per_channel,
                published_after_days=self.max_age_months * 30
            )
            
            # Filtrar por duração
            videos = self._filter_by_duration(videos)
            
            # Marcar a fonte dos vídeos
            for video in videos:
                video['source'] = 'reference_channel'
                
                # Adicionar ao conjunto de IDs coletados
                self.collected_video_ids.add(video['id'])
            
            # Adicionar à lista total
            all_videos.extend(videos)
            
            logger.info(f"Coletados {len(videos)} vídeos do canal de referência {channel_title}")
            
            # Aguardar um pouco entre canais para evitar erros de rate limit
            time.sleep(0.5)
            
            # Forçar coleta de lixo após processar cada canal
            gc.collect()
        
        logger.info(f"Total de vídeos coletados dos canais de referência: {len(all_videos)}")
        
        return all_videos
    
    def collect_from_similar_channels(self, max_similar_channels: int = 30,
                                    max_videos_per_channel: int = 30) -> List[Dict]:
        """
        Coleta vídeos de canais semelhantes aos canais de referência.
        
        Args:
            max_similar_channels: Número máximo de canais semelhantes a buscar
            max_videos_per_channel: Número máximo de vídeos a coletar por canal
            
        Returns:
            Lista de vídeos coletados
        """
        all_videos = []
        
        # Buscar canais semelhantes
        similar_channels = self.channel_finder.find_similar_channels(max_channels=max_similar_channels)
        
        if not similar_channels:
            logger.warning("Nenhum canal semelhante encontrado")
            return []
        
        logger.info(f"Coletando vídeos de {len(similar_channels)} canais semelhantes...")
        
        for channel in similar_channels:
            channel_id = channel['id']
            channel_title = channel['title']
            
            # Buscar vídeos recentes do canal
            videos = self.search_algorithm.get_channel_videos(
                channel_id, 
                max_results=max_videos_per_channel,
                published_after_days=self.max_age_months * 30
            )
            
            # Filtrar por duração
            videos = self._filter_by_duration(videos)
            
            # Marcar a fonte dos vídeos
            for video in videos:
                video['source'] = 'similar_channel'
                
                # Adicionar ao conjunto de IDs coletados
                self.collected_video_ids.add(video['id'])
            
            # Adicionar à lista total
            all_videos.extend(videos)
            
            logger.info(f"Coletados {len(videos)} vídeos do canal semelhante {channel_title}")
            
            # Aguardar um pouco entre canais para evitar erros de rate limit
            time.sleep(0.5)
            
            # Forçar coleta de lixo após processar cada canal
            gc.collect()
        
        logger.info(f"Total de vídeos coletados dos canais semelhantes: {len(all_videos)}")
        
        return all_videos
    
    def collect_from_pattern_searches(self, max_results_per_query: int = 20) -> List[Dict]:
        """
        Coleta vídeos usando consultas baseadas em padrões extraídos.
        
        Args:
            max_results_per_query: Número máximo de resultados por consulta
            
        Returns:
            Lista de vídeos coletados
        """
        all_videos = []
        
        # Gerar consultas de busca
        search_queries = self.pattern_extractor.generate_search_queries(max_queries=50)
        
        logger.info(f"Coletando vídeos usando {len(search_queries)} consultas baseadas em padrões...")
        
        for query in search_queries:
            # Buscar vídeos usando o termo
            search_results = self.search_algorithm.search_videos(
                query,
                max_results=max_results_per_query,
                published_after_days=self.max_age_months * 30
            )
            
            # Extrair IDs dos vídeos
            video_ids = [item['id']['videoId'] for item in search_results]
            
            # Obter detalhes completos dos vídeos
            videos = self.search_algorithm.get_videos_details(video_ids)
            
            # Filtrar por duração
            videos = self._filter_by_duration(videos)
            
            # Marcar a fonte dos vídeos
            for video in videos:
                video['source'] = 'pattern_search'
                video['query'] = query  # Armazenar a consulta que encontrou este vídeo
                
                # Adicionar ao conjunto de IDs coletados se não for duplicata
                if video['id'] not in self.collected_video_ids:
                    self.collected_video_ids.add(video['id'])
                    all_videos.append(video)
            
            logger.info(f"Coletados {len(videos)} vídeos para a consulta '{query}'")
            
            # Verificar se já atingimos o número alvo
            if len(all_videos) + len(self.collected_video_ids) >= self.target_video_count:
                logger.info(f"Atingido número alvo de vídeos ({self.target_video_count}). Interrompendo coleta.")
                break
            
            # Aguardar um pouco entre buscas para evitar erros de rate limit
            time.sleep(0.5)
            
            # Forçar coleta de lixo após cada termo de busca
            gc.collect()
        
        logger.info(f"Total de vídeos coletados usando padrões: {len(all_videos)}")
        
        return all_videos
    
    def collect_from_related_videos(self, seed_videos: List[Dict], max_related_per_video: int = 10) -> List[Dict]:
        """
        Coleta vídeos relacionados a um conjunto de vídeos semente.
        
        Args:
            seed_videos: Lista de vídeos semente
            max_related_per_video: Número máximo de vídeos relacionados por vídeo semente
            
        Returns:
            Lista de vídeos coletados
        """
        all_related_videos = []
        
        # Limitar o número de vídeos semente para evitar excesso de buscas
        if len(seed_videos) > 20:
            selected_seeds = random.sample(seed_videos, 20)
        else:
            selected_seeds = seed_videos
        
        logger.info(f"Coletando vídeos relacionados a {len(selected_seeds)} vídeos semente...")
        
        for seed_video in selected_seeds:
            # Buscar vídeos relacionados
            related_videos = self.search_algorithm.search_related_videos(
                seed_video['id'],
                max_results=max_related_per_video
            )
            
            # Filtrar por duração
            related_videos = self._filter_by_duration(related_videos)
            
            # Marcar a fonte dos vídeos
            for video in related_videos:
                video['source'] = 'related'
                
                # Adicionar ao conjunto de IDs coletados se não for duplicata
                if video['id'] not in self.collected_video_ids:
                    self.collected_video_ids.add(video['id'])
                    all_related_videos.append(video)
            
            logger.info(f"Coletados {len(related_videos)} vídeos relacionados ao vídeo {seed_video['id']}")
            
            # Verificar se já atingimos o número alvo
            if len(all_related_videos) + len(self.collected_video_ids) >= self.target_video_count:
                logger.info(f"Atingido número alvo de vídeos ({self.target_video_count}). Interrompendo coleta.")
                break
            
            # Aguardar um pouco entre buscas para evitar erros de rate limit
            time.sleep(0.5)
            
            # Forçar coleta de lixo após cada vídeo semente
            gc.collect()
        
        logger.info(f"Total de vídeos relacionados coletados: {len(all_related_videos)}")
        
        return all_related_videos
    
    def collect_all_videos(self) -> List[Dict]:
        """
        Coleta vídeos de todas as fontes até atingir o número alvo.
        
        Returns:
            Lista de vídeos coletados
        """
        all_videos = []
        
        # 1. Coletar vídeos dos canais de referência
        reference_channels = self.channel_finder.reference_channels
        reference_videos = self.collect_from_reference_channels(reference_channels)
        all_videos.extend(reference_videos)
        logger.info(f"Após canais de referência: {len(all_videos)} vídeos coletados")
        
        # Verificar se já atingimos o número alvo
        if len(all_videos) >= self.target_video_count:
            logger.info(f"Atingido número alvo de vídeos ({self.target_video_count}). Interrompendo coleta.")
            return all_videos[:self.target_video_count]
        
        # 2. Coletar vídeos de canais semelhantes
        similar_videos = self.collect_from_similar_channels()
        all_videos.extend(similar_videos)
        logger.info(f"Após canais semelhantes: {len(all_videos)} vídeos coletados")
        
        # Verificar se já atingimos o número alvo
        if len(all_videos) >= self.target_video_count:
            logger.info(f"Atingido número alvo de vídeos ({self.target_video_count}). Interrompendo coleta.")
            return all_videos[:self.target_video_count]
        
        # 3. Coletar vídeos usando padrões extraídos
        pattern_videos = self.collect_from_pattern_searches()
        all_videos.extend(pattern_videos)
        logger.info(f"Após buscas por padrões: {len(all_videos)} vídeos coletados")
        
        # Verificar se já atingimos o número alvo
        if len(all_videos) >= self.target_video_count:
            logger.info(f"Atingido número alvo de vídeos ({self.target_video_count}). Interrompendo coleta.")
            return all_videos[:self.target_video_count]
        
        # 4. Coletar vídeos relacionados
        if all_videos:
            related_videos = self.collect_from_related_videos(all_videos)
            all_videos.extend(related_videos)
            logger.info(f"Após vídeos relacionados: {len(all_videos)} vídeos coletados")
        
        logger.info(f"Total de vídeos coletados de todas as fontes: {len(all_videos)}")
        
        # Limitar ao número alvo se exceder
        if len(all_videos) > self.target_video_count:
            all_videos = all_videos[:self.target_video_count]
        
        return all_videos
    
    def save_videos_to_csv(self, videos: List[Dict], output_path: str) -> None:
        """
        Salva vídeos em um arquivo CSV.
        
        Args:
            videos: Lista de vídeos a salvar
            output_path: Caminho do arquivo CSV de saída
        """
        import csv
        import os
        
        # Criar diretório se não existir
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            # Definir colunas
            fieldnames = [
                'video_id', 'title', 'channel_id', 'channel_title', 'published_at',
                'view_count', 'like_count', 'comment_count', 'duration', 'source'
            ]
            
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            # Processar em lotes para economizar memória
            batch_size = 50
            for i in range(0, len(videos), batch_size):
                batch = videos[i:i+batch_size]
                
                # Escrever dados
                for video in batch:
                    try:
                        # Extrair dados do vídeo
                        row = {
                            'video_id': video['id'],
                            'title': video['snippet']['title'],
                            'channel_id': video['snippet']['channelId'],
                            'channel_title': video['snippet']['channelTitle'],
                            'published_at': video['snippet']['publishedAt'],
                            'view_count': video['statistics'].get('viewCount', '0'),
                            'like_count': video['statistics'].get('likeCount', '0'),
                            'comment_count': video['statistics'].get('commentCount', '0'),
                            'duration': video['contentDetails'].get('duration', ''),
                            'source': video.get('source', 'unknown')
                        }
                        
                        writer.writerow(row)
                        
                    except Exception as e:
                        logger.error(f"Erro ao salvar vídeo no CSV: {str(e)}")
                
                # Forçar coleta de lixo após cada lote
                gc.collect()
        
        logger.info(f"Salvos {len(videos)} vídeos em {output_path}")
    
    def run(self, output_path: str) -> List[Dict]:
        """
        Executa o processo completo de coleta de vídeos.
        
        Args:
            output_path: Caminho do arquivo CSV de saída
            
        Returns:
            Lista de vídeos coletados
        """
        # Coletar vídeos de todas as fontes
        all_videos = self.collect_all_videos()
        
        # Salvar vídeos em CSV
        self.save_videos_to_csv(all_videos, output_path)
        
        return all_videos
