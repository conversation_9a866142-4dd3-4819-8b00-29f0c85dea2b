"""
Script de Diagnóstico para Erro 400 da ElevenLabs

Este script ajuda a identificar a causa do erro HTTP 400
testando diferentes parâmetros da API ElevenLabs.
"""

import requests
import json
import sys
from pathlib import Path

# Adiciona o diretório scripts ao path para importar módulos
SCRIPT_DIR = Path(__file__).resolve().parent
sys.path.append(str(SCRIPT_DIR))

from api_key_manager import APIKeyManager
from fix_logging import get_safe_logger

logger = get_safe_logger("DiagnosticoElevenLabs")

class ElevenLabsDiagnostic:
    """Classe para diagnosticar problemas com a API ElevenLabs."""
    
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.elevenlabs.io"
        
    def _get_headers(self, content_type="application/json"):
        """Retorna headers básicos para requisições."""
        return {
            "xi-api-key": self.api_key,
            "Content-Type": content_type,
            "Accept": "application/json"
        }
    
    def test_api_connection(self):
        """Testa conexão básica com a API."""
        try:
            logger.info("[TESTE] Testando conexão com API ElevenLabs...")
            response = requests.get(
                f"{self.base_url}/v1/user",
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                user_data = response.json()
                logger.info(f"[OK] Conexão bem-sucedida. Usuário: {user_data.get('subscription', {}).get('tier', 'N/A')}")
                return True
            else:
                logger.error(f"[ERRO] Falha na conexão: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"[ERRO] Exceção ao testar conexão: {e}")
            return False
    
    def list_available_voices(self):
        """Lista vozes disponíveis na conta."""
        try:
            logger.info("[TESTE] Buscando vozes disponíveis...")
            response = requests.get(
                f"{self.base_url}/v1/voices",
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                voices_data = response.json()
                voices = voices_data.get("voices", [])
                
                logger.info(f"[OK] Encontradas {len(voices)} vozes disponíveis:")
                
                valid_voices = []
                for voice in voices[:5]:  # Mostra apenas as primeiras 5
                    voice_id = voice.get("voice_id", "N/A")
                    voice_name = voice.get("name", "N/A")
                    category = voice.get("category", "N/A")
                    
                    logger.info(f"  - {voice_name} (ID: {voice_id}, Categoria: {category})")
                    valid_voices.append({
                        "id": voice_id,
                        "name": voice_name,
                        "category": category
                    })
                
                return valid_voices
            else:
                logger.error(f"[ERRO] Falha ao listar vozes: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"[ERRO] Exceção ao listar vozes: {e}")
            return []
    
    def test_voice_id(self, voice_id):
        """Testa se um voice_id específico é válido."""
        try:
            logger.info(f"[TESTE] Testando voice_id: {voice_id}")
            response = requests.get(
                f"{self.base_url}/v1/voices/{voice_id}",
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                voice_data = response.json()
                logger.info(f"[OK] Voice ID válido: {voice_data.get('name', 'N/A')}")
                return True
            else:
                logger.error(f"[ERRO] Voice ID inválido: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"[ERRO] Exceção ao testar voice_id: {e}")
            return False
    
    def test_tts_request(self, voice_id, text="Hello, this is a test.", model_id="eleven_monolingual_v1"):
        """Testa uma requisição TTS com parâmetros específicos."""
        try:
            logger.info(f"[TESTE] Testando TTS - Voice: {voice_id}, Model: {model_id}")
            
            # Testa diferentes formatos de output
            output_formats = [
                "mp3_44100_128",  # Original que estava falhando
                "mp3_22050_32",   # Formato mais básico
                "pcm_16000",      # PCM simples
                "mp3_44100"       # MP3 sem bitrate especificado
            ]
            
            for output_format in output_formats:
                logger.info(f"[TESTE] Testando formato: {output_format}")
                
                # Prepara payload
                payload = {
                    "text": text,
                    "model_id": model_id,
                    "voice_settings": {
                        "stability": 0.5,
                        "similarity_boost": 0.5
                    }
                }
                
                # Headers para TTS
                headers = self._get_headers()
                headers["Accept"] = "audio/mpeg"
                
                # Faz requisição
                response = requests.post(
                    f"{self.base_url}/v1/text-to-speech/{voice_id}",
                    headers=headers,
                    params={"output_format": output_format},
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    logger.info(f"[OK] TTS funcionou com formato: {output_format}")
                    logger.info(f"[INFO] Tamanho do áudio: {len(response.content)} bytes")
                    return True, output_format
                else:
                    logger.warning(f"[FALHA] Formato {output_format}: {response.status_code}")
                    try:
                        error_detail = response.json()
                        logger.warning(f"[DETALHE] {error_detail}")
                    except:
                        logger.warning(f"[DETALHE] {response.text[:200]}")
            
            logger.error("[ERRO] Todos os formatos de output falharam")
            return False, None
            
        except Exception as e:
            logger.error(f"[ERRO] Exceção no teste TTS: {e}")
            return False, None
    
    def test_different_models(self, voice_id, text="Test"):
        """Testa diferentes modelos de TTS."""
        models_to_test = [
            "eleven_monolingual_v1",
            "eleven_multilingual_v1", 
            "eleven_multilingual_v2",
            "eleven_turbo_v2"
        ]
        
        working_models = []
        
        for model in models_to_test:
            logger.info(f"[TESTE] Testando modelo: {model}")
            success, format_used = self.test_tts_request(voice_id, text, model)
            
            if success:
                working_models.append({
                    "model": model,
                    "format": format_used
                })
                logger.info(f"[OK] Modelo {model} funcionou!")
            else:
                logger.warning(f"[FALHA] Modelo {model} falhou")
        
        return working_models

def run_full_diagnostic():
    """Executa diagnóstico completo."""
    print("=" * 60)
    print("DIAGNÓSTICO ELEVENLABS API - ERRO 400")
    print("=" * 60)
    
    # Carrega chave API
    try:
        # Ajusta o caminho para encontrar o arquivo de chaves
        keys_file = Path(__file__).parent.parent.parent / "config" / "chaves-api-elevenlabs.txt"
        
        if not keys_file.exists():
            print(f"[ERRO] Arquivo de chaves não encontrado: {keys_file}")
            return
        
        api_key_manager = APIKeyManager(str(keys_file))
        
        if not api_key_manager.working_keys:
            print("[ERRO] Nenhuma chave API funcional encontrada")
            return
        
        # Usa primeira chave funcional
        first_key = list(api_key_manager.working_keys)[0]
        print(f"[INFO] Usando chave: {first_key[:8]}...")
        
    except Exception as e:
        print(f"[ERRO] Falha ao carregar chaves: {e}")
        return
    
    # Inicializa diagnóstico
    diagnostic = ElevenLabsDiagnostic(first_key)
    
    # 1. Testa conexão básica
    if not diagnostic.test_api_connection():
        print("[CRITICAL] Falha na conexão básica - verifique chave API")
        return
    
    # 2. Lista vozes disponíveis  
    available_voices = diagnostic.list_available_voices()
    if not available_voices:
        print("[CRITICAL] Não foi possível listar vozes")
        return
    
    # 3. Testa voice_id configurado
    problematic_voice_id = "KHCvMklQZZo0O30ERnVn"  # Do erro original
    print(f"\n[DIAGNÓSTICO] Testando voice_id problemático: {problematic_voice_id}")
    
    if not diagnostic.test_voice_id(problematic_voice_id):
        print(f"[PROBLEMA ENCONTRADO] Voice ID {problematic_voice_id} é INVÁLIDO!")
        print("\n[SOLUÇÃO] Use um dos voice_ids válidos listados acima")
        
        # Testa com voice_id válido
        if available_voices:
            test_voice = available_voices[0]
            print(f"\n[TESTE] Tentando com voice_id válido: {test_voice['id']}")
            
            working_models = diagnostic.test_different_models(test_voice['id'])
            
            if working_models:
                print(f"\n[SUCESSO] Configuração funcionando encontrada:")
                for config in working_models:
                    print(f"  - Modelo: {config['model']}")
                    print(f"  - Formato: {config['format']}")
                    print(f"  - Voice ID: {test_voice['id']}")
                    print(f"  - Voice Nome: {test_voice['name']}")
            else:
                print("[PROBLEMA] Nenhuma configuração funcionou")
    else:
        print(f"[OK] Voice ID {problematic_voice_id} é válido")
        working_models = diagnostic.test_different_models(problematic_voice_id)
        
        if working_models:
            print("[SUCESSO] Problema pode estar nos parâmetros de formato")
        else:
            print("[PROBLEMA] Voice ID válido mas TTS ainda falha")

if __name__ == "__main__":
    import logging
    
    # Configura logging básico
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    run_full_diagnostic()
