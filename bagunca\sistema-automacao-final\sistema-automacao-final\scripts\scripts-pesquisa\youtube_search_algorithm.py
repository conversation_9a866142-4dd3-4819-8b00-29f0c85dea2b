"""
Módulo para algoritmo de busca do YouTube com gerenciamento de memória otimizado.

Este módulo implementa um algoritmo aprimorado para busca de vídeos e canais no YouTube,
com foco em maximizar o número de resultados e aproveitar múltiplas chaves API,
enquanto gerencia eficientemente o uso de memória.
"""

import os
import time
import random
import logging
import gc
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional

import googleapiclient.discovery
import googleapiclient.errors

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("youtube_search_algorithm")

class YouTubeSearchAlgorithm:
    """
    Algoritmo aprimorado para busca no YouTube.
    """
    
    def __init__(self, api_keys: List[str]):
        """
        Inicializa o algoritmo de busca.
        
        Args:
            api_keys: Lista de chaves API do YouTube
        """
        self.api_keys = api_keys
        self.current_key_index = 0
        self.quota_exceeded_keys = set()
        self.youtube = self._initialize_youtube_client()
        self.search_quota_used = 0
        self.video_quota_used = 0
        self.channel_quota_used = 0
        self.max_quota_per_key = 9900  # Limite seguro para evitar exceder a quota diária de 10.000
        
        # Parâmetros de busca
        self.max_results_per_search = 50  # Máximo permitido pela API
        self.max_videos_per_channel = 15  # Número de vídeos a coletar por canal
        self.days_lookback = 90  # Buscar vídeos dos últimos 90 dias (3 meses)
        
        logger.info(f"Algoritmo de busca inicializado com {len(api_keys)} chaves API")
    
    def _initialize_youtube_client(self) -> Any:
        """
        Inicializa o cliente da API do YouTube.
        
        Returns:
            Cliente da API do YouTube
        """
        try:
            api_key = self.api_keys[self.current_key_index]
            youtube = googleapiclient.discovery.build(
                "youtube", "v3", developerKey=api_key, cache_discovery=False
            )
            return youtube
        except Exception as e:
            logger.error(f"Erro ao inicializar cliente da API do YouTube: {str(e)}")
            raise
    
    def rotate_api_key(self) -> str:
        """
        Rotaciona para a próxima chave API disponível.
        
        Returns:
            Nova chave API atual
        """
        # Marcar a chave atual como excedida
        self.quota_exceeded_keys.add(self.current_key_index)
        
        # Encontrar a próxima chave disponível
        available_keys = len(self.api_keys)
        for _ in range(available_keys):
            self.current_key_index = (self.current_key_index + 1) % available_keys
            if self.current_key_index not in self.quota_exceeded_keys:
                # Reinicializar o cliente da API com a nova chave
                try:
                    # Limpar o cliente anterior para liberar memória
                    if hasattr(self, 'youtube'):
                        del self.youtube
                        gc.collect()
                        
                    self.youtube = googleapiclient.discovery.build(
                        "youtube", "v3", 
                        developerKey=self.api_keys[self.current_key_index],
                        cache_discovery=False
                    )
                    # Resetar contadores de quota para a nova chave
                    self.search_quota_used = 0
                    self.video_quota_used = 0
                    self.channel_quota_used = 0
                    
                    logger.info(f"Rotacionado para chave API #{self.current_key_index+1}")
                    return self.api_keys[self.current_key_index]
                except Exception as e:
                    logger.error(f"Erro ao inicializar cliente com nova chave API: {str(e)}")
                    self.quota_exceeded_keys.add(self.current_key_index)
                    continue
        
        # Se todas as chaves estiverem excedidas, limpar e começar de novo
        logger.warning("Todas as chaves API foram excedidas. Resetando e tentando novamente.")
        self.quota_exceeded_keys.clear()
        self.current_key_index = 0
        
        # Limpar o cliente anterior para liberar memória
        if hasattr(self, 'youtube'):
            del self.youtube
            gc.collect()
            
        self.youtube = self._initialize_youtube_client()
        
        # Aguardar um pouco antes de continuar
        time.sleep(2)
        
        return self.api_keys[self.current_key_index]
    
    def _handle_api_error(self, error: Exception) -> bool:
        """
        Trata erros da API do YouTube, rotacionando a chave API quando necessário.
        
        Args:
            error: Exceção capturada
            
        Returns:
            bool: True se a operação deve ser repetida, False caso contrário
        """
        error_str = str(error)
        
        # Verificar se é erro de quota excedida
        if "quota" in error_str.lower() or "403" in error_str:
            logger.warning(f"Quota da API excedida ou erro 403. Rotacionando para próxima chave API...")
            
            # Rotacionar para próxima chave API
            self.rotate_api_key()
            
            # Aguardar um pouco antes de tentar novamente
            time.sleep(1)
            
            return True  # Repetir a operação
        
        # Outros erros
        logger.error(f"Erro na API do YouTube: {error_str}")
        return False  # Não repetir a operação
    
    def _update_quota_counts(self, operation_type: str, count: int = 1) -> bool:
        """
        Atualiza os contadores de quota e verifica se é necessário rotacionar a chave.
        
        Args:
            operation_type: Tipo de operação ('search', 'video', 'channel')
            count: Número de unidades de quota a adicionar
            
        Returns:
            bool: True se a chave foi rotacionada, False caso contrário
        """
        # Custos de quota por operação
        quota_costs = {
            'search': 100,  # search.list custa 100 unidades
            'video': 1,     # videos.list custa 1 unidade por vídeo
            'channel': 1    # channels.list custa 1 unidade por canal
        }
        
        # Calcular custo total
        cost = quota_costs.get(operation_type, 1) * count
        
        # Atualizar contador apropriado
        if operation_type == 'search':
            self.search_quota_used += cost
        elif operation_type == 'video':
            self.video_quota_used += cost
        elif operation_type == 'channel':
            self.channel_quota_used += cost
        
        # Verificar se atingiu o limite
        total_used = self.search_quota_used + self.video_quota_used + self.channel_quota_used
        if total_used >= self.max_quota_per_key:
            logger.info(f"Limite de quota atingido ({total_used}). Rotacionando chave API.")
            self.rotate_api_key()
            return True
        
        return False
    
    def search_videos(self, query: str, max_results: int = 50, 
                     published_after_days: int = 90) -> List[Dict]:
        """
        Busca vídeos no YouTube com base em uma consulta.
        
        Args:
            query: Termo de busca
            max_results: Número máximo de resultados a retornar
            published_after_days: Buscar vídeos publicados após X dias atrás
            
        Returns:
            Lista de vídeos encontrados
        """
        # Calcular data de publicação mínima
        published_after = datetime.now() - timedelta(days=published_after_days)
        published_after_rfc3339 = published_after.isoformat("T") + "Z"
        
        all_videos = []
        next_page_token = None
        
        # Limitar o número de páginas para evitar loops infinitos
        max_pages = min(3, (max_results // self.max_results_per_search) + 1)
        
        for page in range(max_pages):
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    # Verificar se é necessário rotacionar a chave antes da operação
                    self._update_quota_counts('search')
                    
                    # Preparar parâmetros de busca
                    search_params = {
                        "q": query,
                        "part": "id,snippet",
                        "maxResults": min(self.max_results_per_search, max_results - len(all_videos)),
                        "type": "video",
                        "order": "viewCount",  # Ordenar por contagem de visualizações
                        "publishedAfter": published_after_rfc3339,
                        "relevanceLanguage": "pt",  # Priorizar conteúdo em português
                        "safeSearch": "none",
                        "videoDuration": "long"  # Apenas vídeos longos (>20 minutos)
                    }
                    
                    # Adicionar token de página se disponível
                    if next_page_token:
                        search_params["pageToken"] = next_page_token
                    
                    # Executar busca
                    request = self.youtube.search().list(**search_params)
                    response = request.execute()
                    
                    # Extrair resultados
                    items = response.get('items', [])
                    all_videos.extend(items)
                    
                    # Obter token da próxima página
                    next_page_token = response.get('nextPageToken')
                    
                    # Verificar se atingimos o número máximo de resultados
                    if len(all_videos) >= max_results or not next_page_token:
                        break
                    
                    # Sucesso, sair do loop de tentativas
                    break
                    
                except Exception as e:
                    if attempt < max_attempts - 1 and self._handle_api_error(e):
                        continue  # Tentar novamente com nova chave API
                    else:
                        logger.error(f"Erro ao buscar vídeos para '{query}': {str(e)}")
                        break  # Desistir desta consulta
            
            # Verificar se atingimos o número máximo de resultados ou não há mais páginas
            if len(all_videos) >= max_results or not next_page_token:
                break
            
            # Forçar coleta de lixo para liberar memória
            gc.collect()
        
        logger.info(f"Busca por '{query}' retornou {len(all_videos)} vídeos")
        return all_videos[:max_results]
    
    def get_videos_details(self, video_ids: List[str]) -> List[Dict]:
        """
        Obtém detalhes completos de uma lista de vídeos.
        
        Args:
            video_ids: Lista de IDs de vídeos
            
        Returns:
            Lista de detalhes dos vídeos
        """
        if not video_ids:
            return []
        
        all_videos = []
        
        # Processar em lotes menores para evitar problemas de memória
        batch_size = 25  # Reduzido de 50 para 25
        
        for i in range(0, len(video_ids), batch_size):
            batch = video_ids[i:i+batch_size]
            
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    # Verificar se é necessário rotacionar a chave antes da operação
                    self._update_quota_counts('video', len(batch))
                    
                    # Buscar detalhes dos vídeos
                    request = self.youtube.videos().list(
                        part="snippet,statistics,contentDetails",
                        id=",".join(batch)
                    )
                    response = request.execute()
                    
                    # Adicionar resultados
                    all_videos.extend(response.get('items', []))
                    
                    # Sucesso, sair do loop de tentativas
                    break
                    
                except Exception as e:
                    if attempt < max_attempts - 1 and self._handle_api_error(e):
                        continue  # Tentar novamente com nova chave API
                    else:
                        logger.error(f"Erro ao obter detalhes dos vídeos: {str(e)}")
                        break  # Desistir deste lote
            
            # Aguardar um pouco entre lotes para evitar erros de rate limit
            time.sleep(0.5)
            
            # Forçar coleta de lixo para liberar memória
            gc.collect()
        
        logger.info(f"Obtidos detalhes para {len(all_videos)} vídeos de {len(video_ids)} solicitados")
        return all_videos
    
    def get_channel_videos(self, channel_id: str, max_results: int = 15, 
                          published_after_days: int = 90) -> List[Dict]:
        """
        Busca vídeos recentes de um canal específico.
        
        Args:
            channel_id: ID do canal no YouTube
            max_results: Número máximo de resultados a retornar
            published_after_days: Buscar vídeos publicados após X dias atrás
            
        Returns:
            Lista de vídeos encontrados
        """
        # Calcular data de publicação mínima
        published_after = datetime.now() - timedelta(days=published_after_days)
        published_after_rfc3339 = published_after.isoformat("T") + "Z"
        
        all_videos = []
        next_page_token = None
        
        # Limitar o número de páginas para evitar loops infinitos
        max_pages = min(2, (max_results // self.max_results_per_search) + 1)
        
        for page in range(max_pages):
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    # Verificar se é necessário rotacionar a chave antes da operação
                    self._update_quota_counts('search')
                    
                    # Buscar uploads do canal
                    search_params = {
                        "channelId": channel_id,
                        "part": "id,snippet",
                        "maxResults": min(self.max_results_per_search, max_results - len(all_videos)),
                        "order": "date",  # Ordenar por data (mais recentes primeiro)
                        "publishedAfter": published_after_rfc3339,
                        "type": "video",
                        "videoDuration": "long"  # Apenas vídeos longos (>20 minutos)
                    }
                    
                    # Adicionar token de página se disponível
                    if next_page_token:
                        search_params["pageToken"] = next_page_token
                    
                    # Executar busca
                    request = self.youtube.search().list(**search_params)
                    response = request.execute()
                    
                    # Extrair resultados
                    items = response.get('items', [])
                    all_videos.extend(items)
                    
                    # Obter token da próxima página
                    next_page_token = response.get('nextPageToken')
                    
                    # Verificar se atingimos o número máximo de resultados
                    if len(all_videos) >= max_results or not next_page_token:
                        break
                    
                    # Sucesso, sair do loop de tentativas
                    break
                    
                except Exception as e:
                    if attempt < max_attempts - 1 and self._handle_api_error(e):
                        continue  # Tentar novamente com nova chave API
                    else:
                        logger.error(f"Erro ao buscar vídeos do canal {channel_id}: {str(e)}")
                        break  # Desistir desta consulta
            
            # Verificar se atingimos o número máximo de resultados ou não há mais páginas
            if len(all_videos) >= max_results or not next_page_token:
                break
            
            # Forçar coleta de lixo para liberar memória
            gc.collect()
        
        # Extrair IDs dos vídeos
        video_ids = [item['id']['videoId'] for item in all_videos]
        
        # Obter detalhes completos dos vídeos
        videos = self.get_videos_details(video_ids)
        
        logger.info(f"Obtidos {len(videos)} vídeos do canal {channel_id}")
        return videos
    
    def get_channel_details(self, channel_ids: List[str]) -> List[Dict]:
        """
        Obtém detalhes de uma lista de canais.
        
        Args:
            channel_ids: Lista de IDs de canais
            
        Returns:
            Lista de detalhes dos canais
        """
        if not channel_ids:
            return []
        
        all_channels = []
        
        # Processar em lotes menores para evitar problemas de memória
        batch_size = 25  # Reduzido de 50 para 25
        
        for i in range(0, len(channel_ids), batch_size):
            batch = channel_ids[i:i+batch_size]
            
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    # Verificar se é necessário rotacionar a chave antes da operação
                    self._update_quota_counts('channel', len(batch))
                    
                    # Buscar detalhes dos canais
                    request = self.youtube.channels().list(
                        part="snippet,statistics,contentDetails",
                        id=",".join(batch)
                    )
                    response = request.execute()
                    
                    # Adicionar resultados
                    all_channels.extend(response.get('items', []))
                    
                    # Sucesso, sair do loop de tentativas
                    break
                    
                except Exception as e:
                    if attempt < max_attempts - 1 and self._handle_api_error(e):
                        continue  # Tentar novamente com nova chave API
                    else:
                        logger.error(f"Erro ao obter detalhes dos canais: {str(e)}")
                        break  # Desistir deste lote
            
            # Aguardar um pouco entre lotes para evitar erros de rate limit
            time.sleep(0.5)
            
            # Forçar coleta de lixo para liberar memória
            gc.collect()
        
        logger.info(f"Obtidos detalhes para {len(all_channels)} canais de {len(channel_ids)} solicitados")
        return all_channels
    
    def search_channels(self, query: str, max_results: int = 5) -> List[Dict]:
        """
        Busca canais no YouTube com base em uma consulta.
        
        Args:
            query: Termo de busca
            max_results: Número máximo de resultados a retornar
            
        Returns:
            Lista de canais encontrados
        """
        all_channels = []
        
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                # Verificar se é necessário rotacionar a chave antes da operação
                self._update_quota_counts('search')
                
                # Buscar canais
                request = self.youtube.search().list(
                    q=query,
                    part="snippet",
                    maxResults=max_results,
                    type="channel",
                    relevanceLanguage="pt"  # Priorizar conteúdo em português
                )
                response = request.execute()
                
                # Extrair resultados
                all_channels = response.get('items', [])
                
                # Sucesso, sair do loop de tentativas
                break
                
            except Exception as e:
                if attempt < max_attempts - 1 and self._handle_api_error(e):
                    continue  # Tentar novamente com nova chave API
                else:
                    logger.error(f"Erro ao buscar canais para '{query}': {str(e)}")
                    return []  # Retornar lista vazia em caso de erro
        
        logger.info(f"Busca por canais com '{query}' retornou {len(all_channels)} resultados")
        return all_channels[:max_results]
    
    def search_related_videos(self, video_id: str, max_results: int = 10) -> List[Dict]:
        """
        Busca vídeos relacionados a um vídeo específico.
        
        Args:
            video_id: ID do vídeo
            max_results: Número máximo de resultados a retornar
            
        Returns:
            Lista de vídeos relacionados
        """
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                # Verificar se é necessário rotacionar a chave antes da operação
                self._update_quota_counts('search')
                
                # Buscar vídeos relacionados
                request = self.youtube.search().list(
                    part="id,snippet",
                    relatedToVideoId=video_id,
                    type="video",
                    maxResults=max_results,
                    videoDuration="long"  # Apenas vídeos longos (>20 minutos)
                )
                response = request.execute()
                
                # Extrair IDs dos vídeos
                items = response.get('items', [])
                video_ids = [item['id']['videoId'] for item in items]
                
                # Obter detalhes completos dos vídeos
                videos = self.get_videos_details(video_ids)
                
                logger.info(f"Busca de vídeos relacionados a {video_id} retornou {len(videos)} vídeos")
                return videos
                
            except Exception as e:
                if attempt < max_attempts - 1 and self._handle_api_error(e):
                    continue  # Tentar novamente com nova chave API
                else:
                    logger.error(f"Erro ao buscar vídeos relacionados a {video_id}: {str(e)}")
                    return []
        
        return []
