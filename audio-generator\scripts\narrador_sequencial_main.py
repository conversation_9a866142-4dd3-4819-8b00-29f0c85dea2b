#!/usr/bin/env python3
"""
Narrador de Roteiros Sequencial - Versão 3.0
Sistema com esgotamento completo de chaves e pausas manuais para troca de IP

Principais funcionalidades:
- Esgota completamente uma chave antes de usar a próxima
- Pausa automaticamente quando chave atinge 200 caracteres restantes
- Solicita troca manual de IP entre chaves
- Retoma processamento de onde parou
- Salva progresso automaticamente
"""

import os
import sys
import logging
import argparse
import json
from pathlib import Path

# Adiciona o diretório do projeto ao PATH
SCRIPT_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = SCRIPT_DIR.parent  # audio-generator é a raiz agora
sys.path.insert(0, str(SCRIPT_DIR))  # Adiciona scripts ao PATH

# Importa os módulos do sistema
from config_manager import ConfigManager
from api_key_manager_sequential import SequentialAPIKeyManager
from elevenlabs_api_atualizado import ElevenLabsAPI
from processador_sequencial import ProcessadorArquivosSequencial

# --- Configuração Inicial ---
CONFIG_FILE_PATH = PROJECT_ROOT / "config" / "config_narracao.json"
config_manager = ConfigManager(str(CONFIG_FILE_PATH))

# --- Configuração de Logging ---
LOG_DIR = PROJECT_ROOT / "logs"
LOG_DIR.mkdir(parents=True, exist_ok=True)
log_file_name = config_manager.get_config("paths", "log_file") or "narracao_sequencial.log"
log_file_path = LOG_DIR / log_file_name

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("NarradorSequencial")

def clean_text(raw_text):
    """Limpa o texto removendo símbolos e linhas indesejadas."""
    import re
    cleaned_lines = []
    for line in raw_text.splitlines():
        if re.match(r"^CENA \d+:|^ATO \d+:|^\*\*.+\*\*", line.strip(), re.IGNORECASE):
            cleaned_lines.append(line.strip())
            continue
        if line.strip().startswith(("#", "-")) or re.match(r"^\d+\. ", line.strip()):
             continue
        line = line.replace("*", "")
        cleaned_lines.append(line.strip())
    return "\n".join(filter(None, cleaned_lines))

def show_welcome_banner():
    """Mostra banner de boas-vindas com informações do sistema."""
    print("\n" + "="*70)
    print("🎙️  NARRADOR DE ROTEIROS SEQUENCIAL V3.0")
    print("="*70)
    print("✨ Funcionalidades:")
    print("   • Esgota completamente cada chave API (10.000 caracteres)")
    print("   • Pausa automática aos 200 caracteres restantes")
    print("   • Solicita troca manual de IP entre chaves")
    print("   • Retoma processamento de onde parou")
    print("   • Salva progresso automaticamente")
    print("="*70)

def show_system_status(api_key_manager, diretorio_roteiros, diretorio_saida):
    """Mostra status atual do sistema."""
    status = api_key_manager.get_status_summary()
    
    print("\n📊 STATUS DO SISTEMA:")
    print(f"📁 Entrada: {diretorio_roteiros}")
    print(f"💾 Saída: {diretorio_saida}")
    print(f"🔑 Chave atual: {status['current_key'] or 'Nenhuma'}")
    print(f"📈 Caracteres restantes: {status['current_key_remaining']:,}")
    print(f"🔢 Chaves ativas: {status['active_keys_count']}")
    print(f"💰 Total estimado disponível: {status['estimated_chars_available']:,} caracteres")
    print(f"⚠️  Limite para pausa: {status['threshold']} caracteres")

def verificar_dependencias():
    """Verifica se todas as dependências estão disponíveis."""
    try:
        import requests
        import pathlib
        print("✅ Dependências básicas OK")
        return True
    except ImportError as e:
        print(f"❌ Dependência faltando: {e}")
        return False

def main():
    """Função principal do sistema sequencial."""
    
    show_welcome_banner()
    
    if not verificar_dependencias():
        return 1

    # --- Argument Parsing ---
    parser = argparse.ArgumentParser(
        description="Narrador Sequencial com pausas manuais para troca de IP",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:
  python narrador_sequencial.py                    # Usa configurações padrão
  python narrador_sequencial.py --reset-state     # Reseta estado e recomeça
  python narrador_sequencial.py --status-only     # Mostra apenas status
  python narrador_sequencial.py --concatenar      # Concatena arquivos processados
        """
    )
    
    diretorio_roteiros_default = PROJECT_ROOT / "roteiros_gerados"
    diretorio_saida_default = PROJECT_ROOT / "narracao_sequencial"
    arquivo_chaves_default = PROJECT_ROOT / "config" / "chaves-api-elevenlabs.txt"

    parser.add_argument(
        "--output",
        type=Path,
        default=diretorio_saida_default,
        help=f"Diretório de saída. Padrão: {diretorio_saida_default}"
    )
    parser.add_argument(
        "--keys",
        type=Path,
        default=arquivo_chaves_default,
        help=f"Arquivo de chaves API. Padrão: {arquivo_chaves_default}"
    )
    parser.add_argument(
        "--reset-state",
        action="store_true",
        help="Reseta o estado de processamento e recomeça do zero"
    )
    parser.add_argument(
        "--status-only",
        action="store_true",
        help="Mostra apenas o status atual e sai"
    )
    parser.add_argument(
        "--concatenar",
        action="store_true",
        help="Concatena apenas os arquivos já processados"
    )

    args = parser.parse_args()

    diretorio_roteiros = diretorio_roteiros_default
    diretorio_saida = args.output.resolve()
    arquivo_chaves = args.keys.resolve()

    diretorio_saida.mkdir(parents=True, exist_ok=True)

    logger.info(f"=== Iniciando Narrador Sequencial V3.0 ===")
    logger.info(f"Raiz do Projeto: {PROJECT_ROOT}")
    logger.info(f"Diretório de Roteiros: {diretorio_roteiros}")
    logger.info(f"Diretório de Saída: {diretorio_saida}")
    logger.info(f"Arquivo de Chaves: {arquivo_chaves}")

    # Verificações básicas
    if not arquivo_chaves.is_file():
        logger.error(f"Arquivo de chaves não encontrado: {arquivo_chaves}")
        print(f"❌ ERRO: Arquivo de chaves não encontrado: {arquivo_chaves}")
        return 1

    if not diretorio_roteiros.is_dir():
        logger.error(f"Diretório de roteiros não encontrado: {diretorio_roteiros}")
        print(f"❌ ERRO: Diretório de roteiros não encontrado: {diretorio_roteiros}")
        return 1

    try:
        # --- Inicialização dos Módulos ---
        logger.info("Inicializando gerenciador sequencial de chaves...")
        api_key_manager = SequentialAPIKeyManager(
            keys_file_path=str(arquivo_chaves),
            project_root=str(PROJECT_ROOT)
        )

        # Reset de estado se solicitado
        if args.reset_state:
            print("\n🔄 Resetando estado de processamento...")
            resposta = input("Tem certeza? (digite 'sim' para confirmar): ")
            if resposta.lower() == 'sim':
                api_key_manager.reset_processing_state()
                print("✅ Estado resetado com sucesso!")
            else:
                print("❌ Reset cancelado")
                return 0

        # Mostra status atual
        show_system_status(api_key_manager, diretorio_roteiros, diretorio_saida)

        # Se é apenas para mostrar status, sai aqui
        if args.status_only:
            print("\n✅ Status exibido. Use sem --status-only para processar arquivos.")
            return 0

        # Obtenção de configurações
        api_config = config_manager.get_config("api") or {}
        voice_config = config_manager.get_config("voice") or {}
        processing_config = config_manager.get_config("processing") or {}
        
        base_url = api_config.get("base_url", "https://api.elevenlabs.io")
        max_retries = api_config.get("max_retries", 3)
        retry_delay = api_config.get("retry_delay", 2)
        voice_id = voice_config.get("voice_id", "KHCvMklQZZo0O30ERnVn")
        model_id = voice_config.get("model_id", "eleven_multilingual_v2")
        output_format = voice_config.get("output_format", "mp3_44100_128")
        
        tamanho_minimo_bloco = processing_config.get("tamanho_minimo_bloco", 300)
        tamanho_maximo_bloco = processing_config.get("tamanho_maximo_bloco", 2300)
        min_block_fallback = processing_config.get("min_block_size_on_fallback", 50)

        print(f"\n🔧 CONFIGURAÇÃO:")
        print(f"   Voice ID: {voice_id}")
        print(f"   Model: {model_id}")
        print(f"   Bloco: {tamanho_minimo_bloco}-{tamanho_maximo_bloco} chars")

        logger.info("Inicializando cliente ElevenLabs...")
        elevenlabs_api = ElevenLabsAPI(
            api_key_manager=api_key_manager,
            base_url=base_url,
            model_id=model_id,
            voice_id=voice_id,
            output_format=output_format,
            max_retries=max_retries,
            retry_delay=retry_delay
        )

        logger.info("Inicializando processador sequencial...")
        processador = ProcessadorArquivosSequencial(
            diretorio_roteiros=diretorio_roteiros,
            diretorio_saida=diretorio_saida,
            tamanho_minimo_bloco=tamanho_minimo_bloco,
            tamanho_maximo_bloco=tamanho_maximo_bloco,
            api_key_manager=api_key_manager,
            elevenlabs_api=elevenlabs_api,
            modelo_id=model_id,
            min_block_size_on_fallback=min_block_fallback
        )

        # Se é apenas para concatenar
        if args.concatenar:
            print("\n🔗 Concatenando arquivos processados...")
            resultado_concat = processador.concatenar_arquivos_finais()
            if resultado_concat:
                print(f"✅ Concatenação concluída: {resultado_concat}")
            else:
                print("❌ Falha na concatenação ou nenhum arquivo para concatenar")
            return 0

        # --- Processamento Principal ---
        print(f"\n🚀 Iniciando processamento sequencial...")
        
        # Confirma início
        resposta = input("\nPressione ENTER para começar (ou 'q' para sair): ").strip()
        if resposta.lower() == 'q':
            print("Processamento cancelado pelo usuário.")
            return 0

        resultados = processador.processar_todos_arquivos()

        # --- Resumo Final ---
        sucessos = sum(1 for v in resultados.values() if isinstance(v, Path) and v.exists())
        erros = len(resultados) - sucessos

        print(f"\n{'='*60}")
        print(f"🏁 PROCESSAMENTO CONCLUÍDO")
        print(f"{'='*60}")
        print(f"📊 Total processado: {len(resultados)}")
        print(f"✅ Sucessos: {sucessos}")
        print(f"❌ Erros: {erros}")

        status_final = api_key_manager.get_status_summary()
        print(f"🔑 Chaves restantes: {status_final['active_keys_count']}")
        print(f"📈 Caracteres disponíveis: {status_final['estimated_chars_available']:,}")

        # Caminhos importantes
        relatorio_path = diretorio_saida / "relatorio_sequencial.txt"
        arquivos_finais = diretorio_saida / "completed_files"
        
        print(f"\n📁 ARQUIVOS GERADOS:")
        print(f"   Áudios individuais: {arquivos_finais}")
        print(f"   Relatório: {relatorio_path}")
        
        # Oferece concatenação
        if sucessos > 1:
            resposta = input(f"\nDeseja concatenar os {sucessos} arquivos em um único arquivo? (s/N): ")
            if resposta.lower() in ['s', 'sim', 'y', 'yes']:
                resultado_concat = processador.concatenar_arquivos_finais()
                if resultado_concat:
                    print(f"✅ Arquivo concatenado: {resultado_concat}")

        logger.info("Processamento sequencial finalizado")
        return 0

    except KeyboardInterrupt:
        print(f"\n⏸️  Processamento interrompido pelo usuário")
        print(f"💾 O progresso foi salvo. Execute novamente para continuar.")
        logger.info("Processamento interrompido pelo usuário")
        return 0
    except Exception as e:
        logger.exception("Erro fatal durante execução:")
        print(f"❌ ERRO FATAL: {str(e)}")
        print(f"📝 Verifique o log: {log_file_path}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
