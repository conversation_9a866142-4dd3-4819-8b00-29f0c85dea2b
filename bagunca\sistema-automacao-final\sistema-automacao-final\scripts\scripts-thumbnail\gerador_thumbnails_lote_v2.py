"""
Gerador de Thumbnails em Lote Multilíngue

Este script adapta o gerador de thumbnails original para processar múltiplos textos
de vários arquivos de idiomas (texto-thumbnails_XX.txt) e gerenciar um conjunto de imagens 
de personagem, movendo-as entre duas pastas (A e B) para garantir que todas sejam usadas 
antes de repetir. Utiliza um formato de arquivo de texto com marcadores explícitos 
[PRINCIPAL] e [FINAL]. Os caminhos são relativos à raiz do projeto.
"""

import os
import sys
import re
import glob
from PIL import Image, ImageDraw, ImageFont
import math
import shutil

# --- Constantes de Cor ---
COLOR_BG_MAIN = "black"
COLOR_BG_FINAL = "#ffe500"
COLOR_TEXT_MAIN_DEFAULT = "#ffffff"
COLOR_TEXT_MAIN_QUOTED = "#2bff00"
COLOR_TEXT_FINAL = "#ff0000"

# --- Marcadores Internos --- (Não devem aparecer no texto final)
MARKER_START = "@@START_GREEN@@"
MARKER_END = "@@END_GREEN@@"

# --- Dicionário de traduções das tags em diferentes idiomas ---
TAGS_TRADUCOES = {
    'PRINCIPAL': ['PRINCIPAL', 'MAIN', 'HAUPT', 'HOOFD', 'PRINCIPAL', 'PRINCIPALE', 'HOOFDGEBIED'],
    'FINAL': ['FINAL', 'END', 'ENDE', 'EINDE', 'FINAL', 'FINALE', 'EIND']
}

# --- Caminhos Relativos à Raiz do Projeto ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.abspath(os.path.join(SCRIPT_DIR, "..", ".."))

# Diretório de recursos
RECURSOS_DIR = os.path.join(PROJECT_ROOT, "recursos")

# Caminhos para imagens de personagem (mantendo a lógica A/B dentro de recursos/personagem)
PERSONAGEM_DIR = os.path.join(RECURSOS_DIR, "personagem")
PASTA_A = os.path.join(PERSONAGEM_DIR, "pasta_A")
PASTA_B = os.path.join(PERSONAGEM_DIR, "pasta_B")

# Caminhos para layout e fontes (dentro de recursos/thumbnail_layout)
THUMBNAIL_LAYOUT_DIR = os.path.join(RECURSOS_DIR, "thumbnail_layout")
LAYOUT_FILE = os.path.join(THUMBNAIL_LAYOUT_DIR, "layout-1-thumbnail.txt")
FONT_PATH = os.path.join(THUMBNAIL_LAYOUT_DIR, "impact.ttf")
FONT_PATH_HINDI = os.path.join(THUMBNAIL_LAYOUT_DIR, "noto-sans-devanagari.ttf") # Verificar se este nome está correto na nova estrutura
FONT_PATH_ROMENO = os.path.join(THUMBNAIL_LAYOUT_DIR, "dejavu-sans.ttf") # Verificar se este nome está correto na nova estrutura

# Caminho para o arquivo de texto de entrada (na raiz do projeto)
# O script busca por texto-thumbnails_*.txt, mas a estrutura define texto-thumbnails.txt
# Ajustando para buscar o arquivo definido na estrutura.
INPUT_TEXT_FILE = os.path.join(PROJECT_ROOT, "texto-thumbnails.txt")

# Caminho para a pasta de saída das thumbnails (na raiz do projeto)
OUTPUT_DIR = os.path.join(PROJECT_ROOT, "thumbnails")

def parse_layout(layout_file):
    layout = {}
    try:
        with open(layout_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                match_element = re.match(r'^(.*?):(.*)', line)
                if not match_element:
                    print(f"Aviso: Linha ignorada no layout (formato inválido): {line}")
                    continue
                element_name = match_element.group(1).strip()
                params_str = match_element.group(2).strip()
                params = {}
                for part in params_str.split():
                    # Ajustado regex para aceitar floats e ints
                    match_param = re.match(r'(\w+)=(\d*\.?\d+)px', part)
                    if match_param:
                        key = match_param.group(1)
                        # Usar float para precisão e depois arredondar/converter se necessário
                        value = float(match_param.group(2))
                        # Decidir se arredonda ou trunca baseado no uso
                        # Para coordenadas e dimensões, pode ser útil manter float ou arredondar
                        params[key] = int(math.ceil(value)) # Mantendo ceil como no original
                    else:
                        print(f"Aviso: Parâmetro ignorado no layout (formato inválido): {part} em {element_name}")
                if params:
                    layout[element_name] = params
                else:
                     print(f"Aviso: Elemento '{element_name}' sem parâmetros válidos no layout.")

    except FileNotFoundError:
        print(f"Erro Crítico: Arquivo de layout '{layout_file}' não encontrado.")
        return None
    except Exception as e:
        print(f"Erro crítico ao ler ou parsear o arquivo de layout '{layout_file}': {e}")
        return None

    # Verificação de elementos essenciais
    required_elements = [
        'background do texto principal', 'Caixa de texto principal',
        'background do texto final', 'Caixa de texto final',
        'personagem', 'tamanho total'
    ]
    missing_elements = [elem for elem in required_elements if elem not in layout]
    if missing_elements:
        print(f"Erro Crítico: O arquivo de layout '{layout_file}' não contém os seguintes elementos necessários: {', '.join(missing_elements)}")
        return None

    # Verificação de parâmetros essenciais dentro dos elementos
    required_params = {
        'background do texto principal': ['x', 'y', 'largura', 'altura'],
        'Caixa de texto principal': ['x', 'y', 'largura', 'altura'],
        'background do texto final': ['x', 'y', 'largura', 'altura'],
        'Caixa de texto final': ['x', 'y', 'largura', 'altura'],
        'personagem': ['x', 'y', 'largura', 'altura'],
        'tamanho total': ['largura', 'altura']
    }
    for elem, params_needed in required_params.items():
        if elem in layout:
            missing_params = [p for p in params_needed if p not in layout[elem]]
            if missing_params:
                print(f"Erro Crítico: Elemento '{elem}' no layout não contém os parâmetros necessários: {', '.join(missing_params)}")
                return None
        # A verificação de existência do elemento já foi feita acima

    return layout

def get_text_dimensions(text_string, font):
    # Tenta obter dimensões usando getbbox (preferencial)
    try:
        bbox = font.getbbox(text_string)
        width = bbox[2] - bbox[0]
        # Altura da linha pode ser estimada pelas métricas da fonte
        ascent, descent = font.getmetrics()
        line_height = ascent + descent
        # Adiciona uma pequena margem para segurança, se necessário
        # line_height += 2
        return width, line_height
    except AttributeError:
        # Fallback para getlength (menos preciso para altura)
        try:
            width = font.getlength(text_string)
            ascent, descent = font.getmetrics()
            height = ascent + descent
            return width, height
        except AttributeError:
            # Fallback final (estimativa grosseira)
            # print("Aviso: Usando estimativa grosseira para dimensões do texto.")
            # A estimativa original era muito dependente do tamanho, ajustando:
            avg_char_width = font.size * 0.6 # Média empírica
            line_height_est = font.size * 1.2 # Média empírica
            return len(text_string) * avg_char_width, line_height_est
    except Exception as e:
        # print(f"Erro inesperado em get_text_dimensions: {e}. Usando estimativa.")
        avg_char_width = font.size * 0.6
        line_height_est = font.size * 1.2
        return len(text_string) * avg_char_width, line_height_est

def process_special_text_v2(text):
    # Retorna string vazia se o texto for None, "NONE" (case-insensitive) ou vazio/espaços
    if text is None or text.strip().upper() == "NONE" or not text.strip():
        return ""

    # Função interna para marcar texto entre aspas
    def mark_quoted(match):
        # Pega o conteúdo dentro das aspas (grupo 1 ou 2 dependendo do regex)
        content = match.group(1) if match.group(1) is not None else match.group(2)
        # Se o conteúdo for None, "NONE" ou vazio, retorna string vazia
        if content is None or content.strip().upper() == "NONE" or not content.strip():
            return ""
        # Retorna o conteúdo marcado
        return f"{MARKER_START}{content}{MARKER_END}"

    # Aplica a marcação usando regex para encontrar texto entre aspas duplas
    # O regex (?:.|\n|\r)*? lida com múltiplas linhas e é não-guloso
    marked_text = re.sub(r'"((?:.|\n|\r)*?)"|"((?:.|\n|\r)*?)"', mark_quoted, text)

    # Converte todo o texto para maiúsculas APÓS a marcação
    marked_text_upper = marked_text.upper()

    # Remove marcadores vazios que podem ter sido gerados (ex: "" -> @@START_GREEN@@@@END_GREEN@@)
    marked_text_upper = marked_text_upper.replace(f"{MARKER_START}{MARKER_END}", "")

    return marked_text_upper

def find_optimal_font_v2(marked_text, font_path, max_width, max_height):
    font_size = 200 # Tamanho inicial grande
    min_font_size = 10 # Tamanho mínimo aceitável
    best_font = None
    best_lines_marked = []

    # Loop para encontrar o maior tamanho de fonte que cabe
    while font_size >= min_font_size:
        try:
            font = ImageFont.truetype(font_path, font_size)
        except IOError:
            # Fallback para fonte padrão se o arquivo não for encontrado ou for inválido
            print(f"Aviso: Fonte '{font_path}' não encontrada ou inválida. Tentando carregar fonte padrão.")
            try:
                # Tenta carregar a fonte padrão com o tamanho atual
                font = ImageFont.load_default(size=font_size)
            except Exception:
                # Fallback final para a fonte padrão no tamanho default dela
                try:
                    font = ImageFont.load_default()
                    print(f"Aviso: Usando fonte padrão em tamanho default.")
                    # Se a fonte padrão default for maior que o min_font_size, ajusta
                    if font.size > font_size:
                         font_size = font.size # Para evitar loop infinito se default > min
                except Exception:
                     # Erro crítico se nem a fonte padrão puder ser carregada
                    print("Erro crítico: Não foi possível carregar nenhuma fonte.")
                    # Retorna uma fonte mínima e texto vazio para evitar crash
                    return ImageFont.load_default(), [""]

        lines_marked = []
        current_line_marked = ""
        # Lida com texto nulo ou vazio
        if marked_text is None or not marked_text.strip():
            words_marked = []
        else:
            # Divide o texto em palavras (ou segmentos marcados)
            words_marked = marked_text.split()

        possible_fit = True
        # Calcula a altura de referência e espaçamento entre linhas
        _, line_height_ref = get_text_dimensions("Tg", font) # Usa 'Tg' para altura
        line_spacing = line_height_ref * 0.1 # 10% da altura da linha como espaçamento

        is_currently_highlighted = False # Flag para rastrear estado de marcação

        # Se não há palavras, define linha vazia e considera que cabe
        if not words_marked:
            if best_font is None: best_font = font # Guarda a fonte atual como melhor
            best_lines_marked = [""]
            # Não precisa continuar o loop interno, vai para o próximo tamanho de fonte

        # Itera sobre as palavras/segmentos
        for word_m_original in words_marked:
            word_m_processed = word_m_original
            # Adiciona marcador de início se a palavra anterior terminou destacada
            if is_currently_highlighted and MARKER_START not in word_m_processed:
                word_m_processed = MARKER_START + word_m_processed

            # Testa adicionar a palavra à linha atual
            test_line_m = current_line_marked + (" " if current_line_marked else "") + word_m_processed
            # Remove marcadores para calcular a largura visual
            test_line_display = test_line_m.replace(MARKER_START, "").replace(MARKER_END, "")
            line_w_display, _ = get_text_dimensions(test_line_display, font)

            # Se couber na largura máxima
            if line_w_display <= max_width:
                current_line_marked = test_line_m # Adiciona a palavra à linha
                # Atualiza o estado de destaque baseado nos marcadores da palavra
                if MARKER_START in word_m_original:
                    is_currently_highlighted = True
                if MARKER_END in word_m_original:
                    is_currently_highlighted = False
            # Se não couber
            else:
                # Finaliza a linha anterior
                line_to_add = current_line_marked
                # Garante que o marcador de fim seja adicionado se a linha terminar destacada
                if (MARKER_START in line_to_add and MARKER_END not in line_to_add) or \
                   (is_currently_highlighted and MARKER_END not in line_to_add and line_to_add.strip()):
                    line_to_add += MARKER_END

                # Adiciona a linha completa (se não vazia) à lista de linhas
                if line_to_add.strip():
                    lines_marked.append(line_to_add)

                # Começa uma nova linha com a palavra atual
                current_line_marked = word_m_original
                # Adiciona marcador de início se necessário (continuação de destaque)
                if is_currently_highlighted and MARKER_START not in current_line_marked:
                    current_line_marked = MARKER_START + current_line_marked

                # Atualiza estado de destaque baseado na palavra que iniciou a nova linha
                if MARKER_START in word_m_original:
                    is_currently_highlighted = True
                if MARKER_END in word_m_original:
                    is_currently_highlighted = False

                # Verifica se a própria palavra que não coube excede a largura máxima
                word_display_check = current_line_marked.replace(MARKER_START, "").replace(MARKER_END, "")
                word_w_display_check, _ = get_text_dimensions(word_display_check, font)
                if word_w_display_check > max_width:
                    possible_fit = False # Impossível encaixar com esta fonte
                    break # Sai do loop de palavras

        # Se uma palavra foi longa demais, tenta um tamanho de fonte menor
        if not possible_fit:
            font_size -= 2 # Reduz o tamanho da fonte
            continue # Próxima iteração do while

        # Adiciona a última linha que estava sendo construída
        if current_line_marked.strip():
            line_to_add = current_line_marked
            # Garante marcador de fim se necessário
            if is_currently_highlighted and MARKER_END not in line_to_add:
                line_to_add += MARKER_END
            lines_marked.append(line_to_add)
        # Se não havia palavras e nenhuma linha foi adicionada, garante uma linha vazia
        elif not lines_marked and not words_marked:
             lines_marked.append("")

        # Calcula a altura total do bloco de texto com o espaçamento
        actual_total_h = 0
        for idx, line_m_calc in enumerate(lines_marked):
            line_d_calc = line_m_calc.replace(MARKER_START, "").replace(MARKER_END, "")
            # Obtém altura da linha (mesmo se for só espaço)
            if not line_d_calc.strip() and line_m_calc.strip(): # Linha com marcadores mas sem texto visível
                 _, line_h_calc = get_text_dimensions(" ", font) # Usa espaço para altura
            elif not line_d_calc.strip(): # Linha completamente vazia
                line_h_calc = 0
            else:
                _, line_h_calc = get_text_dimensions(line_d_calc, font)

            # Soma a altura da linha e o espaçamento (exceto após a última linha)
            if line_h_calc > 0:
                actual_total_h += line_h_calc
                if idx < len(lines_marked) - 1:
                    actual_total_h += line_spacing

        # Verifica se a altura total calculada cabe na altura máxima
        if lines_marked and actual_total_h <= max_height:
            best_font = font # Encontrou a melhor fonte
            best_lines_marked = lines_marked
            return best_font, best_lines_marked # Retorna a fonte e as linhas

        # Se não coube na altura, tenta um tamanho de fonte menor
        font_size -= 2

    # Se o loop terminar (font_size < min_font_size) sem encontrar uma solução
    # Usa a última 'best_font' encontrada (ou a mínima testada) e recalcula as linhas
    if best_font is None:
        # Tenta criar a fonte com o tamanho mínimo
        try:
            best_font = ImageFont.truetype(font_path, min_font_size)
        except:
            best_font = ImageFont.load_default() # Fallback final
        print(f"Aviso: Texto não coube mesmo com fonte mínima ({min_font_size}). Usando fonte mínima e quebrando linhas como possível.")

    # Se best_lines_marked ainda estiver vazio (ex: texto vazio inicial)
    # ou se precisamos recalcular com a fonte mínima
    if not best_lines_marked:
        font_to_use = best_font
        lines_marked = []
        current_line_marked = ""
        if marked_text is None or not marked_text.strip():
            words_marked_fallback = []
        else:
            words_marked_fallback = marked_text.split()

        is_currently_highlighted_fallback = False
        if not words_marked_fallback:
            best_lines_marked = [""]
        else:
            # Repete a lógica de quebra de linha com a fonte mínima, ignorando altura
            for word_m_original_fb in words_marked_fallback:
                word_m_processed_fb = word_m_original_fb
                if is_currently_highlighted_fallback and MARKER_START not in word_m_processed_fb:
                    word_m_processed_fb = MARKER_START + word_m_processed_fb

                test_line_m_fb = current_line_marked + (" " if current_line_marked else "") + word_m_processed_fb
                test_line_display_fb = test_line_m_fb.replace(MARKER_START, "").replace(MARKER_END, "")
                line_w_display_fb, _ = get_text_dimensions(test_line_display_fb, font_to_use)

                # Quebra linha se exceder largura
                if line_w_display_fb <= max_width:
                    current_line_marked = test_line_m_fb
                    if MARKER_START in word_m_original_fb:
                        is_currently_highlighted_fallback = True
                    if MARKER_END in word_m_original_fb:
                        is_currently_highlighted_fallback = False
                else:
                    line_to_add_fb = current_line_marked
                    if (MARKER_START in line_to_add_fb and MARKER_END not in line_to_add_fb) or \
                       (is_currently_highlighted_fallback and MARKER_END not in line_to_add_fb and line_to_add_fb.strip()):
                        line_to_add_fb += MARKER_END
                    if line_to_add_fb.strip():
                        lines_marked.append(line_to_add_fb)

                    current_line_marked = word_m_original_fb
                    if is_currently_highlighted_fallback and MARKER_START not in current_line_marked:
                        current_line_marked = MARKER_START + current_line_marked
                    if MARKER_START in word_m_original_fb:
                        is_currently_highlighted_fallback = True
                    if MARKER_END in word_m_original_fb:
                        is_currently_highlighted_fallback = False
                    # Não verifica mais 'possible_fit', apenas quebra

            # Adiciona a última linha
            if current_line_marked.strip():
                line_to_add_fb = current_line_marked
                if is_currently_highlighted_fallback and MARKER_END not in line_to_add_fb:
                    line_to_add_fb += MARKER_END
                lines_marked.append(line_to_add_fb)
            elif not lines_marked and not words_marked_fallback:
                 lines_marked.append("") # Garante linha vazia para texto vazio

            # Retorna as linhas quebradas com a fonte mínima
            best_lines_marked = lines_marked if lines_marked else [marked_text if marked_text else ""] # Evita retornar lista vazia

    return best_font, best_lines_marked

def draw_text_logic_aligned_v2(draw, text_lines_marked, font, box_x, box_y, box_w, box_h,
                               default_fill_color, green_color=None):
    # Verifica se há algo para desenhar
    if not text_lines_marked or not font:
        return
    # Se a única linha está vazia, não desenha nada
    if len(text_lines_marked) == 1 and not text_lines_marked[0].strip():
        return

    # Calcula a altura total do bloco de texto e a largura máxima
    total_text_height_calculated = 0
    line_heights = []
    max_line_width_display = 0
    _, line_height_ref = get_text_dimensions("Tg", font)
    line_spacing = line_height_ref * 0.1 # Espaçamento entre linhas
    ascent, descent = font.getmetrics() # Métricas da fonte para posicionamento vertical

    for i, line_m in enumerate(text_lines_marked):
        line_d = line_m.replace(MARKER_START, "").replace(MARKER_END, "")
        line_w_d, line_h_d = get_text_dimensions(line_d if line_d.strip() else " ", font) # Usa espaço para altura de linhas vazias
        line_heights.append(line_h_d)
        total_text_height_calculated += line_h_d
        max_line_width_display = max(max_line_width_display, line_w_d)
        if i < len(text_lines_marked) - 1:
            total_text_height_calculated += line_spacing

    # Calcula a posição Y inicial para centralizar verticalmente o bloco de texto
    block_top_y = box_y + max(0, (box_h - total_text_height_calculated) / 2)
    # A posição Y para draw.text é a linha de base (baseline)
    current_y_baseline = block_top_y # Correção: Usar block_top_y diretamente para alinhar pelo topo com anchor='la'
    max_y_drawable = box_y + box_h # Limite inferior da caixa

    # Desenha cada linha
    for i, line_m in enumerate(text_lines_marked):
        # Verifica se a linha caberá verticalmente (considerando a descida dos caracteres)
        if current_y_baseline + descent > max_y_drawable and i > 0: # Permite que a primeira linha exceda um pouco se necessário
             print(f"Aviso: Texto cortado verticalmente na caixa. Linha: '{line_m[:30]}...' ")
             break # Para de desenhar se não couber mais

        line_d = line_m.replace(MARKER_START, "").replace(MARKER_END, "")
        line_w_d, _ = get_text_dimensions(line_d if line_d.strip() else " ", font)
        # Calcula a posição X inicial para centralizar horizontalmente
        start_x = box_x + (box_w - line_w_d) / 2

        # --- Desenho do Texto --- #
        # 1. Desenha a linha inteira com a cor padrão (preenche a área)
        # Usa anchor='la' (left, ascent) para alinhar pela linha de base esquerda
        draw.text((start_x, current_y_baseline), line_d, font=font, fill=default_fill_color, anchor='la', stroke_width=0)

        # 2. Se houver cor especial (verde) e marcadores na linha
        if green_color and (MARKER_START in line_m or MARKER_END in line_m):
            current_segment_x = start_x # Começa no X da linha
            is_green = False
            # Divide a linha pelos marcadores, mantendo os marcadores
            segments = re.split(f'({MARKER_START}|{MARKER_END})', line_m)

            for segment in segments:
                if not segment: # Ignora segmentos vazios resultantes do split
                    continue

                if segment == MARKER_START:
                    is_green = True # Ativa a cor verde
                elif segment == MARKER_END:
                    is_green = False # Desativa a cor verde
                else:
                    # Se o segmento atual deve ser verde e não é só espaço em branco
                    if is_green and segment.strip():
                        # Desenha APENAS este segmento com a cor verde por cima
                        draw.text((current_segment_x, current_y_baseline), segment, font=font, fill=green_color, anchor='la', stroke_width=0)

                    # Calcula a largura do segmento atual para avançar o X
                    segment_w, _ = get_text_dimensions(segment, font)
                    current_segment_x += segment_w

        # Move a posição Y da linha de base para a próxima linha
        current_y_baseline += line_heights[i] + line_spacing

def create_thumbnail(image_path, raw_main_text, raw_final_text, font_path_principal, font_path_final, layout, output_path):
    # Processa os textos (maiúsculas, marcação)
    processed_main_text = process_special_text_v2(raw_main_text)
    processed_final_text = process_special_text_v2(raw_final_text)

    # Obtém dimensões totais do layout
    total_w = layout['tamanho total']['largura']
    total_h = layout['tamanho total']['altura']

    # Cria a imagem base da thumbnail (fundo branco inicial)
    thumbnail = Image.new('RGB', (total_w, total_h), color='white')
    draw = ImageDraw.Draw(thumbnail)

    # --- Desenha a Imagem de Personagem --- #
    try:
        original_img = Image.open(image_path).convert("RGBA") # Abre com canal alfa
        orig_w, orig_h = original_img.size

        # Área alvo para a imagem no layout
        target_img_area = layout['personagem']
        target_w, target_h = target_img_area['largura'], target_img_area['altura']
        target_x, target_y = target_img_area['x'], target_img_area['y']

        # Calcula a escala para preencher a área alvo (cover)
        scale = max(target_w / orig_w, target_h / orig_h)
        scaled_w, scaled_h = int(orig_w * scale), int(orig_h * scale)
        resized_img = original_img.resize((scaled_w, scaled_h), Image.Resampling.LANCZOS)

        # Calcula o ponto de corte para centralizar a imagem redimensionada na área alvo
        crop_x = (scaled_w - target_w) / 2
        crop_y = (scaled_h - target_h) / 2
        # Define a caixa de corte (left, top, right, bottom)
        crop_box = (int(crop_x), int(crop_y), int(crop_x + target_w), int(crop_y + target_h))
        cropped_img = resized_img.crop(crop_box)

        # Cola a imagem cortada na posição alvo da thumbnail
        # Usa a própria imagem como máscara se for RGBA para preservar transparência
        thumbnail.paste(cropped_img, (target_x, target_y), mask=cropped_img if cropped_img.mode == 'RGBA' else None)

    except FileNotFoundError:
        print(f"Erro: Imagem de personagem não encontrada em '{image_path}'. Thumbnail gerada sem personagem.")
    except Exception as e:
        print(f"Erro ao processar imagem de personagem '{image_path}': {e}. Thumbnail gerada sem personagem.")

    # --- Desenha Fundos dos Textos --- #
    # Fundo Principal (Preto)
    bg_main_coords = layout['background do texto principal']
    draw.rectangle(
        [bg_main_coords['x'], bg_main_coords['y'],
         bg_main_coords['x'] + bg_main_coords['largura'], bg_main_coords['y'] + bg_main_coords['altura']],
        fill=COLOR_BG_MAIN
    )
    # Fundo Final (Amarelo)
    bg_final_coords = layout['background do texto final']
    draw.rectangle(
        [bg_final_coords['x'], bg_final_coords['y'],
         bg_final_coords['x'] + bg_final_coords['largura'], bg_final_coords['y'] + bg_final_coords['altura']],
        fill=COLOR_BG_FINAL
    )

    # --- Prepara e Desenha Texto Principal --- #
    if processed_main_text:
        box_main = layout['Caixa de texto principal']
        font_main, lines_main = find_optimal_font_v2(
            processed_main_text, font_path_principal, box_main['largura'], box_main['altura']
        )
        draw_text_logic_aligned_v2(
            draw, lines_main, font_main,
            box_main['x'], box_main['y'], box_main['largura'], box_main['altura'],
            COLOR_TEXT_MAIN_DEFAULT, COLOR_TEXT_MAIN_QUOTED
        )

    # --- Prepara e Desenha Texto Final --- #
    if processed_final_text:
        box_final = layout['Caixa de texto final']
        # Nota: Usando a mesma fonte principal para o texto final, como no original.
        # Se precisar de fonte diferente, passe font_path_final aqui.
        font_final, lines_final = find_optimal_font_v2(
            processed_final_text, font_path_principal, box_final['largura'], box_final['altura'] # Usando font_path_principal
        )
        draw_text_logic_aligned_v2(
            draw, lines_final, font_final,
            box_final['x'], box_final['y'], box_final['largura'], box_final['altura'],
            COLOR_TEXT_FINAL # Cor vermelha para o texto final
            # Sem cor especial (verde) para o texto final
        )

    # --- Salva a Thumbnail --- #
    try:
        # Garante que o diretório de saída exista
        output_dir_final = os.path.dirname(output_path)
        if not os.path.exists(output_dir_final):
            os.makedirs(output_dir_final)
            print(f"Diretório de saída criado: {output_dir_final}")
        thumbnail.save(output_path, quality=95) # Salva com alta qualidade
        print(f"Thumbnail salva em: {output_path}")
    except Exception as e:
        print(f"Erro ao salvar a thumbnail em '{output_path}': {e}")

def get_next_image(pasta_a, pasta_b):
    # Garante que as pastas existam
    os.makedirs(pasta_a, exist_ok=True)
    os.makedirs(pasta_b, exist_ok=True)

    images_a = glob.glob(os.path.join(pasta_a, "*.png")) + glob.glob(os.path.join(pasta_a, "*.jpg"))
    images_b = glob.glob(os.path.join(pasta_b, "*.png")) + glob.glob(os.path.join(pasta_b, "*.jpg"))

    # Se A tem imagens, usa a primeira e move para B
    if images_a:
        image_path = images_a[0]
        filename = os.path.basename(image_path)
        destination_path = os.path.join(pasta_b, filename)
        try:
            shutil.move(image_path, destination_path)
            print(f"Imagem movida de A para B: {filename}")
            return destination_path # Retorna o novo caminho em B
        except Exception as e:
            print(f"Erro ao mover imagem {filename} de A para B: {e}")
            # Se falhar ao mover, tenta usar a imagem de A mesmo assim
            return image_path
    # Se A está vazia mas B tem imagens, move todas de B para A, usa a primeira e move de volta para B
    elif images_b:
        print("Pasta A vazia. Movendo imagens de B para A.")
        moved_count = 0
        for img_b_path in images_b:
            filename = os.path.basename(img_b_path)
            dest_a_path = os.path.join(pasta_a, filename)
            try:
                shutil.move(img_b_path, dest_a_path)
                moved_count += 1
            except Exception as e:
                print(f"Erro ao mover imagem {filename} de B para A: {e}")

        if moved_count == 0:
            print("Erro: Nenhuma imagem pôde ser movida de B para A. Nenhuma imagem disponível.")
            return None

        # Pega a primeira imagem agora em A
        images_a_refreshed = glob.glob(os.path.join(pasta_a, "*.png")) + glob.glob(os.path.join(pasta_a, "*.jpg"))
        if not images_a_refreshed:
             print("Erro: Nenhuma imagem encontrada em A após mover de B.")
             return None

        image_path = images_a_refreshed[0]
        filename = os.path.basename(image_path)
        destination_path = os.path.join(pasta_b, filename) # Destino final é B
        try:
            shutil.move(image_path, destination_path)
            print(f"Imagem movida de B para A, e agora de A para B: {filename}")
            return destination_path # Retorna o caminho final em B
        except Exception as e:
            print(f"Erro ao mover imagem {filename} de A para B (após ciclo B->A): {e}")
            # Se falhar ao mover de volta, retorna o caminho em A onde ela ficou
            return image_path
    # Se ambas as pastas estão vazias
    else:
        print("Erro: Pastas A e B estão vazias. Nenhuma imagem de personagem disponível.")
        return None

def parse_thumbnail_text_file(filepath):
    entries = []
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"Erro: Arquivo de texto para thumbnails não encontrado em '{filepath}'")
        return []
    except Exception as e:
        print(f"Erro ao ler o arquivo de texto '{filepath}': {e}")
        return []

    # Divide o conteúdo em blocos separados por "---"
    blocks = content.strip().split('\n---\n')

    for i, block in enumerate(blocks):
        if not block.strip():
            continue

        main_text = ""
        final_text = ""
        in_main = False
        in_final = False

        lines = block.strip().split('\n')
        current_text_lines = []

        for line in lines:
            line_upper = line.strip().upper()
            is_tag = False

            # Verifica se a linha é uma tag [PRINCIPAL] ou suas traduções
            for tag in TAGS_TRADUCOES['PRINCIPAL']:
                if line_upper == f"[{tag}]".upper():
                    # Salva o texto acumulado anterior (se houver)
                    if in_main:
                        main_text = "\n".join(current_text_lines).strip()
                    elif in_final:
                        final_text = "\n".join(current_text_lines).strip()
                    # Inicia seção PRINCIPAL
                    in_main = True
                    in_final = False
                    current_text_lines = []
                    is_tag = True
                    break
            if is_tag: continue

            # Verifica se a linha é uma tag [FINAL] ou suas traduções
            for tag in TAGS_TRADUCOES['FINAL']:
                if line_upper == f"[{tag}]".upper():
                    # Salva o texto acumulado anterior (se houver)
                    if in_main:
                        main_text = "\n".join(current_text_lines).strip()
                    elif in_final:
                        final_text = "\n".join(current_text_lines).strip()
                    # Inicia seção FINAL
                    in_main = False
                    in_final = True
                    current_text_lines = []
                    is_tag = True
                    break
            if is_tag: continue

            # Se não for uma tag, adiciona a linha ao texto atual
            if in_main or in_final:
                current_text_lines.append(line) # Mantém a linha original, não a versão .strip()

        # Salva o último bloco de texto acumulado
        if in_main:
            main_text = "\n".join(current_text_lines).strip()
        elif in_final:
            final_text = "\n".join(current_text_lines).strip()

        # Adiciona a entrada se pelo menos um texto foi encontrado
        if main_text or final_text:
            entries.append({"main": main_text, "final": final_text, "id": i + 1})
        else:
            print(f"Aviso: Bloco {i+1} no arquivo '{filepath}' não continha tags [PRINCIPAL] ou [FINAL] válidas ou estava vazio.")

    return entries

def main_batch():
    print("--- Iniciando Geração de Thumbnails em Lote ---")

    # 1. Carrega o Layout
    layout = parse_layout(LAYOUT_FILE)
    if not layout:
        print("Erro crítico: Falha ao carregar o layout. Encerrando.")
        return
    print(f"Layout carregado de: {LAYOUT_FILE}")

    # 2. Verifica Fontes Principais (outras são verificadas depois)
    if not os.path.exists(FONT_PATH):
        print(f"Aviso: Fonte principal '{FONT_PATH}' não encontrada. O script tentará usar a fonte padrão.")
        # Não encerra, find_optimal_font_v2 tem fallback

    # 3. Garante que as pastas de personagem A e B existem
    try:
        os.makedirs(PASTA_A, exist_ok=True)
        os.makedirs(PASTA_B, exist_ok=True)
    except OSError as e:
        print(f"Erro crítico ao criar pastas de personagem A ({PASTA_A}) ou B ({PASTA_B}): {e}. Encerrando.")
        return

    # 4. Garante que a pasta de saída exista
    try:
        os.makedirs(OUTPUT_DIR, exist_ok=True)
    except OSError as e:
        print(f"Erro crítico ao criar pasta de saída ({OUTPUT_DIR}): {e}. Encerrando.")
        return
    print(f"Diretório de saída: {OUTPUT_DIR}")

    # 5. Encontra e processa o arquivo de texto de entrada
    # Removido o glob, usando o caminho direto definido
    if not os.path.exists(INPUT_TEXT_FILE):
        print(f"Erro crítico: Arquivo de texto de entrada '{INPUT_TEXT_FILE}' não encontrado. Encerrando.")
        return

    print(f"Processando arquivo de texto: {INPUT_TEXT_FILE}")
    text_entries = parse_thumbnail_text_file(INPUT_TEXT_FILE)

    if not text_entries:
        print("Nenhuma entrada de texto válida encontrada no arquivo. Encerrando.")
        return

    print(f"Encontradas {len(text_entries)} entradas de texto para gerar thumbnails.")

    # 6. Processa cada entrada de texto
    for i, entry in enumerate(text_entries):
        print(f"\n--- Processando Thumbnail {i+1}/{len(text_entries)} (ID Bloco: {entry['id']}) ---")

        # Obtém a próxima imagem de personagem (ciclo A <-> B)
        image_path = get_next_image(PASTA_A, PASTA_B)
        if not image_path:
            print("AVISO: Não foi possível obter imagem de personagem. Pulando thumbnail.")
            continue # Pula para a próxima entrada de texto
        print(f"Usando imagem: {os.path.basename(image_path)}")

        # Define o caminho de saída para a thumbnail atual
        # Usando um nome de arquivo numérico sequencial (001, 002, ...)
        output_filename = f"thumbnail_{i+1:03d}.jpg"
        output_path = os.path.join(OUTPUT_DIR, output_filename)

        # Determina qual fonte usar (Hindi, Romeno ou Padrão)
        # Simplificado: Assume padrão a menos que precise de outra.
        # A lógica original de detectar idioma foi removida, pois o texto já vem processado.
        # Se precisar de fontes específicas por idioma, a lógica precisaria ser reintroduzida
        # baseada talvez no nome do arquivo original (se usasse _XX) ou metadados.
        # Por ora, usa FONT_PATH para ambos os textos.
        current_font_path_main = FONT_PATH
        current_font_path_final = FONT_PATH # Usando a mesma para o final

        # Cria a thumbnail
        create_thumbnail(
            image_path,
            entry["main"],
            entry["final"],
            current_font_path_main, # Fonte para texto principal
            current_font_path_final, # Fonte para texto final (mesma neste caso)
            layout,
            output_path
        )

    print("\n--- Geração de Thumbnails em Lote Concluída ---")

# Ponto de entrada principal
if __name__ == "__main__":
    main_batch()
