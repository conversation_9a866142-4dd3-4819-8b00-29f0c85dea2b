"""
Configurador para sistema principal SEMPRE usar proxy
"""

import json
from pathlib import Path
import shutil
import sys

def main():
    print("🔧 CONFIGURANDO SISTEMA PRINCIPAL PARA USAR SEMPRE PROXY")
    print("="*65)
    
    try:
        # <PERSON>inhos
        project_root = Path(__file__).parent.parent.parent
        config_dir = project_root / "config"
        scripts_dir = project_root / "scripts" / "scripts-narracao"
        
        config_dir.mkdir(parents=True, exist_ok=True)
        
        # 1. Cria config.json otimizado para proxy
        print("1️⃣ Criando config.json otimizado...")
        
        config_with_proxy = {
            "api": {
                "base_url": "https://api.elevenlabs.io",
                "max_retries": 3,
                "retry_delay": 2
            },
            "voice": {
                "voice_id": "9BWtsMINqrJLrRacOk9x",  # Aria - validada
                "voice_name": "Aria",
                "model_id": "eleven_multilingual_v2",
                "output_format": "mp3_44100_128"
                # SEM language_code - causa erro 400
            },
            "processing": {
                "max_block_size": 2500,
                "respect_punctuation": True,
                "default_language": "pt",
                "tamanho_minimo_bloco": 900,
                "tamanho_maximo_bloco": 1100,
                "min_block_size_on_fallback": 50
            },
            "paths": {
                "output_directory": "audios_gerados",
                "log_file": "narracao_v4_proxy.log",
                "usage_data_file": "api_keys_usage.json"
            },
            "proxy": {
                "enabled": True,  # SEMPRE habilitado
                "webshare_token": "mt643rsq556yqggigaa3emmmjmdycc2h6868bi7b",
                "use_rotating_endpoint": True,
                "enable_fallback": True,
                "country_specific": None,  # Rotação automática
                "request_timeout": 30,
                "mandatory": True  # NOVO: proxy obrigatório
            }
        }
        
        config_path = config_dir / "config_narracao.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_with_proxy, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ Config salvo: {config_path}")
        
        # 2. Cria arquivo de chaves funcionais (template)
        print("2️⃣ Preparando arquivo de chaves...")
        
        keys_template_path = config_dir / "chaves-api-elevenlabs-template.txt"
        with open(keys_template_path, 'w') as f:
            f.write("# Arquivo de chaves da ElevenLabs\n")
            f.write("# IMPORTANTE: Use apenas chaves testadas COM proxy!\n")
            f.write("# Uma chave por linha, sem comentários na mesma linha\n")
            f.write("#\n")
            f.write("# Exemplo:\n")
            f.write("# sk_1234567890abcdef1234567890abcdef12345678\n")
            f.write("# sk_abcdef1234567890abcdef1234567890abcdef12\n")
            f.write("#\n")
            f.write("# Cole suas chaves funcionais aqui:\n")
        
        print(f"   ✅ Template de chaves: {keys_template_path}")
        
        # 3. Verifica se proxy_manager.py existe
        print("3️⃣ Verificando módulos necessários...")
        
        proxy_manager_path = scripts_dir / "proxy_manager.py"
        if proxy_manager_path.exists():
            print(f"   ✅ proxy_manager.py encontrado")
        else:
            print(f"   ❌ proxy_manager.py NÃO encontrado em {scripts_dir}")
            print(f"      💡 Você precisa copiar o arquivo proxy_manager.py")
        
        # 4. Cria script de execução otimizado
        print("4️⃣ Criando script de execução...")
        
        run_script_content = '''#!/usr/bin/env python3
"""
Script de execução do sistema de narração COM PROXY OBRIGATÓRIO
"""

import sys
from pathlib import Path

# Adiciona diretório dos scripts ao path
scripts_dir = Path(__file__).parent / "scripts" / "scripts-narracao"
sys.path.insert(0, str(scripts_dir))

# Importa e executa o script principal
try:
    from narrador_roteiros_final import main
    
    # Força uso de proxy
    sys.argv.extend([
        # "--proxy-token", "mt643rsq556yqggigaa3emmmjmdycc2h6868bi7b",  # Já no config
        # "--no-proxy",  # NUNCA usar esta opção
    ])
    
    print("🚀 EXECUTANDO SISTEMA COM PROXY OBRIGATÓRIO")
    print("⚠️  IMPORTANTE: Proxy está sempre habilitado!")
    print()
    
    sys.exit(main())
    
except ImportError as e:
    print(f"❌ Erro ao importar módulo: {e}")
    print("💡 Verifique se todos os arquivos estão no local correto:")
    print(f"   - proxy_manager.py")
    print(f"   - elevenlabs_api_atualizado.py (versão V4)")
    print(f"   - narrador_roteiros_final.py")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ Erro na execução: {e}")
    sys.exit(1)
'''
        
        run_script_path = project_root / "run_with_proxy.py"
        with open(run_script_path, 'w', encoding='utf-8') as f:
            f.write(run_script_content)
        
        print(f"   ✅ Script de execução: {run_script_path}")
        
        # 5. Instruções de uso
        print(f"\n5️⃣ INSTRUÇÕES DE USO:")
        print(f"{'='*50}")
        
        print(f"\n📋 CHECKLIST ANTES DE EXECUTAR:")
        print(f"   □ Criar/testar chaves novas COM proxy")
        print(f"   □ Adicionar chaves funcionais ao arquivo de chaves")
        print(f"   □ Verificar se todos os módulos estão instalados")
        print(f"   □ Ter roteiros na pasta roteiros_gerados/")
        
        print(f"\n🧪 PARA TESTAR CHAVES NOVAS:")
        print(f"   python {scripts_dir}/new_account_tester.py")
        
        print(f"\n🚀 PARA EXECUTAR O SISTEMA:")
        print(f"   python run_with_proxy.py")
        print(f"   # OU")
        print(f"   python scripts/scripts-narracao/narrador_roteiros_final.py")
        
        print(f"\n📁 ESTRUTURA DE ARQUIVOS:")
        print(f"   {project_root}/")
        print(f"   ├── config/")
        print(f"   │   ├── config_narracao.json ✅")
        print(f"   │   └── chaves-api-elevenlabs.txt (você precisa criar)")
        print(f"   ├── scripts/scripts-narracao/")
        print(f"   │   ├── proxy_manager.py ✅")
        print(f"   │   ├── elevenlabs_api_atualizado.py (versão V4)")
        print(f"   │   └── narrador_roteiros_final.py")
        print(f"   ├── roteiros_gerados/ (seus roteiros .txt)")
        print(f"   ├── narracao/ (saída dos áudios)")
        print(f"   └── run_with_proxy.py ✅")
        
        print(f"\n⚠️  LEMBRETES IMPORTANTES:")
        print(f"   🌐 Proxy está SEMPRE habilitado")
        print(f"   🔑 Use apenas chaves testadas com proxy")
        print(f"   🚫 NUNCA use --no-proxy")
        print(f"   ⏱️  Aguarde entre processamentos para evitar rate limits")
        
        # 6. Verifica arquivos necessários
        print(f"\n6️⃣ VERIFICANDO ARQUIVOS NECESSÁRIOS:")
        
        required_files = [
            (scripts_dir / "proxy_manager.py", "Gerenciador de proxy"),
            (scripts_dir / "api_key_manager.py", "Gerenciador de chaves"),
            (scripts_dir / "config_manager.py", "Gerenciador de configuração"),
            (scripts_dir / "elevenlabs_api_atualizado.py", "Cliente ElevenLabs"),
            (scripts_dir / "processador_arquivos_atualizado.py", "Processador de arquivos"),
            (scripts_dir / "narrador_roteiros_final.py", "Script principal")
        ]
        
        missing_files = []
        for file_path, description in required_files:
            if file_path.exists():
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - {file_path.name}")
                missing_files.append(file_path.name)
        
        if missing_files:
            print(f"\n⚠️  ARQUIVOS FALTANDO:")
            for filename in missing_files:
                print(f"   • {filename}")
            print(f"\n💡 Você precisa copiar estes arquivos para: {scripts_dir}")
        
        print(f"\n🎯 CONFIGURAÇÃO CONCLUÍDA!")
        print(f"   ✅ Sistema configurado para usar SEMPRE proxy")
        print(f"   ✅ Voice ID válido configurado (Aria)")
        print(f"   ✅ Configurações otimizadas")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ ERRO: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
