# -*- coding: utf-8 -*-
import os
import openai
import time
import glob # Para listar arquivos
import re # Para filtrar premissas numeradas

# --- Configurações Globais ---

# Calcula o diretório do script atual
script_dir = os.path.dirname(os.path.abspath(__file__))
# Calcula o diretório pai (um nível acima)
parent_dir = os.path.dirname(script_dir)
# Calcula a raiz do projeto (dois níveis acima do script)
PROJECT_ROOT = os.path.dirname(parent_dir)

print(f"[INFO] Raiz do projeto detectada em: {PROJECT_ROOT}")

# Caminho para o arquivo de chave API (relativo à raiz do projeto)
API_KEY_FILE_REL = "config/chaves-api-openai.txt"
API_KEY_FILE = os.path.join(PROJECT_ROOT, API_KEY_FILE_REL)

# IDs dos Assistentes
STRUCTURE_ASSISTANT_ID = "asst_HdhUznDBg2eXWJBnF1DSnMJc"
SCRIPT_WRITER_ASSISTANT_ID = "asst_okaY92HsYM4XC2irzdEiyhHq"

# Caminhos de Arquivos e Diretórios (relativos à raiz do projeto)
PREMISES_FILE_PATH_REL = "generated_premises.md"
STRUCTURE_OUTPUT_DIR_REL = "logs/estruturas_de_historias" # Diretório para estruturas intermediárias
SCRIPT_OUTPUT_DIR_REL = "roteiros_gerados" # Diretório para roteiros finais

PREMISES_FILE_PATH = os.path.join(PROJECT_ROOT, PREMISES_FILE_PATH_REL)
STRUCTURE_OUTPUT_DIR = os.path.join(PROJECT_ROOT, STRUCTURE_OUTPUT_DIR_REL)
SCRIPT_OUTPUT_DIR = os.path.join(PROJECT_ROOT, SCRIPT_OUTPUT_DIR_REL)

# Configurações para Geração de Roteiro por Blocos
SCRIPT_BLOCK_TARGET_LENGTHS = [
    3000,  # Bloco 1
    5000,  # Bloco 2
    4500,  # Bloco 3
    6000,  # Bloco 4
    6500,  # Bloco 5
    6000,  # Bloco 6
    4000   # Bloco 7
]
SCRIPT_BLOCK_CHAR_MARGIN = 400
MAX_ITERATIONS_PER_BLOCK = 3 # Número máximo de tentativas para ajustar o tamanho do bloco
MAX_ROTEIROS_TO_GENERATE = 1 # Limita a geração aos 1 primeiros roteiros para validação

# Cliente OpenAI (será inicializado em main)
client = None

# --- Funções Auxiliares ---

def load_api_key(file_path):
    """Lê a chave da API de um arquivo (caminho absoluto construído)."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            key = f.read().strip()
            if not key:
                print(f"Erro: O arquivo de chave API '{file_path}' está vazio.")
                return None
            return key
    except FileNotFoundError:
        print(f"Erro: Arquivo de chave API não encontrado em '{file_path}'. Crie o arquivo com sua chave ou verifique o caminho.")
        return None
    except Exception as e:
        print(f"Erro ao ler o arquivo de chave API '{file_path}': {e}")
        return None

def read_file_content(file_path):
    """Lê todo o conteúdo de um arquivo (caminho absoluto construído)."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"Erro: Arquivo não encontrado em '{file_path}'.")
        return None
    except Exception as e:
        print(f"Erro ao ler o arquivo '{file_path}': {e}")
        return None

def sanitize_filename(text, max_length=50):
    """Limpa o texto para ser usado como nome de arquivo."""
    sanitized = "".join(c for c in text if c.isalnum() or c in (' ', '_', '-')).rstrip()
    sanitized = sanitized.replace(' ', '_')
    if not sanitized:
        return "sem_titulo"
    return sanitized[:max_length]

def get_assistant_response(assistant_id, thread_id, user_prompt, instruction_prefix=""):
    """Envia um prompt para o assistente e obtém a resposta."""
    if not client:
        return "Erro: Cliente OpenAI não inicializado."

    full_prompt = f"{instruction_prefix}\n\n{user_prompt}"

    try:
        client.beta.threads.messages.create(
            thread_id=thread_id,
            role="user",
            content=full_prompt
        )

        run = client.beta.threads.runs.create(
            thread_id=thread_id,
            assistant_id=assistant_id
        )

        max_retries = 24
        retries = 0
        while run.status not in ["completed", "failed", "cancelled", "expired"] and retries < max_retries:
            time.sleep(5)
            run = client.beta.threads.runs.retrieve(thread_id=thread_id, run_id=run.id)
            retries += 1

        if retries == max_retries and run.status not in ["completed", "failed", "cancelled", "expired"]:
             return f"A execução do assistente ({assistant_id}) excedeu o tempo limite de espera ({max_retries*5}s). Último status: {run.status}"

        if run.status == "completed":
            messages = client.beta.threads.messages.list(thread_id=thread_id, order='desc')
            for msg in messages.data:
                if msg.role == "assistant":
                    # Verifica se o conteúdo é do tipo esperado antes de acessar
                    if msg.content and isinstance(msg.content, list) and len(msg.content) > 0:
                        content_item = msg.content[0]
                        if hasattr(content_item, 'text') and hasattr(content_item.text, 'value'):
                            return content_item.text.value
            return "Nenhuma resposta válida do assistente encontrada."
        else:
            error_message = f"A execução do assistente ({assistant_id}) falhou ou foi cancelada. Status: {run.status}"
            if run.last_error:
                error_message += f" Detalhes do erro: {run.last_error.message} (Código: {run.last_error.code})"
            print(error_message)
            return error_message

    except openai.APIError as e:
        print(f"Erro na API da OpenAI ({assistant_id}): {e}")
        return f"Erro na API da OpenAI: {e}"
    except Exception as e:
        print(f"Ocorreu um erro inesperado ao interagir com a OpenAI ({assistant_id}): {e}")
        return f"Ocorreu um erro inesperado: {e}"

# --- Módulo de Geração de Estruturas ---

def generate_story_structures():
    """Lê premissas e gera estruturas de história para cada uma."""
    print("\n--- Iniciando Módulo de Geração de Estruturas de História ---")
    # Usa o caminho absoluto construído dinamicamente
    premises_content = read_file_content(PREMISES_FILE_PATH)
    if not premises_content:
        print(f"Nenhuma premissa encontrada ou erro ao ler o arquivo de premissas em '{PREMISES_FILE_PATH}'. Encerrando geração de estruturas.")
        return False # Indica falha

    all_lines = [p.strip() for p in premises_content.split('\n') if p.strip()]
    premise_list = [line for line in all_lines if re.match(r"^\d+\.\s*", line)]

    if not premise_list:
        print(f"Nenhuma premissa numerada válida (ex: '1. Texto...') encontrada em '{PREMISES_FILE_PATH}'. Encerrando geração de estruturas.")
        return False # Indica falha

    # Usa o caminho absoluto construído dinamicamente
    if not os.path.exists(STRUCTURE_OUTPUT_DIR):
        try:
            os.makedirs(STRUCTURE_OUTPUT_DIR)
            print(f"Diretório de saída de estruturas '{STRUCTURE_OUTPUT_DIR}' criado.")
        except OSError as e:
            print(f"Erro ao criar o diretório de saída de estruturas '{STRUCTURE_OUTPUT_DIR}': {e}.")
            return False # Indica falha

    print(f"Total de premissas numeradas para gerar estruturas: {len(premise_list)}")
    generated_structure_files = []
    for i, premise_text in enumerate(premise_list):
        print(f"\nProcessando Premissa Numerada {i+1}/{len(premise_list)} para estrutura: ({premise_text[:60]}...)")

        try:
            structure_thread_obj = client.beta.threads.create()
            structure_thread_id = structure_thread_obj.id
        except Exception as e:
            print(f"Erro ao criar thread para estrutura da premissa {i+1}: {e}")
            continue

        story_structure = get_assistant_response(STRUCTURE_ASSISTANT_ID, structure_thread_id, premise_text, "Por favor, gere a estrutura da história para a seguinte premissa de vingança:")

        if story_structure and not story_structure.startswith("Erro") and not story_structure.startswith("A execução do assistente falhou") and not story_structure == "Nenhuma resposta válida do assistente encontrada." and not story_structure.startswith("A execução do assistente excedeu"):
            base_filename = sanitize_filename(premise_text)
            if not base_filename:
                base_filename = f"estrutura_historia_{i+1}"
            # Usa o caminho absoluto construído dinamicamente para o diretório
            output_filename = os.path.join(STRUCTURE_OUTPUT_DIR, f"{base_filename}.txt")

            try:
                with open(output_filename, 'w', encoding='utf-8') as f:
                    f.write(story_structure)
                print(f"Estrutura da história salva em: {output_filename}")
                generated_structure_files.append(output_filename)
            except IOError as e:
                print(f"Erro ao salvar o arquivo de estrutura '{output_filename}': {e}")
        else:
            print(f"Não foi possível gerar a estrutura para a premissa: {premise_text[:60]}... Resposta: {story_structure}")

    print("\n--- Módulo de Geração de Estruturas de História Concluído ---")
    return True if generated_structure_files else False

# --- Módulo de Geração de Roteiros ---

def generate_scripts_from_structures():
    """Lê estruturas de história e gera roteiros bloco a bloco."""
    print("\n--- Iniciando Módulo de Geração de Roteiros ---")

    # Usa o caminho absoluto construído dinamicamente
    if not os.path.exists(STRUCTURE_OUTPUT_DIR):
        print(f"Diretório de estruturas '{STRUCTURE_OUTPUT_DIR}' não encontrado. Execute o módulo de geração de estruturas primeiro.")
        return

    # Usa o caminho absoluto construído dinamicamente para listar arquivos
    all_structure_files = sorted(glob.glob(os.path.join(STRUCTURE_OUTPUT_DIR, "*.txt")))

    # Limita aos N primeiros arquivos, conforme MAX_ROTEIROS_TO_GENERATE
    structure_files_to_process = all_structure_files[:MAX_ROTEIROS_TO_GENERATE]

    if not structure_files_to_process:
        print(f"Nenhum arquivo de estrutura (.txt) encontrado ou selecionado em '{STRUCTURE_OUTPUT_DIR}' para processar (limite: {MAX_ROTEIROS_TO_GENERATE}).")
        return

    # Usa o caminho absoluto construído dinamicamente
    if not os.path.exists(SCRIPT_OUTPUT_DIR):
        try:
            os.makedirs(SCRIPT_OUTPUT_DIR)
            print(f"Diretório de saída de roteiros '{SCRIPT_OUTPUT_DIR}' criado.")
        except OSError as e:
            print(f"Erro ao criar o diretório de saída de roteiros '{SCRIPT_OUTPUT_DIR}': {e}.")
            return

    print(f"Total de estruturas para gerar roteiros (limitado a {MAX_ROTEIROS_TO_GENERATE}): {len(structure_files_to_process)}")

    for i, structure_filepath in enumerate(structure_files_to_process):
        structure_filename = os.path.basename(structure_filepath)
        print(f"\nProcessando Estrutura {i+1}/{len(structure_files_to_process)}: {structure_filename}")

        # structure_filepath já é o caminho absoluto vindo do glob
        story_structure_content = read_file_content(structure_filepath)
        if not story_structure_content:
            print(f"Não foi possível ler o conteúdo de {structure_filepath}. Pulando.")
            continue

        try:
            script_thread_obj = client.beta.threads.create()
            script_thread_id = script_thread_obj.id
        except Exception as e:
            print(f"Erro ao criar thread para roteiro da estrutura {structure_filename}: {e}")
            continue

        full_script_content = ""
        initial_prompt = f"Baseado na seguinte estrutura de história, vamos escrever o roteiro em blocos. A estrutura é:\n\n{story_structure_content}\n\nPor favor, identifique os blocos principais desta estrutura para que possamos desenvolvê-los um a um."
        client.beta.threads.messages.create(
            thread_id=script_thread_id,
            role="user",
            content=initial_prompt
        )

        for block_num, target_len in enumerate(SCRIPT_BLOCK_TARGET_LENGTHS):
            print(f"  Gerando Bloco {block_num + 1}/{len(SCRIPT_BLOCK_TARGET_LENGTHS)} (Meta: {target_len} caracteres)... ")
            current_block_content = ""
            iteration = 0

            min_len = target_len - SCRIPT_BLOCK_CHAR_MARGIN
            max_len = target_len + SCRIPT_BLOCK_CHAR_MARGIN

            block_prompt = f"Estamos escrevendo um roteiro baseado na estrutura fornecida anteriormente (contida nesta thread)."
            if full_script_content:
                block_prompt += f"\n\nOs blocos anteriores do roteiro são:\n{full_script_content[-2000:]}"
                block_prompt += f"\n\nContinue a história, desenvolvendo o BLOCO {block_num + 1} do roteiro."
            else:
                block_prompt = f"Comece a escrever o BLOCO {block_num + 1} do roteiro, seguindo a estrutura fornecida no início da nossa conversa nesta thread."

            block_prompt += f" Este bloco deve ter aproximadamente {target_len} caracteres. Tente ficar entre {min_len} e {max_len} caracteres."
            block_prompt += " O assistente deve identificar e desenvolver o conteúdo apropriado para este bloco com base na estrutura geral da história já fornecida."

            while iteration < MAX_ITERATIONS_PER_BLOCK:
                iteration += 1
                print(f"    Tentativa {iteration} para o Bloco {block_num + 1}...")

                adjustment_instruction = ""
                if iteration > 1 and current_block_content:
                    if len(current_block_content) < min_len:
                        adjustment_instruction = f"O bloco anterior que você gerou para o BLOCO {block_num + 1} tinha {len(current_block_content)} caracteres, o que é menos que os {min_len} necessários. Por favor, expanda o conteúdo para atingir a meta de aproximadamente {target_len} caracteres, mantendo a coerência com a estrutura e o que já foi escrito."
                    elif len(current_block_content) > max_len:
                        adjustment_instruction = f"O bloco anterior que você gerou para o BLOCO {block_num + 1} tinha {len(current_block_content)} caracteres, o que é mais que os {max_len} permitidos. Por favor, resuma ou condense o conteúdo para atingir a meta de aproximadamente {target_len} caracteres, mantendo a coerência com a estrutura e o que já foi escrito."

                final_prompt_for_assistant = block_prompt
                if adjustment_instruction:
                    final_prompt_for_assistant = f"{adjustment_instruction}\n\nLembre-se do prompt original para este bloco e da estrutura geral: {block_prompt}"

                generated_text = get_assistant_response(SCRIPT_WRITER_ASSISTANT_ID, script_thread_id, final_prompt_for_assistant)

                if generated_text and not generated_text.startswith("Erro") and not generated_text.startswith("A execução do assistente falhou") and not generated_text == "Nenhuma resposta válida do assistente encontrada." and not generated_text.startswith("A execução do assistente excedeu"):
                    current_block_content = generated_text
                    char_count = len(current_block_content)
                    print(f"      Bloco {block_num + 1} gerado com {char_count} caracteres (Meta: {min_len}-{max_len}).")
                    if min_len <= char_count <= max_len:
                        print(f"      Tamanho do Bloco {block_num + 1} está dentro da margem. OK.")
                        break
                    elif iteration == MAX_ITERATIONS_PER_BLOCK:
                        print(f"      Máximo de iterações ({MAX_ITERATIONS_PER_BLOCK}) atingido para o Bloco {block_num + 1}. Usando o conteúdo atual de {char_count} caracteres.")
                else:
                    print(f"    Erro ao gerar Bloco {block_num + 1}, tentativa {iteration}. Resposta: {generated_text}")
                    current_block_content = f"[[ERRO NA GERAÇÃO DO BLOCO {block_num + 1}]]"
                    if iteration == MAX_ITERATIONS_PER_BLOCK:
                        break
                    time.sleep(10)

            full_script_content += f"\n\n--- BLOCO {block_num + 1} ---\n{current_block_content}\n--- FIM DO BLOCO {block_num + 1} ---"

            script_base_filename = sanitize_filename(os.path.splitext(structure_filename)[0] + "_roteiro")
            # Usa o caminho absoluto construído dinamicamente para o diretório
            script_output_filename = os.path.join(SCRIPT_OUTPUT_DIR, f"{script_base_filename}.txt")
            try:
                with open(script_output_filename, 'w', encoding='utf-8') as f_script:
                    f_script.write(full_script_content)
                print(f"  Roteiro parcial salvo em: {script_output_filename}")
            except IOError as e:
                print(f"  Erro ao salvar o roteiro parcial '{script_output_filename}': {e}")

        print(f"Roteiro completo para {structure_filename} gerado e salvo.")

    print("\n--- Módulo de Geração de Roteiros Concluído ---")

# --- Função Principal ---

def main():
    """Função principal para orquestrar os módulos."""
    global client
    print("Iniciando o sistema de geração de histórias e roteiros...")
    # A raiz do projeto já foi impressa no início

    # Verifica se o diretório raiz existe
    if not os.path.isdir(PROJECT_ROOT):
        print(f"Erro: O diretório raiz do projeto '{PROJECT_ROOT}' não foi encontrado. Verifique a lógica de cálculo da raiz.")
        return

    # Usa o caminho absoluto construído dinamicamente
    api_key_value = load_api_key(API_KEY_FILE)
    if not api_key_value:
        print(f"Chave API não encontrada ou inválida. Verifique o arquivo '{API_KEY_FILE}'. Encerrando.")
        return

    try:
        client = openai.OpenAI(api_key=api_key_value)
        client.models.list() # Testa a conexão/autenticação
        print("Cliente OpenAI inicializado e conexão testada com sucesso.")
    except openai.AuthenticationError:
        print(f"Erro de autenticação com a OpenAI. Verifique se a chave em '{API_KEY_FILE}' é válida.")
        return
    except openai.APIConnectionError as e:
        print(f"Erro ao conectar com a API da OpenAI: {e}")
        return
    except Exception as e:
        print(f"Erro inesperado ao inicializar o cliente OpenAI: {e}")
        return

    # Etapa 1: Gerar Estruturas
    structures_generated = generate_story_structures()

    # Etapa 2: Gerar Roteiros (só executa se as estruturas foram geradas)
    if structures_generated:
        generate_scripts_from_structures()
    else:
        print("\nGeração de roteiros não iniciada porque a geração de estruturas falhou ou não produziu arquivos.")

    print("\nProcesso concluído.")

if __name__ == "__main__":
    main()