"""
Processador de Arquivos Sequencial - Versão 3.0
Suporta retomada de processamento e pausas manuais para troca de IP
"""

import os
import re
import time
import shutil
import json
from typing import List, Dict, Tuple, Optional, Union
from pathlib import Path
import logging

# Importa os módulos necessários
from api_key_manager_sequential import SequentialAPIKeyManager
from elevenlabs_api_atualizado import ElevenLabsAPI

logger = logging.getLogger("NarradorRoteiros.ProcessadorSequencial")

class ProcessadorArquivosSequencial:
    """
    Processador que trabalha em conjunto com SequentialAPIKeyManager
    para processar arquivos com suporte a retomada e pausas manuais.
    """

    def __init__(self,
                 diretorio_roteiros: Path,
                 diretorio_saida: Path,
                 tamanho_minimo_bloco: int,
                 tamanho_maximo_bloco: int,
                 api_key_manager: SequentialAPIKeyManager,
                 elevenlabs_api: ElevenLabsAPI,
                 modelo_id: str,
                 min_block_size_on_fallback: int = 50,
                 voice_settings: Optional[Dict] = None):
        """
        Inicializa o processador sequencial de arquivos.
        """
        self.diretorio_roteiros = diretorio_roteiros
        self.diretorio_saida = diretorio_saida
        self.tamanho_minimo_bloco = tamanho_minimo_bloco
        self.tamanho_maximo_bloco = tamanho_maximo_bloco
        self.api_key_manager = api_key_manager
        self.elevenlabs_api = elevenlabs_api
        self.modelo_id = modelo_id
        self.min_block_size_on_fallback = min_block_size_on_fallback
        self.voice_settings = voice_settings or {
            "stability": 0.9,
            "similarity_boost": 0.95,
            "style": 0.0,
            "use_speaker_boost": True
        }
        
        # Diretórios para arquivos temporários e finais
        self.temp_dir = self.diretorio_saida / "temp_processing"
        self.final_dir = self.diretorio_saida / "completed_files"
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.final_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Processador Sequencial inicializado")
        logger.info(f"Entrada: {self.diretorio_roteiros}")
        logger.info(f"Saída: {self.diretorio_saida}")
        logger.info(f"Temp: {self.temp_dir}")
        logger.info(f"Final: {self.final_dir}")

    def detectar_idioma_por_caminho(self, caminho_arquivo: Path) -> Optional[str]:
        """Detecta o idioma com base na estrutura de pastas."""
        if caminho_arquivo.parent == self.diretorio_roteiros:
            return "pt-BR"
        
        try:
            relative_path = caminho_arquivo.relative_to(self.diretorio_roteiros)
            if len(relative_path.parts) > 1:
                codigo_idioma = relative_path.parts[0]
                if codigo_idioma in self.elevenlabs_api.LANGUAGE_CODES:
                    logger.info(f"Idioma detectado: {codigo_idioma} para {caminho_arquivo.name}")
                    return codigo_idioma
        except ValueError:
            pass
        
        logger.info(f"Usando idioma padrão pt-BR para: {caminho_arquivo.name}")
        return "pt-BR"

    def encontrar_arquivos_txt(self) -> List[Path]:
        """Encontra todos os arquivos .txt, excluindo os já processados."""
        if not self.diretorio_roteiros.is_dir():
            logger.warning(f"Diretório de roteiros não encontrado: {self.diretorio_roteiros}")
            return []
        
        todos_arquivos = list(self.diretorio_roteiros.rglob("*.txt"))
        arquivos_pendentes = []
        
        for arquivo in todos_arquivos:
            if not self.api_key_manager.is_file_processed(str(arquivo)):
                arquivos_pendentes.append(arquivo)
            else:
                logger.info(f"Arquivo já processado (pulando): {arquivo.name}")
        
        logger.info(f"Encontrados {len(arquivos_pendentes)} arquivos pendentes de {len(todos_arquivos)} total")
        return arquivos_pendentes

    def _limpar_tags_bloco(self, texto: str) -> str:
        """Remove as tags de bloco do texto."""
        padrao_tags = r"^(?:--- BLOCO \d+ ---|### BLOCO \d+:.*|--- FIM DO BLOCO \d+ ---)\s*$"
        texto_sem_tags = re.sub(padrao_tags, '', texto, flags=re.MULTILINE)
        texto_final = re.sub(r'^\s*\n', '', texto_sem_tags, flags=re.MULTILINE)
        return texto_final.strip()

    def _find_best_split_point(self, text: str, min_len: int, max_len: int) -> int:
        """Encontra o melhor ponto para quebrar o texto."""
        text_len = len(text)
        actual_max_len = min(max_len, text_len)
        actual_min_len = min(min_len, actual_max_len)

        if text_len <= actual_min_len:
            return text_len - 1

        search_slice = text[actual_min_len:actual_max_len]
        slice_len = len(search_slice)

        if slice_len <= 0:
            return actual_max_len - 1

        # Busca pontos de quebra ideais
        for match in reversed(list(re.finditer(r'[.!?](?=\s|$)', search_slice))):
            return match.start() + actual_min_len

        last_comma = search_slice.rfind(',')
        if last_comma != -1:
            return last_comma + actual_min_len

        last_space = search_slice.rfind(' ')
        if last_space != -1:
            return last_space + actual_min_len

        return actual_max_len - 1

    def _show_progress_status(self, arquivo_nome: str, bloco_atual: int, caracteres_processados: int, 
                             total_caracteres: int, chave_restante: int) -> None:
        """Mostra o status atual do processamento."""
        progresso_pct = (caracteres_processados / total_caracteres) * 100 if total_caracteres > 0 else 0
        
        print(f"\n📊 STATUS DO PROCESSAMENTO")
        print(f"📄 Arquivo: {arquivo_nome}")
        print(f"🔢 Bloco: {bloco_atual}")
        print(f"📈 Progresso: {progresso_pct:.1f}% ({caracteres_processados}/{total_caracteres} chars)")
        print(f"🔑 Chave atual: {chave_restante} chars restantes")
        print(f"⚠️  Limite para pausa: {self.api_key_manager.MIN_REMAINING_THRESHOLD} chars")

    def processar_arquivo(self, caminho_arquivo: Path) -> Union[Path, str]:
        """
        Processa um único arquivo com suporte a retomada e pausa automática.
        """
        logger.info(f"Iniciando processamento: {caminho_arquivo.name}")

        # Verifica se arquivo já foi processado completamente
        arquivo_final = self.final_dir / f"{caminho_arquivo.stem}.mp3"
        if arquivo_final.exists():
            logger.info(f"Arquivo já processado: {caminho_arquivo.name}")
            return arquivo_final

        # Verifica se há processamento parcial em andamento
        processamento_parcial = self._verificar_processamento_parcial(caminho_arquivo)
        if processamento_parcial:
            resposta = input(f"\n🔄 Encontrado processamento parcial de '{caminho_arquivo.name}'.\n"
                           f"📊 Blocos já processados: {processamento_parcial['blocos_existentes']}\n"
                           f"📈 Progresso estimado: {processamento_parcial['progresso_pct']:.1f}%\n"
                           f"Deseja continuar de onde parou? (S/n): ").strip().lower()

            if resposta in ['', 's', 'sim', 'y', 'yes']:
                logger.info(f"Retomando processamento parcial de {caminho_arquivo.name}")
                return self._retomar_processamento_parcial(caminho_arquivo, processamento_parcial)
            else:
                logger.info(f"Reiniciando processamento completo de {caminho_arquivo.name}")
                # Remove arquivos parciais para recomeçar
                self._limpar_processamento_parcial(processamento_parcial['dir_temp'])

        try:
            # 1. Detecta idioma e prepara texto
            codigo_idioma = self.detectar_idioma_por_caminho(caminho_arquivo)
            texto_original = caminho_arquivo.read_text(encoding='utf-8')
            texto_limpo = self._limpar_tags_bloco(texto_original)
            
            if not texto_limpo:
                logger.warning(f"Arquivo vazio após limpeza: {caminho_arquivo.name}")
                return f"ERRO: Arquivo vazio ou inválido"

            total_caracteres = len(texto_limpo)
            logger.info(f"Texto carregado: {total_caracteres} caracteres")

            # 2. Preparar diretório temporário para este arquivo
            nome_base = caminho_arquivo.stem
            dir_temp_arquivo = self.temp_dir / f"{nome_base}_{int(time.time())}"
            dir_temp_arquivo.mkdir(parents=True, exist_ok=True)

            # 3. Loop de processamento com blocos adaptativos
            arquivos_audio_blocos = []
            texto_restante = texto_limpo
            caracteres_processados = 0
            bloco_index = 0

            while texto_restante:
                bloco_index += 1
                available_text_len = len(texto_restante)
                
                # Mostra status atual
                self._show_progress_status(
                    caminho_arquivo.name, bloco_index, caracteres_processados, 
                    total_caracteres, self.api_key_manager.current_key_remaining
                )

                # Define tamanhos para este bloco
                if available_text_len < self.tamanho_minimo_bloco:
                    # Último bloco pequeno
                    tamanho_bloco = available_text_len
                    bloco_atual = texto_restante.strip()
                else:
                    # Bloco normal - encontra ponto de quebra
                    split_index = self._find_best_split_point(
                        texto_restante, self.tamanho_minimo_bloco, 
                        min(self.tamanho_maximo_bloco, available_text_len)
                    )
                    tamanho_bloco = split_index + 1
                    bloco_atual = texto_restante[:tamanho_bloco].strip()

                if not bloco_atual:
                    logger.warning("Bloco vazio, avançando...")
                    texto_restante = texto_restante[tamanho_bloco:]
                    continue

                logger.info(f"Processando Bloco {bloco_index}: {len(bloco_atual)} caracteres")

                try:
                    # ESTRATÉGIA HÍBRIDA: Decide se ajusta bloco ou troca chave
                    api_key, remaining_chars = self.api_key_manager.get_available_key(len(bloco_atual))

                    # Verifica se precisa ajustar bloco ou trocar chave
                    if remaining_chars < len(bloco_atual):
                        if remaining_chars >= 500:  # ESTRATÉGIA 1: Ajustar bloco (chave ainda útil)
                            logger.info(f"AJUSTANDO bloco: chave tem {remaining_chars} chars (>=500)")
                            split_index = self._find_best_split_point(bloco_atual, 50, remaining_chars - 10)
                            if split_index > 0:
                                bloco_ajustado = bloco_atual[:split_index].strip()
                                texto_nao_processado = bloco_atual[split_index:]

                                # CORREÇÃO CRÍTICA: Preserva texto não processado
                                if texto_nao_processado.strip():
                                    # Remove o bloco original do texto_restante e adiciona o não processado
                                    texto_restante = texto_nao_processado + texto_restante[tamanho_bloco:]
                                    logger.info(f"Texto preservado: {len(texto_nao_processado)} chars retornaram à fila")
                                else:
                                    texto_restante = texto_restante[tamanho_bloco:]

                                bloco_atual = bloco_ajustado
                                tamanho_bloco = len(bloco_ajustado)
                                logger.info(f"Bloco ajustado: {len(bloco_atual)} chars processados")
                            else:
                                logger.warning("Não foi possível ajustar bloco - forçando troca de chave")
                                # Força troca de chave
                                api_key, remaining_chars = self.api_key_manager.get_available_key(len(bloco_atual), force_new_key=True)
                        else:  # ESTRATÉGIA 2: Trocar chave (chave quase esgotada)
                            logger.info(f"TROCANDO chave: apenas {remaining_chars} chars restantes (<500)")
                            # Força troca de chave para preservar bloco completo
                            api_key, remaining_chars = self.api_key_manager.get_available_key(len(bloco_atual), force_new_key=True)
                            logger.info(f"Nova chave: {remaining_chars} chars disponíveis")

                    # Gera áudio para este bloco
                    caminho_audio_bloco = dir_temp_arquivo / f"bloco_{bloco_index:03d}.mp3"
                    
                    self.elevenlabs_api.text_to_speech(
                        text=bloco_atual,
                        output_path=str(caminho_audio_bloco),
                        model_id=self.modelo_id,
                        language_code=codigo_idioma,
                        api_key=api_key,
                        voice_settings=self.voice_settings
                    )
                    
                    arquivos_audio_blocos.append(caminho_audio_bloco)
                    
                    # Deduz uso e atualiza progresso
                    self.api_key_manager.deduct_usage(api_key, len(bloco_atual))
                    texto_restante = texto_restante[tamanho_bloco:]
                    caracteres_processados += len(bloco_atual)
                    
                    # Pequena pausa entre blocos
                    time.sleep(1.0)
                    
                except KeyboardInterrupt:
                    logger.info("Processamento pausado pelo usuário")
                    return f"PAUSADO: Processamento pausado para troca de IP"
                    
                except Exception as e:
                    error_msg = str(e)
                    logger.error(f"Erro no bloco {bloco_index}: {e}")

                    # Detecta se a chave foi bloqueada (erro 401)
                    if ("401" in error_msg or "Unauthorized" in error_msg or "detected_unusual_activity" in error_msg or
                        "401_BLOCKED" in error_msg):
                        logger.warning(f"CHAVE BLOQUEADA detectada: {self.api_key_manager.current_key[:8]}...")
                        logger.info("Forcando troca de chave E IP devido ao bloqueio")

                        try:
                            # Força troca de chave E IP (chave bloqueada)
                            api_key, remaining_chars = self.api_key_manager.get_available_key(len(bloco_atual), force_new_key=True, force_ip_change=True)
                            logger.info(f"Nova chave obtida apos troca de IP: {remaining_chars} caracteres disponiveis")

                            # Tenta novamente com a nova chave
                            self.elevenlabs_api.text_to_speech(
                                text=bloco_atual,
                                output_path=str(caminho_audio_bloco),
                                model_id=self.modelo_id,
                                language_code=codigo_idioma,
                                api_key=api_key
                            )

                            arquivos_audio_blocos.append(caminho_audio_bloco)
                            self.api_key_manager.deduct_usage(api_key, len(bloco_atual))
                            logger.info(f"Bloco {bloco_index} processado com nova chave apos troca de IP")

                            # IMPORTANTE: Avança texto após processamento bem-sucedido
                            texto_restante = texto_restante[tamanho_bloco:]
                            caracteres_processados += len(bloco_atual)

                        except Exception as retry_error:
                            logger.error(f"Falha mesmo com nova chave: {retry_error}")
                            # IMPORTANTE: NÃO avança texto - bloco retorna para fila
                            logger.warning(f"Bloco {bloco_index} retornando para fila para nova tentativa")
                            continue

                    # Detecta se a chave atingiu limite de vozes customizadas (erro 400)
                    elif ("voice_limit_reached" in error_msg or "custom voices" in error_msg or
                          "400" in error_msg and "limit" in error_msg.lower()):
                        logger.warning(f"LIMITE DE VOZES detectado: {self.api_key_manager.current_key[:8]}...")
                        logger.info("Removendo chave problemática e forçando troca")

                        # IMPORTANTE: Remove a chave problemática do arquivo de chaves ativas
                        chave_problemática = self.api_key_manager.current_key
                        self.api_key_manager._move_key_to_used(chave_problemática)
                        logger.info(f"Chave {chave_problemática[:8]}... removida do arquivo de chaves ativas")

                        try:
                            # Força troca de chave (sem necessariamente trocar IP)
                            api_key, remaining_chars = self.api_key_manager.get_available_key(len(bloco_atual), force_new_key=True)
                            logger.info(f"Nova chave obtida: {remaining_chars} caracteres disponiveis")

                            # Tenta novamente com a nova chave
                            self.elevenlabs_api.text_to_speech(
                                text=bloco_atual,
                                output_path=str(caminho_audio_bloco),
                                model_id=self.modelo_id,
                                language_code=codigo_idioma,
                                api_key=api_key,
                                voice_settings=self.voice_settings
                            )

                            arquivos_audio_blocos.append(caminho_audio_bloco)
                            self.api_key_manager.deduct_usage(api_key, len(bloco_atual))
                            logger.info(f"Bloco {bloco_index} processado com nova chave")

                            # IMPORTANTE: Avança texto após processamento bem-sucedido
                            texto_restante = texto_restante[tamanho_bloco:]
                            caracteres_processados += len(bloco_atual)

                        except Exception as retry_error:
                            logger.error(f"Falha mesmo com nova chave: {retry_error}")
                            # IMPORTANTE: NÃO avança texto - bloco retorna para fila
                            logger.warning(f"Bloco {bloco_index} retornando para fila para nova tentativa")
                            continue
                    else:
                        # IMPORTANTE: Para outros erros, também não avança - bloco retorna para fila
                        logger.warning(f"Bloco {bloco_index} com erro genérico retornando para fila: {error_msg}")
                        continue

            # 4. Concatenação final
            if not arquivos_audio_blocos:
                logger.warning(f"Nenhum bloco de áudio gerado para {nome_base}")
                return f"ERRO: Nenhum bloco de áudio gerado"

            logger.info(f"Concatenando {len(arquivos_audio_blocos)} blocos...")
            
            if len(arquivos_audio_blocos) == 1:
                # Apenas um bloco - move diretamente
                shutil.move(str(arquivos_audio_blocos[0]), str(arquivo_final))
            else:
                # Múltiplos blocos - concatena
                self.elevenlabs_api.concatenate_audio_files(
                    [str(p) for p in arquivos_audio_blocos], 
                    str(arquivo_final)
                )

            # 5. Limpeza e finalização
            shutil.rmtree(dir_temp_arquivo, ignore_errors=True)
            self.api_key_manager.mark_file_processed(str(caminho_arquivo))
            
            logger.info(f"Arquivo processado com sucesso: {arquivo_final}")
            return arquivo_final

        except Exception as e:
            logger.error(f"Erro geral no processamento de {caminho_arquivo.name}: {e}")
            return f"ERRO GERAL: {str(e)}"

    def _verificar_processamento_parcial(self, caminho_arquivo: Path) -> Optional[Dict]:
        """Verifica se há processamento parcial em andamento para este arquivo."""
        nome_base = caminho_arquivo.stem

        # Procura por diretórios temporários existentes
        dirs_temp_existentes = list(self.temp_dir.glob(f"{nome_base}_*"))

        for dir_temp in dirs_temp_existentes:
            if dir_temp.is_dir():
                blocos_existentes = list(dir_temp.glob("bloco_*.mp3"))
                if blocos_existentes:
                    # Calcula progresso estimado
                    texto_original = caminho_arquivo.read_text(encoding='utf-8')
                    texto_limpo = self._limpar_tags_bloco(texto_original)
                    total_caracteres = len(texto_limpo)

                    # Estima caracteres processados baseado no número de blocos
                    caracteres_estimados = len(blocos_existentes) * self.tamanho_minimo_bloco
                    progresso_pct = min(95.0, (caracteres_estimados / total_caracteres) * 100)

                    return {
                        'dir_temp': dir_temp,
                        'blocos_existentes': len(blocos_existentes),
                        'arquivos_blocos': sorted(blocos_existentes, key=lambda x: x.name),
                        'progresso_pct': progresso_pct,
                        'total_caracteres': total_caracteres
                    }

        return None

    def _limpar_processamento_parcial(self, dir_temp: Path) -> None:
        """Remove arquivos de processamento parcial."""
        try:
            if dir_temp.exists():
                shutil.rmtree(dir_temp, ignore_errors=True)
                logger.info(f"Processamento parcial removido: {dir_temp.name}")
        except Exception as e:
            logger.warning(f"Erro ao limpar processamento parcial: {e}")

    def _retomar_processamento_parcial(self, caminho_arquivo: Path, info_parcial: Dict) -> Union[Path, str]:
        """Retoma processamento de onde parou."""
        try:
            logger.info(f"Retomando processamento: {len(info_parcial['arquivos_blocos'])} blocos já processados")

            # Carrega texto e calcula onde parou
            texto_original = caminho_arquivo.read_text(encoding='utf-8')
            texto_limpo = self._limpar_tags_bloco(texto_original)

            # Estima onde parou baseado nos blocos existentes
            blocos_processados = len(info_parcial['arquivos_blocos'])
            caracteres_estimados_processados = blocos_processados * self.tamanho_minimo_bloco

            # Ajusta para não ultrapassar o texto total
            caracteres_estimados_processados = min(caracteres_estimados_processados, len(texto_limpo) - 100)

            # Continua processamento do ponto estimado
            texto_restante = texto_limpo[caracteres_estimados_processados:]
            arquivos_audio_blocos = info_parcial['arquivos_blocos'].copy()

            bloco_index = blocos_processados
            caracteres_processados = caracteres_estimados_processados
            total_caracteres = len(texto_limpo)

            print(f"\n🔄 RETOMANDO PROCESSAMENTO")
            print(f"📊 Blocos já processados: {blocos_processados}")
            print(f"📈 Progresso atual: {(caracteres_processados/total_caracteres)*100:.1f}%")
            print(f"📝 Caracteres restantes: {len(texto_restante):,}")

            # Continua processamento normal
            while texto_restante:
                bloco_index += 1
                available_text_len = len(texto_restante)

                # Mostra status atual
                self._show_progress_status(
                    caminho_arquivo.name, bloco_index, caracteres_processados,
                    total_caracteres, self.api_key_manager.current_key_remaining
                )

                # Define tamanhos para este bloco
                if available_text_len < self.tamanho_minimo_bloco:
                    tamanho_bloco = available_text_len
                    bloco_atual = texto_restante.strip()
                else:
                    split_index = self._find_best_split_point(
                        texto_restante, self.tamanho_minimo_bloco,
                        min(self.tamanho_maximo_bloco, available_text_len)
                    )
                    tamanho_bloco = split_index + 1
                    bloco_atual = texto_restante[:tamanho_bloco].strip()

                if not bloco_atual:
                    texto_restante = texto_restante[tamanho_bloco:]
                    continue

                logger.info(f"Processando Bloco {bloco_index}: {len(bloco_atual)} caracteres")

                try:
                    # ESTRATÉGIA HÍBRIDA: Decide se ajusta bloco ou troca chave
                    api_key, remaining_chars = self.api_key_manager.get_available_key(len(bloco_atual))

                    # Verifica se precisa ajustar bloco ou trocar chave
                    if remaining_chars < len(bloco_atual):
                        if remaining_chars >= 500:  # ESTRATÉGIA 1: Ajustar bloco (chave ainda útil)
                            logger.info(f"AJUSTANDO bloco: chave tem {remaining_chars} chars (>=500)")
                            split_index = self._find_best_split_point(bloco_atual, 50, remaining_chars - 10)
                            if split_index > 0:
                                bloco_ajustado = bloco_atual[:split_index].strip()
                                texto_nao_processado = bloco_atual[split_index:]

                                # CORREÇÃO CRÍTICA: Preserva texto não processado
                                if texto_nao_processado.strip():
                                    texto_restante = texto_nao_processado + texto_restante[tamanho_bloco:]
                                    logger.info(f"Texto preservado: {len(texto_nao_processado)} chars retornaram à fila")
                                else:
                                    texto_restante = texto_restante[tamanho_bloco:]

                                bloco_atual = bloco_ajustado
                                tamanho_bloco = len(bloco_ajustado)
                                logger.info(f"Bloco ajustado: {len(bloco_atual)} chars processados")
                            else:
                                logger.warning("Não foi possível ajustar bloco - forçando troca de chave")
                                api_key, remaining_chars = self.api_key_manager.get_available_key(len(bloco_atual), force_new_key=True)
                        else:  # ESTRATÉGIA 2: Trocar chave (chave quase esgotada)
                            logger.info(f"TROCANDO chave: apenas {remaining_chars} chars restantes (<500)")
                            api_key, remaining_chars = self.api_key_manager.get_available_key(len(bloco_atual), force_new_key=True)
                            logger.info(f"Nova chave: {remaining_chars} chars disponíveis")

                    # Gera áudio para este bloco
                    caminho_audio_bloco = info_parcial['dir_temp'] / f"bloco_{bloco_index:03d}.mp3"

                    self.elevenlabs_api.text_to_speech(
                        text=bloco_atual,
                        output_path=str(caminho_audio_bloco),
                        model_id=self.modelo_id,
                        language_code=self.detectar_idioma_por_caminho(caminho_arquivo),
                        api_key=api_key,
                        voice_settings=self.voice_settings
                    )

                    arquivos_audio_blocos.append(caminho_audio_bloco)

                    # Deduz uso e atualiza progresso
                    self.api_key_manager.deduct_usage(api_key, len(bloco_atual))
                    texto_restante = texto_restante[tamanho_bloco:]
                    caracteres_processados += len(bloco_atual)

                    time.sleep(1.0)

                except KeyboardInterrupt:
                    logger.info("Processamento pausado pelo usuário")
                    return f"PAUSADO: Processamento pausado para troca de IP"

                except Exception as e:
                    error_msg = str(e)
                    logger.error(f"Erro no bloco {bloco_index}: {e}")

                    # Detecta se a chave foi bloqueada (erro 401)
                    if ("401" in error_msg or "Unauthorized" in error_msg or "detected_unusual_activity" in error_msg or
                        "401_BLOCKED" in error_msg):
                        logger.warning(f"CHAVE BLOQUEADA detectada: {self.api_key_manager.current_key[:8]}...")
                        logger.info("Forçando troca de chave E IP devido ao bloqueio")

                        try:
                            # Força troca de chave E IP (chave bloqueada)
                            api_key, remaining_chars = self.api_key_manager.get_available_key(len(bloco_atual), force_new_key=True, force_ip_change=True)
                            logger.info(f"Nova chave obtida após troca de IP: {remaining_chars} caracteres disponíveis")

                            # Tenta novamente com a nova chave
                            self.elevenlabs_api.text_to_speech(
                                text=bloco_atual,
                                output_path=str(caminho_audio_bloco),
                                model_id=self.modelo_id,
                                language_code=self.detectar_idioma_por_caminho(caminho_arquivo),
                                api_key=api_key,
                                voice_settings=self.voice_settings
                            )

                            arquivos_audio_blocos.append(caminho_audio_bloco)
                            self.api_key_manager.deduct_usage(api_key, len(bloco_atual))
                            logger.info(f"Bloco {bloco_index} processado com nova chave apos troca de IP")

                            # IMPORTANTE: Avança texto após processamento bem-sucedido
                            texto_restante = texto_restante[tamanho_bloco:]
                            caracteres_processados += len(bloco_atual)

                        except Exception as retry_error:
                            logger.error(f"Falha mesmo com nova chave: {retry_error}")
                            texto_restante = texto_restante[tamanho_bloco:]
                            continue

                    # Detecta se a chave atingiu limite de vozes customizadas (erro 400)
                    elif ("voice_limit_reached" in error_msg or "custom voices" in error_msg or
                          "400" in error_msg and "limit" in error_msg.lower()):
                        logger.warning(f"LIMITE DE VOZES detectado: {self.api_key_manager.current_key[:8]}...")
                        logger.info("Removendo chave problemática e forçando troca")

                        # IMPORTANTE: Remove a chave problemática do arquivo de chaves ativas
                        chave_problemática = self.api_key_manager.current_key
                        self.api_key_manager._move_key_to_used(chave_problemática)
                        logger.info(f"Chave {chave_problemática[:8]}... removida do arquivo de chaves ativas")

                        try:
                            # Força troca de chave (sem necessariamente trocar IP)
                            api_key, remaining_chars = self.api_key_manager.get_available_key(len(bloco_atual), force_new_key=True)
                            logger.info(f"Nova chave obtida: {remaining_chars} caracteres disponiveis")

                            # Tenta novamente com a nova chave
                            self.elevenlabs_api.text_to_speech(
                                text=bloco_atual,
                                output_path=str(caminho_audio_bloco),
                                model_id=self.modelo_id,
                                language_code=self.detectar_idioma_por_caminho(caminho_arquivo),
                                api_key=api_key,
                                voice_settings=self.voice_settings
                            )

                            arquivos_audio_blocos.append(caminho_audio_bloco)
                            self.api_key_manager.deduct_usage(api_key, len(bloco_atual))
                            logger.info(f"Bloco {bloco_index} processado com nova chave")

                            # IMPORTANTE: Avança texto após processamento bem-sucedido
                            texto_restante = texto_restante[tamanho_bloco:]
                            caracteres_processados += len(bloco_atual)

                        except Exception as retry_error:
                            logger.error(f"Falha mesmo com nova chave: {retry_error}")
                            # IMPORTANTE: NÃO avança texto - bloco retorna para fila
                            logger.warning(f"Bloco {bloco_index} retornando para fila para nova tentativa")
                            continue
                    else:
                        # IMPORTANTE: Para outros erros, também não avança - bloco retorna para fila
                        logger.warning(f"Bloco {bloco_index} com erro genérico retornando para fila: {error_msg}")
                        continue

            # Finaliza processamento
            arquivo_final = self.final_dir / f"{caminho_arquivo.stem}.mp3"

            if len(arquivos_audio_blocos) == 1:
                shutil.move(str(arquivos_audio_blocos[0]), str(arquivo_final))
            else:
                self.elevenlabs_api.concatenate_audio_files(
                    [str(p) for p in arquivos_audio_blocos],
                    str(arquivo_final)
                )

            # Limpeza
            shutil.rmtree(info_parcial['dir_temp'], ignore_errors=True)
            self.api_key_manager.mark_file_processed(str(caminho_arquivo))

            logger.info(f"Processamento retomado com sucesso: {arquivo_final}")
            return arquivo_final

        except Exception as e:
            logger.error(f"Erro ao retomar processamento: {e}")
            return f"ERRO NA RETOMADA: {str(e)}"

    def processar_todos_arquivos(self) -> Dict[Path, Union[Path, str]]:
        """
        Processa todos os arquivos pendentes com suporte a retomada.
        """
        arquivos_txt = self.encontrar_arquivos_txt()
        
        if not arquivos_txt:
            logger.info("Nenhum arquivo pendente para processar")
            return {}

        resultados: Dict[Path, Union[Path, str]] = {}
        total_arquivos = len(arquivos_txt)
        
        print(f"\n🚀 INICIANDO PROCESSAMENTO SEQUENCIAL")
        print(f"📁 Total de arquivos: {total_arquivos}")
        
        # Mostra status das chaves
        status = self.api_key_manager.get_status_summary()
        print(f"🔑 Chaves disponíveis: {status['active_keys_count']}")
        print(f"💾 Caracteres estimados disponíveis: {status['estimated_chars_available']:,}")
        print(f"⚠️  Limite para pausa: {status['threshold']} caracteres")
        
        for i, arquivo_path in enumerate(arquivos_txt):
            print(f"\n{'='*60}")
            print(f"📄 ARQUIVO {i+1}/{total_arquivos}: {arquivo_path.name}")
            print(f"{'='*60}")
            
            try:
                resultado = self.processar_arquivo(arquivo_path)
                resultados[arquivo_path] = resultado
                
                if isinstance(resultado, Path):
                    print(f"✅ SUCESSO: {resultado.name}")
                else:
                    print(f"❌ FALHA: {resultado}")
                    
                    # Se foi pausado, para o processamento
                    if "PAUSADO" in resultado:
                        print(f"\n⏸️  Processamento pausado. Execute novamente para continuar.")
                        break
                        
            except KeyboardInterrupt:
                print(f"\n⏸️  Processamento interrompido pelo usuário")
                break
            except Exception as e:
                logger.error(f"Erro inesperado em {arquivo_path.name}: {e}")
                resultados[arquivo_path] = f"ERRO CRÍTICO: {str(e)}"

        # Gera relatório final
        self._gerar_relatorio_sequencial(resultados)
        return resultados

    def _gerar_relatorio_sequencial(self, resultados: Dict[Path, Union[Path, str]]) -> None:
        """Gera relatório do processamento sequencial."""
        relatorio_path = self.diretorio_saida / "relatorio_sequencial.txt"
        
        sucessos = sum(1 for v in resultados.values() if isinstance(v, Path))
        falhas = len(resultados) - sucessos
        
        try:
            with relatorio_path.open('w', encoding='utf-8') as f:
                f.write("RELATÓRIO DE PROCESSAMENTO SEQUENCIAL\n")
                f.write("=" * 50 + "\n")
                f.write(f"Processado em: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write("RESUMO:\n")
                f.write(f"- Total processado: {len(resultados)}\n")
                f.write(f"- Sucessos: {sucessos}\n")
                f.write(f"- Falhas: {falhas}\n\n")
                
                # Status das chaves
                status = self.api_key_manager.get_status_summary()
                f.write("STATUS DAS CHAVES:\n")
                f.write(f"- Chave atual: {status['current_key']}\n")
                f.write(f"- Caracteres restantes na chave atual: {status['current_key_remaining']}\n")
                f.write(f"- Chaves ativas restantes: {status['active_keys_count']}\n")
                f.write(f"- Total estimado disponível: {status['estimated_chars_available']:,} chars\n\n")
                
                f.write("DETALHES POR ARQUIVO:\n")
                for arquivo, resultado in resultados.items():
                    status_arquivo = "SUCESSO" if isinstance(resultado, Path) else "FALHA"
                    detalhe = resultado.name if isinstance(resultado, Path) else resultado
                    f.write(f"- {arquivo.name}: {status_arquivo} -> {detalhe}\n")
            
            logger.info(f"Relatório sequencial gerado: {relatorio_path}")
            
        except Exception as e:
            logger.error(f"Erro ao gerar relatório: {e}")

    def concatenar_arquivos_finais(self, arquivo_saida: str = "audio_completo.mp3") -> Optional[Path]:
        """
        Concatena todos os arquivos processados em um único arquivo.
        """
        arquivos_finais = list(self.final_dir.glob("*.mp3"))
        
        if not arquivos_finais:
            logger.warning("Nenhum arquivo final encontrado para concatenar")
            return None
        
        if len(arquivos_finais) == 1:
            logger.info("Apenas um arquivo final - copiando...")
            arquivo_resultado = self.diretorio_saida / arquivo_saida
            shutil.copy2(arquivos_finais[0], arquivo_resultado)
            return arquivo_resultado
        
        logger.info(f"Concatenando {len(arquivos_finais)} arquivos finais...")
        arquivo_resultado = self.diretorio_saida / arquivo_saida
        
        try:
            # Ordena arquivos por nome para manter ordem
            arquivos_ordenados = sorted(arquivos_finais, key=lambda x: x.name)
            
            self.elevenlabs_api.concatenate_audio_files(
                [str(p) for p in arquivos_ordenados],
                str(arquivo_resultado)
            )
            
            logger.info(f"Concatenação concluída: {arquivo_resultado}")
            return arquivo_resultado
            
        except Exception as e:
            logger.error(f"Erro na concatenação final: {e}")
            return None


if __name__ == "__main__":
    print("Este módulo deve ser usado pelo script principal narrador_roteiros_final.py")
    print("Contém o processador sequencial com suporte a retomada e pausas manuais.")
