"""
Módulo de Comunicação com a API do ElevenLabs (Versão com Proxy + Retry Automático)

Melhorias V4:
1. Integração com WebshareProxyManager para rotação automática de IP
2. Mantém toda a funcionalidade de retry automático de chaves
3. Combina proxy rotation + key management para máxima robustez
4. Logs detalhados sobre proxies e tentativas
"""

import os
import time
import requests
import json
from typing import Dict, List, Optional, Tuple, Union, BinaryIO
from pathlib import Path
import logging

# Importa o gerenciador de chaves API aprimorado
from api_key_manager import APIKeyManager
# Importa o novo gerenciador de proxies
from proxy_manager import WebshareProxyManager

logger = logging.getLogger(__name__)

class ElevenLabsAPI:
    """
    Cliente para comunicação com a API do ElevenLabs com proxy rotation + retry automático.
    """

    LANGUAGE_CODES = {
        "en": "en", "de": "de", "es": "es", "fr": "fr", "hi": "hi",
        "it": "it", "ja": "ja", "ko": "ko", "nl": "nl", "no": "no",
        "pl": "pl", "pt-BR": "pt-BR", "ro": "ro", "ru": "ru", "sv": "sv",
        "tr": "tr", "zh": "zh"
    }

    def __init__(self, 
                 api_key_manager: APIKeyManager,
                 proxy_manager: Optional[WebshareProxyManager] = None,
                 base_url: str = "https://api.elevenlabs.io",
                 model_id: str = "eleven_multilingual_v2",
                 voice_id: str = "21m00Tcm4TlvDq81kWAM",
                 output_format: str = "mp3_44100_128",
                 max_retries: int = 3,
                 retry_delay: int = 2,
                 max_key_retries: int = 5,
                 use_proxy_country: Optional[str] = None):  # NOVO: código do país para proxy específico
        """
        Inicializa o cliente da API do ElevenLabs com proxy support.

        Args:
            api_key_manager: Instância do APIKeyManager aprimorado.
            proxy_manager: Instância do WebshareProxyManager (opcional).
            max_key_retries: Máximo de chaves diferentes para tentar em caso de bloqueio.
            use_proxy_country: Código do país para proxy específico (ex: 'US', 'BR').
        """
        self.api_key_manager = api_key_manager
        self.proxy_manager = proxy_manager
        self.base_url = base_url
        self.model_id = model_id
        self.voice_id = voice_id
        self.output_format = output_format
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.max_key_retries = max_key_retries
        self.use_proxy_country = use_proxy_country
        
        # Log da configuração
        proxy_status = "COM PROXY" if self.proxy_manager else "SEM PROXY"
        country_info = f" (país: {use_proxy_country})" if use_proxy_country else ""
        logger.info(f"🚀 ElevenLabsAPI V4 inicializada - {proxy_status}{country_info}")
        
        if self.proxy_manager:
            status = self.proxy_manager.get_proxy_status()
            logger.info(f"📊 Proxies disponíveis: {status['proxy_config']['total_proxies']}")

    def _get_headers(self, api_key: str) -> Dict[str, str]:
        """Retorna os cabeçalhos HTTP para as requisições."""
        return {
            "xi-api-key": api_key,
            "Content-Type": "application/json",
            "Accept": "audio/mpeg"
        }

    def _make_request_with_proxy_and_retry(self, 
                                         method: str,
                                         url: str, 
                                         api_key: str,
                                         max_network_retries: int = 3,
                                         **kwargs) -> requests.Response:
        """
        Faz requisição HTTP com proxy (se disponível) e retry de rede.
        
        Args:
            method: Método HTTP ('GET', 'POST', etc.)
            url: URL da requisição
            api_key: Chave API do ElevenLabs
            max_network_retries: Tentativas para erros de rede
            **kwargs: Argumentos adicionais para a requisição
            
        Returns:
            Response da requisição
            
        Raises:
            requests.exceptions.HTTPError: Para erros HTTP (401, 429, etc.)
            Exception: Para erros de rede após todas as tentativas
        """
        headers = self._get_headers(api_key)
        request_kwargs = {**kwargs, 'headers': headers}
        
        # Se não tem proxy manager, usa requisição tradicional
        if not self.proxy_manager:
            logger.debug(f"Fazendo requisição SEM proxy para {url}")
            return self._traditional_request_with_retry(method, url, max_network_retries, **request_kwargs)
        
        # Usa proxy manager para fazer a requisição
        logger.debug(f"Fazendo requisição COM proxy para {url}")
        try:
            response = self.proxy_manager.make_request_with_proxy(
                method=method,
                url=url,
                use_country_proxy=self.use_proxy_country,
                max_retries=max_network_retries,
                **request_kwargs
            )
            
            # Verifica se houve erro HTTP
            response.raise_for_status()
            return response
            
        except requests.exceptions.HTTPError as e:
            # Erros HTTP (401, 429, etc.) são repassados para o gerenciamento de chaves
            raise e
        except Exception as e:
            # Outros erros (rede, proxy) são tratados aqui
            logger.error(f"Erro com proxy: {e}")
            raise e

    def _traditional_request_with_retry(self, 
                                      method: str, 
                                      url: str, 
                                      max_retries: int,
                                      **kwargs) -> requests.Response:
        """Requisição tradicional sem proxy, com retry para erros de rede."""
        for attempt in range(max_retries):
            try:
                response = requests.request(method, url, timeout=60, **kwargs)
                response.raise_for_status()
                return response
                
            except requests.exceptions.HTTPError as e:
                # HTTPError são repassados imediatamente
                raise e
                
            except requests.exceptions.RequestException as e:
                # Erros de rede - pode fazer retry
                if attempt < max_retries - 1:
                    logger.warning(f"Erro de rede (tentativa {attempt + 1}): {str(e)}, tentando novamente em {self.retry_delay}s...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"Erro de rede após {max_retries} tentativas: {str(e)}")
                    raise e
        
        raise Exception("Erro inesperado no loop de retries")

    def list_voices(self) -> Dict:
        """Lista todas as vozes disponíveis usando uma chave funcional."""
        endpoint = f"{self.base_url}/v1/voices"
        try:
            api_key, _ = self.api_key_manager.get_available_key(required_chars=1)
            
            response = self._make_request_with_proxy_and_retry(
                method="GET",
                url=endpoint,
                api_key=api_key
            )
            
            return response.json()
            
        except ValueError as e:
            error_msg = f"Erro ao obter chave para listar vozes: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"Erro ao listar vozes: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)

    def text_to_speech_with_auto_retry(self, 
                                     text: str, 
                                     output_path: str,
                                     voice_id: Optional[str] = None,
                                     model_id: Optional[str] = None,
                                     output_format: Optional[str] = None,
                                     language_code: Optional[str] = None) -> str:
        """
        Converte texto em áudio com retry automático de chaves + proxy rotation.
        
        Esta é a função principal que combina:
        - Rotação automática de IP via proxy
        - Retry automático de chaves quando bloqueadas
        - Handling completo de erros
        """
        # Validações iniciais
        if not text:
            raise ValueError("O texto para conversão não pode estar vazio.")
        if not output_path:
            raise ValueError("O caminho de saída do áudio deve ser especificado.")

        # Usa os valores padrão se não especificados
        voice_id = voice_id or self.voice_id
        model_id = model_id or self.model_id
        output_format = output_format or self.output_format
        
        char_count = len(text)
        proxy_info = "COM proxy rotation" if self.proxy_manager else "SEM proxy"
        logger.info(f"🎵 Iniciando TTS: {char_count} chars, até {self.max_key_retries} chaves, {proxy_info}")
        
        # Loop principal: tenta diferentes chaves
        keys_tried = set()
        last_error = None
        
        for key_attempt in range(self.max_key_retries):
            try:
                # Obtém uma nova chave (APIKeyManager filtra automaticamente as bloqueadas)
                api_key, remaining_chars = self.api_key_manager.get_available_key(required_chars=char_count)
                
                # Evita tentar a mesma chave novamente
                if api_key in keys_tried:
                    logger.warning(f"Chave {api_key[:8]}... já foi tentada, solicitando próxima...")
                    # Marca temporariamente como bloqueada para forçar nova seleção
                    self.api_key_manager.mark_key_as_blocked(api_key, "Temporary block for retry")
                    continue
                    
                keys_tried.add(api_key)
                proxy_status = f"proxy-{self.use_proxy_country}" if self.use_proxy_country else "proxy-auto"
                proxy_log = f" via {proxy_status}" if self.proxy_manager else ""
                logger.info(f"🔑 Tentativa {key_attempt + 1}/{self.max_key_retries}: {api_key[:8]}... ({remaining_chars} chars){proxy_log}")
                
                # Tenta usar esta chave com proxy + retries para erros de rede
                try:
                    result_path = self._text_to_speech_single_key_with_proxy(
                        text=text,
                        output_path=output_path,
                        api_key=api_key,
                        voice_id=voice_id,
                        model_id=model_id,
                        output_format=output_format,
                        language_code=language_code
                    )
                    
                    # Sucesso! Deduz o uso e retorna
                    self.api_key_manager.deduct_usage(api_key=api_key, characters_used=char_count)
                    success_msg = f"✅ TTS concluído: {api_key[:8]}..."
                    if self.proxy_manager:
                        status = self.proxy_manager.get_proxy_status()
                        success_msg += f" (proxy stats: {status['statistics']['successful_requests']} sucessos)"
                    logger.info(success_msg)
                    return result_path
                    
                except requests.exceptions.HTTPError as e:
                    if e.response.status_code == 401:
                        # Esta chave foi bloqueada/é inválida
                        error_detail = ""
                        try:
                            error_data = e.response.json()
                            error_detail = error_data.get("detail", {}).get("message", "")
                        except:
                            pass
                            
                        logger.warning(f"🚫 Chave {api_key[:8]}... bloqueada: {error_detail}")
                        self.api_key_manager.mark_key_as_blocked(api_key, f"401 Error: {error_detail}")
                        last_error = e
                        continue  # Tenta próxima chave
                        
                    else:
                        # Outros erros HTTP (429, 500, etc.)
                        logger.error(f"❌ Erro HTTP {e.response.status_code} com {api_key[:8]}...: {e.response.text}")
                        last_error = e
                        # Para 429 (rate limit), espera mais tempo
                        if e.response.status_code == 429:
                            logger.info("⏳ Rate limit detectado, aguardando...")
                            time.sleep(self.retry_delay * 2)
                        continue
                        
                except Exception as e:
                    # Outros erros (rede, proxy, etc.)
                    error_type = "proxy" if self.proxy_manager else "rede"
                    logger.error(f"❌ Erro de {error_type} com {api_key[:8]}...: {str(e)}")
                    last_error = e
                    continue
                    
            except ValueError as e:
                # Erro do APIKeyManager (sem chaves disponíveis)
                logger.error(f"🔑 APIKeyManager: {str(e)}")
                last_error = e
                break  # Não adianta continuar se não há chaves
        
        # Se chegou aqui, todas as tentativas falharam
        working_keys = len(self.api_key_manager.working_keys)
        blocked_keys = len(self.api_key_manager.blocked_keys)
        
        proxy_stats = ""
        if self.proxy_manager:
            status = self.proxy_manager.get_proxy_status()
            stats = status["statistics"]
            proxy_stats = f" Proxy stats: {stats['successful_requests']}/{stats['total_requests']} sucessos."
        
        final_error_msg = (
            f"❌ Falha no TTS após {len(keys_tried)} chaves diferentes. "
            f"Status: {working_keys} funcionais, {blocked_keys} bloqueadas.{proxy_stats} "
            f"Último erro: {str(last_error)}"
        )
        logger.error(final_error_msg)
        raise Exception(final_error_msg)

    def _text_to_speech_single_key_with_proxy(self, 
                                            text: str, 
                                            output_path: str,
                                            api_key: str,
                                            voice_id: str,
                                            model_id: str,
                                            output_format: str,
                                            language_code: Optional[str] = None) -> str:
        """
        Faz a conversão TTS usando uma chave específica com proxy support.
        """
        endpoint = f"{self.base_url}/v1/text-to-speech/{voice_id}"
        
        # Preparar payload
        payload = {
            "text": text,
            "model_id": model_id,
        }
        query_params = {"output_format": output_format}
        
        # Adiciona código de idioma se suportado
        models_supporting_lang_code = ["eleven_multilingual_v2", "eleven_turbo_v2_5"]
        if language_code and model_id in models_supporting_lang_code:
            if language_code in self.LANGUAGE_CODES:
                payload["language_code"] = self.LANGUAGE_CODES[language_code]
                logger.debug(f"Usando idioma: {language_code} ({self.LANGUAGE_CODES[language_code]})")
        
        # Faz a requisição com proxy (se disponível) + retry de rede
        response = self._make_request_with_proxy_and_retry(
            method="POST",
            url=endpoint,
            api_key=api_key,
            params=query_params,
            json=payload
        )
        
        # Sucesso - salva o arquivo
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        with open(output_path, "wb") as f:
            f.write(response.content)
        
        logger.info(f"💾 Áudio salvo: {output_path}")
        return output_path

    def text_to_speech(self, 
                       text: str, 
                       output_path: str,
                       api_key: str,
                       voice_id: Optional[str] = None,
                       model_id: Optional[str] = None,
                       output_format: Optional[str] = None,
                       language_code: Optional[str] = None) -> str:
        """
        Método de compatibilidade com a versão anterior.
        
        RECOMENDAÇÃO: Use text_to_speech_with_auto_retry() para melhor gerenciamento
        automático de chaves + proxy rotation.
        """
        logger.warning("⚠️  Usando text_to_speech() legacy. Recomendado: text_to_speech_with_auto_retry()")
        
        return self._text_to_speech_single_key_with_proxy(
            text=text,
            output_path=output_path,
            api_key=api_key,
            voice_id=voice_id or self.voice_id,
            model_id=model_id or self.model_id,
            output_format=output_format or self.output_format,
            language_code=language_code
        )

    def concatenate_audio_files(self, 
                               input_files: List[str], 
                               output_file: str) -> str:
        """
        Concatena múltiplos arquivos de áudio em um único arquivo.
        (Função mantida igual à versão anterior)
        """
        try:
            from pydub import AudioSegment
        except ImportError:
            raise ImportError("Módulo pydub não encontrado. Instale-o com 'pip install pydub'.")

        if not input_files:
            raise ValueError("A lista de arquivos de entrada para concatenação está vazia.")

        try:
            combined = None
            for i, file_path in enumerate(input_files):
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"Arquivo de áudio não encontrado: {file_path}")
                
                try:
                    audio = AudioSegment.from_file(file_path)
                except Exception as e_load:
                    logger.warning(f"Falha ao carregar {file_path} com autodetection ({e_load}). Tentando como MP3.")
                    try:
                        audio = AudioSegment.from_mp3(file_path)
                    except Exception as e_mp3:
                        raise Exception(f"Falha ao carregar arquivo de áudio {file_path}: {e_mp3}")
                
                if i == 0:
                    combined = audio
                else:
                    combined += audio
            
            if combined is None:
                raise ValueError("Nenhum segmento de áudio foi carregado para concatenação.")

            os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
            combined.export(output_file, format="mp3")
            
            logger.info(f"🔗 Áudios concatenados com sucesso: {output_file}")
            return output_file
            
        except Exception as e:
            error_msg = f"Erro ao concatenar áudios: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)

    def get_combined_status(self) -> Dict:
        """
        Retorna status combinado de chaves + proxies.
        
        Returns:
            Dicionário com status completo do sistema
        """
        # Status das chaves
        key_status = self.api_key_manager.get_status_summary()
        working_keys = len(self.api_key_manager.working_keys)
        blocked_keys = len(self.api_key_manager.blocked_keys)
        
        # Status dos proxies (se disponível)
        proxy_status = None
        if self.proxy_manager:
            proxy_status = self.proxy_manager.get_proxy_status()
        
        return {
            "api_keys": {
                "total": len(self.api_key_manager.api_keys),
                "working": working_keys,
                "blocked": blocked_keys,
                "details": key_status
            },
            "proxies": proxy_status,
            "integration": {
                "proxy_enabled": bool(self.proxy_manager),
                "country_specific": self.use_proxy_country,
                "version": "V4 - Proxy + Auto Retry"
            }
        }

# Exemplo de uso
if __name__ == "__main__":
    keys_file = "chaves-api-elevenlabs.txt"
    webshare_token = "mt643rsq556yqggigaa3emmmjmdycc2h6868bi7b"
    
    try:
        print("="*80)
        print("🚀 ELEVENLABS API V4 - COM PROXY ROTATION + AUTO RETRY")
        print("="*80)
        
        # Inicializa gerenciadores
        print("\n1️⃣ Inicializando gerenciador de chaves...")
        key_manager = APIKeyManager(keys_file)
        
        print("\n2️⃣ Inicializando gerenciador de proxies...")
        proxy_manager = WebshareProxyManager(
            api_token=webshare_token,
            use_rotating_endpoint=True,
            enable_fallback=True
        )
        
        # Verifica se há chaves funcionais
        working_count = len(key_manager.working_keys)
        if working_count == 0:
            print("❌ Nenhuma chave funcional disponível!")
            exit(1)
        
        print(f"\n3️⃣ Inicializando cliente ElevenLabs...")
        elevenlabs_api = ElevenLabsAPI(
            api_key_manager=key_manager,
            proxy_manager=proxy_manager,
            use_proxy_country=None  # None = rotação automática, ou 'US', 'BR', etc.
        )
        
        # Status combinado
        print(f"\n📊 STATUS GERAL:")
        status = elevenlabs_api.get_combined_status()
        print(f"   🔑 Chaves: {status['api_keys']['working']}/{status['api_keys']['total']} funcionais")
        if status['proxies']:
            print(f"   🌐 Proxies: {status['proxies']['proxy_config']['total_proxies']} disponíveis")
            print(f"   📍 Países: {', '.join(status['proxies']['proxy_config']['countries'][:5])}...")
        
        # Teste integrado
        print(f"\n🧪 TESTE INTEGRADO (Proxy + Chaves):")
        text = "Este é um teste da integração completa com proxy rotation e retry automático de chaves."
        output_path = "teste_proxy_integration_v4.mp3"
        
        try:
            generated_file = elevenlabs_api.text_to_speech_with_auto_retry(
                text=text, 
                output_path=output_path, 
                language_code="pt-BR"
            )
            
            print(f"✅ SUCESSO! Áudio gerado: {generated_file}")
            
            # Status final
            final_status = elevenlabs_api.get_combined_status()
            if final_status['proxies']:
                proxy_stats = final_status['proxies']['statistics']
                print(f"📈 Estatísticas de proxy: {proxy_stats['successful_requests']}/{proxy_stats['total_requests']} sucessos")
            
        except Exception as e:
            print(f"❌ FALHA: {e}")

    except Exception as e:
        print(f"❌ ERRO NA INICIALIZAÇÃO: {e}")
