"""
Módulo para busca aprimorada de canais semelhantes no YouTube.

Este módulo implementa estratégias avançadas para encontrar canais semelhantes
aos canais de referência, com foco específico no nicho de histórias de vingança.
"""

import os
import re
import time
import random
import logging
import gc
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional, Set

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("youtube_channel_finder")

class YouTubeChannelFinder:
    """
    Classe para busca aprimorada de canais semelhantes no YouTube.
    """
    
    def __init__(self, search_algorithm, reference_data_path: str):
        """
        Inicializa o buscador de canais semelhantes.
        
        Args:
            search_algorithm: Algoritmo de busca do YouTube
            reference_data_path: Caminho para o arquivo CSV com dados de referência
        """
        self.search_algorithm = search_algorithm
        self.reference_data_path = reference_data_path
        
        # Carregar dados de referência
        self.reference_data = self._load_reference_data()
        
        # Extrair canais de referência
        self.reference_channels = self._extract_reference_channels()
        
        # Extrair padrões de títulos e palavras-chave
        self.title_patterns, self.keywords = self._extract_patterns_and_keywords()
        
        # Conjunto para rastrear canais já encontrados (evitar duplicatas)
        self.found_channel_ids = set()
        
        # Adicionar IDs dos canais de referência ao conjunto
        for channel in self.reference_channels:
            self.found_channel_ids.add(channel['id'])
        
        logger.info(f"YouTubeChannelFinder inicializado com {len(self.reference_channels)} canais de referência")
    
    def _load_reference_data(self) -> List[Dict]:
        """
        Carrega os dados de referência do CSV.
        
        Returns:
            Lista de dicionários com dados de referência
        """
        import pandas as pd
        
        try:
            df = pd.read_csv(self.reference_data_path)
            logger.info(f"Dados de referência carregados: {len(df)} vídeos")
            
            # Converter para lista de dicionários
            reference_data = df.to_dict('records')
            
            return reference_data
        except Exception as e:
            logger.error(f"Erro ao carregar dados de referência: {str(e)}")
            return []
    
    def _extract_reference_channels(self) -> List[Dict]:
        """
        Extrai os canais de referência do DataFrame.
        
        Returns:
            Lista de dicionários com informações dos canais
        """
        channels = []
        channel_ids = set()
        
        for video in self.reference_data:
            channel_id = video.get('channel_id', '')
            channel_title = video.get('channel_title', '')
            
            if channel_id and channel_id not in channel_ids and channel_title:
                channel_ids.add(channel_id)
                channels.append({
                    'id': channel_id,
                    'title': channel_title
                })
        
        logger.info(f"Extraídos {len(channels)} canais de referência")
        return channels
    
    def _extract_patterns_and_keywords(self) -> Tuple[List[str], List[str]]:
        """
        Extrai padrões de títulos e palavras-chave dos dados de referência.
        
        Returns:
            Tupla com lista de padrões de títulos e lista de palavras-chave
        """
        # Padrões específicos para histórias de vingança
        title_patterns = [
            "mother-in-law", "mil", "revenge", "karma", "payback",
            "had no idea", "what was coming", "changed my", "left me",
            "told me to leave", "banned me", "emptied", "found out",
            "discovered", "realized", "shocked", "surprised"
        ]
        
        # Palavras-chave específicas para histórias de vingança
        keywords = [
            "revenge", "story", "stories", "karma", "family", "drama",
            "relationship", "marriage", "divorce", "betrayal", "cheating",
            "mother-in-law", "father-in-law", "sister", "brother", "parent",
            "child", "inheritance", "money", "property", "house", "job", "work",
            "boss", "colleague", "friend", "neighbor", "ex", "wife", "husband"
        ]
        
        # Extrair padrões adicionais dos títulos de referência
        all_titles = [video.get('title', '') for video in self.reference_data if 'title' in video]
        
        # Extrair frases comuns dos títulos
        for title in all_titles:
            # Extrair frases entre aspas
            quotes = re.findall(r'"([^"]*)"', title)
            for quote in quotes:
                if len(quote.split()) >= 3 and quote not in title_patterns:
                    title_patterns.append(quote)
            
            # Extrair frases após hífen ou travessão
            if '-' in title or '—' in title:
                parts = re.split(r'[-—]', title)
                if len(parts) > 1 and len(parts[1].strip().split()) >= 3:
                    phrase = parts[1].strip()
                    if phrase not in title_patterns:
                        title_patterns.append(phrase)
        
        # Extrair palavras-chave adicionais dos títulos
        title_words = ' '.join(all_titles).lower()
        title_words = re.sub(r'[^\w\s]', ' ', title_words)
        word_list = title_words.split()
        
        # Contar frequência das palavras
        from collections import Counter
        word_counts = Counter(word_list)
        
        # Adicionar palavras frequentes às palavras-chave
        for word, count in word_counts.most_common(30):
            if len(word) > 3 and word not in keywords and count > 1:
                keywords.append(word)
        
        # Remover duplicatas e limitar o tamanho das listas
        title_patterns = list(set(title_patterns))[:20]
        keywords = list(set(keywords))[:30]
        
        logger.info(f"Extraídos {len(title_patterns)} padrões de títulos e {len(keywords)} palavras-chave")
        return title_patterns, keywords
    
    def _generate_search_queries(self) -> List[str]:
        """
        Gera consultas de busca para encontrar canais semelhantes.
        
        Returns:
            Lista de consultas de busca
        """
        queries = []
        
        # 1. Usar nomes dos canais de referência
        for channel in self.reference_channels:
            channel_name = channel['title']
            if channel_name:
                # Adicionar nome do canal como consulta
                queries.append(channel_name)
                
                # Adicionar variações com "like" ou "similar to"
                queries.append(f"channels like {channel_name}")
                queries.append(f"similar to {channel_name}")
        
        # 2. Usar padrões de títulos
        for pattern in self.title_patterns[:10]:  # Limitar para os 10 primeiros padrões
            queries.append(pattern)
        
        # 3. Usar combinações de palavras-chave
        if len(self.keywords) >= 2:
            for i in range(min(10, len(self.keywords))):
                for j in range(i+1, min(15, len(self.keywords))):
                    queries.append(f"{self.keywords[i]} {self.keywords[j]}")
        
        # 4. Adicionar consultas específicas para o nicho
        niche_queries = [
            "revenge story channels",
            "family drama stories",
            "mother in law revenge stories",
            "relationship revenge channels",
            "karma stories youtube",
            "real life revenge stories",
            "family conflict stories",
            "marriage drama channels",
            "revenge narration channels",
            "true revenge stories"
        ]
        queries.extend(niche_queries)
        
        # Remover duplicatas e limitar o número de consultas
        unique_queries = list(set(queries))
        
        # Embaralhar para diversificar os resultados
        random.shuffle(unique_queries)
        
        # Limitar o número de consultas
        max_queries = 50
        if len(unique_queries) > max_queries:
            unique_queries = unique_queries[:max_queries]
        
        logger.info(f"Geradas {len(unique_queries)} consultas de busca para canais semelhantes")
        return unique_queries
    
    def find_similar_channels(self, max_channels: int = 30) -> List[Dict]:
        """
        Encontra canais semelhantes aos canais de referência.
        
        Args:
            max_channels: Número máximo de canais a retornar
            
        Returns:
            Lista de canais semelhantes
        """
        similar_channels = []
        
        # Gerar consultas de busca
        search_queries = self._generate_search_queries()
        
        logger.info(f"Buscando canais semelhantes usando {len(search_queries)} consultas...")
        
        # Buscar canais para cada consulta
        for query in search_queries:
            try:
                # Buscar canais relacionados à consulta
                channels = self.search_algorithm.search_channels(query, max_results=5)
                
                # Processar resultados
                for channel in channels:
                    channel_id = channel.get('id', {}).get('channelId', '')
                    
                    # Pular canais já encontrados
                    if not channel_id or channel_id in self.found_channel_ids:
                        continue
                    
                    # Adicionar ao conjunto de IDs encontrados
                    self.found_channel_ids.add(channel_id)
                    
                    # Adicionar à lista de canais semelhantes
                    similar_channels.append({
                        'id': channel_id,
                        'title': channel.get('snippet', {}).get('title', ''),
                        'description': channel.get('snippet', {}).get('description', ''),
                        'query': query  # Armazenar a consulta que encontrou este canal
                    })
                
                # Verificar se já atingimos o limite máximo
                if len(similar_channels) >= max_channels:
                    break
                
                # Aguardar um pouco entre consultas para evitar erros de rate limit
                time.sleep(0.5)
                
                # Forçar coleta de lixo após cada consulta
                gc.collect()
                
            except Exception as e:
                logger.error(f"Erro ao buscar canais para '{query}': {str(e)}")
                continue
        
        logger.info(f"Encontrados {len(similar_channels)} canais semelhantes")
        
        # Limitar ao número máximo de canais
        if len(similar_channels) > max_channels:
            similar_channels = similar_channels[:max_channels]
        
        return similar_channels
    
    def get_channel_details(self, channel_ids: List[str]) -> List[Dict]:
        """
        Obtém detalhes completos dos canais.
        
        Args:
            channel_ids: Lista de IDs de canais
            
        Returns:
            Lista de detalhes dos canais
        """
        return self.search_algorithm.get_channel_details(channel_ids)
    
    def calculate_channel_similarity(self, channel: Dict) -> float:
        """
        Calcula a similaridade de um canal com os canais de referência.
        
        Args:
            channel: Dicionário com dados do canal
            
        Returns:
            Pontuação de similaridade (0-10)
        """
        if not channel:
            return 0
        
        # Inicializar pontuação
        score = 0
        
        # Obter título e descrição
        title = channel.get('title', '').lower()
        description = channel.get('description', '').lower() if 'description' in channel else ''
        
        # Verificar palavras-chave no título
        for keyword in self.keywords:
            if keyword.lower() in title:
                score += 1.5  # Peso maior para palavras-chave no título
            if description and keyword.lower() in description:
                score += 0.5  # Peso menor para palavras-chave na descrição
        
        # Verificar padrões de títulos na descrição
        for pattern in self.title_patterns:
            pattern_lower = pattern.lower()
            if pattern_lower in title:
                score += 2  # Peso significativo para padrões no título
            if description and pattern_lower in description:
                score += 1  # Peso menor para padrões na descrição
        
        # Verificar similaridade com nomes dos canais de referência
        for ref_channel in self.reference_channels:
            ref_title = ref_channel.get('title', '').lower()
            if ref_title and (ref_title in title or title in ref_title):
                score += 3  # Peso muito alto para similaridade direta com canais de referência
        
        # Normalizar pontuação para escala 0-10
        score = min(10, score)
        
        return score
    
    def rank_similar_channels(self, channels: List[Dict]) -> List[Dict]:
        """
        Classifica canais semelhantes por similaridade.
        
        Args:
            channels: Lista de canais semelhantes
            
        Returns:
            Lista de canais ordenados por similaridade
        """
        # Calcular pontuação de similaridade para cada canal
        for channel in channels:
            similarity_score = self.calculate_channel_similarity(channel)
            channel['similarity_score'] = similarity_score
        
        # Ordenar por pontuação de similaridade (decrescente)
        sorted_channels = sorted(channels, key=lambda x: x.get('similarity_score', 0), reverse=True)
        
        return sorted_channels
