"""
APIKeyManager Sequencial - Esgota completamente uma chave antes de usar a próxima
Versão 3.0 - Rotação Manual de IP com Pausa Automática
"""

import os
import json
import requests
import time
import logging
import shutil
from typing import Dict, List, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

class SequentialAPIKeyManager:
    """
    Gerenciador de rotação de chaves API - usa apenas 2 chamadas por chave
    antes de trocar para a próxima, evitando bloqueios por uso abusivo.
    NOVA ESTRATÉGIA: Rotação rápida sem necessidade de troca de IP.
    """

    ELEVENLABS_API_ENDPOINT = "https://api.elevenlabs.io/v1/user/subscription"
    MIN_REMAINING_THRESHOLD = 200  # Pausa quando chave tem <= 200 caracteres
    MAX_CALLS_PER_KEY = 2  # 🔧 NOVA CONFIGURAÇÃO: Máximo de 2 chamadas por chave

    def __init__(self, keys_file_path: str, project_root: Optional[str] = None):
        """
        Inicializa o gerenciador sequencial de chaves API.

        Args:
            keys_file_path: Caminho para o arquivo contendo as chaves API.
            project_root: Raiz do projeto para salvar arquivos de estado.
        """
        self.keys_file_path = Path(keys_file_path)
        self.project_root = Path(project_root) if project_root else self.keys_file_path.parent
        
        # Arquivos de estado e controle
        self.state_file = self.project_root / "config" / "processing_state.json"
        self.used_keys_file = self.project_root / "config" / "chaves-usadas.txt"
        self.backup_keys_file = self.keys_file_path.with_suffix('.backup')
        
        # Estado interno
        self.active_keys: List[str] = []
        self.current_key: Optional[str] = None
        self.current_key_remaining: int = 0
        self.processing_state: Dict = {}

        # NOVA ESTRATÉGIA: Controle de chamadas por chave
        self.current_key_calls: int = 0  # Quantas chamadas já foram feitas na chave atual
        self.keys_used_current_ip: int = 0  # Mantido para compatibilidade (não usado)
        
        # Garante que diretórios existem
        self.state_file.parent.mkdir(parents=True, exist_ok=True)
        
        self._initialize_state()

    def _initialize_state(self) -> None:
        """Inicializa o estado do gerenciador."""
        # Cria backup das chaves originais se não existir
        if not self.backup_keys_file.exists() and self.keys_file_path.exists():
            shutil.copy2(self.keys_file_path, self.backup_keys_file)
            logger.info(f"Backup criado: {self.backup_keys_file}")

        # Carrega chaves ativas
        self._load_active_keys()
        
        # Carrega estado de processamento
        self._load_processing_state()
        
        # Sincroniza chave atual se houver uma ativa
        if self.current_key:
            logger.info("Sincronizando chave atual...")
            self._sync_current_key()

    def _load_active_keys(self) -> None:
        """Carrega as chaves ainda não utilizadas."""
        try:
            if self.keys_file_path.exists():
                with open(self.keys_file_path, 'r') as f:
                    self.active_keys = [line.strip() for line in f.readlines() if line.strip() and not line.startswith('#')]
                logger.info(f"Carregadas {len(self.active_keys)} chaves ativas")
            else:
                logger.warning(f"Arquivo de chaves não encontrado: {self.keys_file_path}")
                self.active_keys = []
        except Exception as e:
            logger.error(f"Erro ao carregar chaves ativas: {e}")
            self.active_keys = []

    def _load_processing_state(self) -> None:
        """Carrega o estado de processamento salvo."""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r') as f:
                    self.processing_state = json.load(f)
                
                self.current_key = self.processing_state.get('current_key')
                self.current_key_remaining = self.processing_state.get('current_key_remaining', 0)
                self.current_key_calls = self.processing_state.get('current_key_calls', 0)  # NOVA ESTRATÉGIA
                self.keys_used_current_ip = self.processing_state.get('keys_used_current_ip', 0)

                logger.info(f"Estado carregado - Chave atual: {self.current_key[:8] if self.current_key else 'Nenhuma'}...")
                logger.info(f"Caracteres restantes: {self.current_key_remaining}")
                logger.info(f"Chamadas na chave atual: {self.current_key_calls}/{self.MAX_CALLS_PER_KEY}")
            else:
                logger.info("Nenhum estado anterior encontrado - iniciando novo processamento")
                self.processing_state = {
                    'current_key': None,
                    'current_key_remaining': 0,
                    'processed_files': [],
                    'current_file_progress': None
                }
        except Exception as e:
            logger.error(f"Erro ao carregar estado: {e}")
            self.processing_state = {}

    def _save_processing_state(self) -> None:
        """Salva o estado atual de processamento."""
        try:
            self.processing_state.update({
                'current_key': self.current_key,
                'current_key_remaining': self.current_key_remaining,
                'current_key_calls': self.current_key_calls,  # NOVA ESTRATÉGIA: Salva contador de chamadas
                'keys_used_current_ip': self.keys_used_current_ip,
                'last_updated': time.time()
            })
            
            with open(self.state_file, 'w') as f:
                json.dump(self.processing_state, f, indent=2)
            
            logger.debug("Estado de processamento salvo")
        except Exception as e:
            logger.error(f"Erro ao salvar estado: {e}")

    def _sync_current_key(self) -> None:
        """Sincroniza o status da chave atual com a API."""
        if not self.current_key:
            return
            
        try:
            headers = {"xi-api-key": self.current_key}
            response = requests.get(self.ELEVENLABS_API_ENDPOINT, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            if "character_limit" in data and "character_count" in data:
                remaining = data["character_limit"] - data["character_count"]
                self.current_key_remaining = remaining
                
                logger.info(f"Chave sincronizada: {remaining} caracteres restantes")
                self._save_processing_state()
            else:
                logger.warning("Resposta da API não contém dados de caracteres esperados")
                
        except Exception as e:
            logger.error(f"Erro ao sincronizar chave atual: {e}")

    def _move_key_to_used(self, key: str) -> None:
        """Move uma chave esgotada para o arquivo de chaves usadas."""
        try:
            # Adiciona à lista de chaves usadas
            with open(self.used_keys_file, 'a') as f:
                f.write(f"{key}\n")
            
            # Remove da lista de chaves ativas
            if key in self.active_keys:
                self.active_keys.remove(key)
            
            # Reescreve o arquivo de chaves ativas
            with open(self.keys_file_path, 'w') as f:
                for active_key in self.active_keys:
                    f.write(f"{active_key}\n")
            
            logger.info(f"Chave movida para arquivo de usadas: {key[:8]}...")
            
        except Exception as e:
            logger.error(f"Erro ao mover chave para usadas: {e}")

    def _request_ip_change(self) -> bool:
        """Solicita ao usuário para trocar o IP e aguarda confirmação."""
        print("\n" + "="*60)
        print("TROCA DE IP NECESSARIA")
        print("="*60)
        print(f"Chave atual esgotada: {self.current_key[:8]}...")
        print(f"Chaves restantes: {len(self.active_keys)}")
        print()
        print("INSTRUCOES:")
        print("   1. Mude seu IP (VPN, roteador, etc.)")
        print("   2. Verifique se o IP mudou (whatismyip.com)")
        print("   3. Pressione ENTER para continuar")
        print("   4. Ou digite 'quit' para encerrar")
        print()
        
        while True:
            user_input = input("Pressione ENTER após trocar o IP (ou 'quit' para sair): ").strip().lower()
            
            if user_input == 'quit':
                print("💾 Processamento pausado. Execute novamente para continuar.")
                return False
            elif user_input == '':
                print("✅ Continuando com nova chave e novo IP...")
                return True
            else:
                print("❌ Digite apenas ENTER para continuar ou 'quit' para sair.")

    def get_available_key(self, required_chars: int, force_new_key: bool = False, force_ip_change: bool = False) -> Tuple[str, int]:
        """
        NOVA ESTRATÉGIA: Rotação rápida de chaves (máximo 2 chamadas por chave).
        Evita bloqueios por uso abusivo distribuindo as chamadas entre chaves.

        Args:
            required_chars: Número de caracteres necessários
            force_new_key: Se True, força troca para nova chave
            force_ip_change: Mantido para compatibilidade (não usado na nova estratégia)
        """
        # NOVA LÓGICA: Verifica se precisa trocar chave por limite de chamadas
        if self.current_key and self.current_key_calls >= self.MAX_CALLS_PER_KEY:
            logger.info(f"ROTACAO: {self.current_key_calls} chamadas feitas na chave atual - trocando para evitar bloqueio")
            force_new_key = True

        # Se forçar nova chave ou não há chave atual, seleciona nova
        if force_new_key or not self.current_key:
            if force_new_key and self.current_key:
                logger.info(f"ROTACAO: Trocando chave: {self.current_key[:8]}... -> nova chave")
                # Move chave atual para o final da lista (para reutilizar depois)
                self._rotate_current_key()

            if not self.active_keys:
                raise ValueError("Nenhuma chave API disponível")

            # Seleciona próxima chave e reseta contador de chamadas
            self.current_key = self.active_keys[0]
            self.current_key_calls = 0
            logger.info(f"Nova chave selecionada: {self.current_key[:8]}...")
            self._sync_current_key()
        
        # Verifica se a chave atual ainda é utilizável
        if self.current_key_remaining <= self.MIN_REMAINING_THRESHOLD:
            logger.warning(f"Chave atual com {self.current_key_remaining} caracteres (≤ {self.MIN_REMAINING_THRESHOLD})")

            # Move chave atual para usadas (esgotada)
            self._move_key_to_used(self.current_key)

            # Verifica se há mais chaves
            if not self.active_keys:
                raise ValueError("Todas as chaves foram esgotadas")

            # NOVA ESTRATÉGIA: Sem necessidade de troca de IP
            logger.info("ROTACAO: Chave esgotada - selecionando proxima chave disponivel")

            # Seleciona próxima chave e reseta contador de chamadas
            self.current_key = self.active_keys[0]
            self.current_key_calls = 0
            logger.info(f"Nova chave selecionada: {self.current_key[:8]}...")
            self._sync_current_key()
        
        # Verifica se há caracteres suficientes para a requisição
        if self.current_key_remaining < required_chars:
            logger.warning(f"Chave com caracteres insuficientes: {self.current_key_remaining} < {required_chars}")

            # Verifica se ainda pode usar os caracteres restantes (margem segura)
            if self.current_key_remaining >= 50:  # Margem mínima de segurança
                logger.info(f"Usando caracteres restantes até margem segura: {self.current_key_remaining}")
                return self.current_key, self.current_key_remaining

            # Move chave atual para usadas (esgotada)
            self._move_key_to_used(self.current_key)

            # Verifica se há mais chaves
            if not self.active_keys:
                raise ValueError("Todas as chaves foram esgotadas")

            # NOVA ESTRATÉGIA: Sem necessidade de troca de IP
            logger.info("ROTACAO: Caracteres insuficientes - selecionando proxima chave")

            # Seleciona próxima chave e reseta contador de chamadas
            self.current_key = self.active_keys[0]
            self.current_key_calls = 0
            logger.info(f"Nova chave selecionada: {self.current_key[:8]}...")
            self._sync_current_key()

        return self.current_key, self.current_key_remaining

    def deduct_usage(self, api_key: str, characters_used: int) -> None:
        """Deduz o uso de caracteres da chave atual e incrementa contador de chamadas."""
        if api_key == self.current_key:
            self.current_key_remaining = max(0, self.current_key_remaining - characters_used)
            self.current_key_calls += 1  # NOVA ESTRATÉGIA: Incrementa contador de chamadas
            logger.info(f"Uso deduzido: {characters_used} chars. Restantes: {self.current_key_remaining}")
            logger.info(f"Chamadas na chave atual: {self.current_key_calls}/{self.MAX_CALLS_PER_KEY}")
            self._save_processing_state()
        else:
            logger.warning(f"Tentativa de deduzir uso de chave não atual: {api_key[:8]}...")

    def get_status_summary(self) -> Dict:
        """Retorna resumo do status das chaves."""
        return {
            'current_key': self.current_key[:8] + "..." if self.current_key else None,
            'current_key_remaining': self.current_key_remaining,
            'active_keys_count': len(self.active_keys),
            'threshold': self.MIN_REMAINING_THRESHOLD,
            'estimated_chars_available': self.current_key_remaining + (len(self.active_keys) - 1) * 10000,
            'processing_state_file': str(self.state_file),
            'backup_file': str(self.backup_keys_file),
            'keys_used_current_ip': self.keys_used_current_ip,
            'max_calls_per_key': self.MAX_CALLS_PER_KEY,
            'current_key_calls': self.current_key_calls
        }

    def mark_file_processed(self, file_path: str) -> None:
        """Marca um arquivo como processado com sucesso."""
        if 'processed_files' not in self.processing_state:
            self.processing_state['processed_files'] = []
        
        if file_path not in self.processing_state['processed_files']:
            self.processing_state['processed_files'].append(file_path)
            self._save_processing_state()
            logger.info(f"Arquivo marcado como processado: {Path(file_path).name}")

    def is_file_processed(self, file_path: str) -> bool:
        """Verifica se um arquivo já foi processado."""
        return file_path in self.processing_state.get('processed_files', [])

    def reset_processing_state(self) -> None:
        """Reseta o estado de processamento (use com cuidado)."""
        if self.state_file.exists():
            backup_state = self.state_file.with_suffix('.reset_backup')
            shutil.copy2(self.state_file, backup_state)
            logger.info(f"Backup do estado criado em: {backup_state}")
        
        self.processing_state = {
            'current_key': None,
            'current_key_remaining': 0,
            'current_key_calls': 0,
            'processed_files': [],
            'current_file_progress': None,
            'keys_used_current_ip': 0
        }
        self.current_key = None
        self.current_key_remaining = 0
        self.current_key_calls = 0
        self.keys_used_current_ip = 0
        self._save_processing_state()
        logger.info("Estado de processamento resetado")

    def _rotate_current_key(self) -> None:
        """
        NOVA ESTRATÉGIA: Move a chave atual para o final da lista para reutilizar depois.
        Isso permite rotação contínua sem esgotar chaves desnecessariamente.
        """
        if self.current_key and self.current_key in self.active_keys:
            # Remove chave atual da posição atual
            self.active_keys.remove(self.current_key)
            # Adiciona no final da lista para reutilizar depois
            self.active_keys.append(self.current_key)
            logger.info(f"ROTACAO: Chave {self.current_key[:8]}... rotacionada para o final da lista")
            self._save_active_keys()

    def _save_active_keys(self) -> None:
        """Salva a lista de chaves ativas no arquivo."""
        try:
            with open(self.keys_file_path, 'w') as f:
                for key in self.active_keys:
                    f.write(f"{key}\n")
            logger.debug("Lista de chaves ativas salva")
        except Exception as e:
            logger.error(f"Erro ao salvar chaves ativas: {e}")


# Função utilitária para migrar do APIKeyManager antigo
def migrate_from_old_key_manager(old_keys_file: str, project_root: str) -> SequentialAPIKeyManager:
    """
    Migra do sistema antigo para o novo sistema sequencial.
    """
    logger.info("Migrando para sistema sequencial de chaves...")
    return SequentialAPIKeyManager(old_keys_file, project_root)


if __name__ == "__main__":
    # Teste do sistema sequencial
    keys_file = "chaves-api-elevenlabs.txt"
    
    try:
        manager = SequentialAPIKeyManager(keys_file)
        status = manager.get_status_summary()
        
        print("Status do Gerenciador Sequencial:")
        print(json.dumps(status, indent=2))
        
        # Teste de obtenção de chave
        key, remaining = manager.get_available_key(1000)
        print(f"\nChave obtida: {key[:8]}... ({remaining} chars restantes)")
        
    except Exception as e:
        print(f"Erro no teste: {e}")
