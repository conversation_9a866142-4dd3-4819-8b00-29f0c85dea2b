#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Converte arquivos SRT da pasta 'legendas' (na raiz do projeto) para o formato ASS
com efeito de karaoke, posicionamento e estilo customizados, salvando-os na mesma
pasta 'legendas' (na raiz do projeto).
"""

import re
import math
import sys
import os
import glob
import io
from datetime import timedelta

# Configurar stdout para UTF-8 no Windows
if sys.platform == "win32":
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# --- Configurações de Caminhos Relativos à Raiz do Projeto ---
# A raiz do projeto está duas pastas acima do diretório do script
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))

INPUT_DIR = os.path.join(PROJECT_ROOT, "legendas") # Pasta de entrada para SRTs (na raiz)
OUTPUT_DIR = INPUT_DIR # Salvar ASS na mesma pasta dos SRTs (na raiz)

# Configurações de estilo e posicionamento (padrões mantidos)
PLAY_RES_X = 1280
PLAY_RES_Y = 720
FONT_SIZE = 36
BOX_X = 197.7
BOX_Y = 547.7
BOX_WIDTH = 888.5
BOX_HEIGHT = 52.2
# -----------------------------------------------------------

def srt_time_to_timedelta(srt_time):
    """Converte string de tempo SRT (00:00:00,000) para objeto timedelta."""
    try:
        if "," in srt_time:
            parts = re.split("[:,]", srt_time)
        elif "." in srt_time:
            parts = re.split("[:.]", srt_time)
        else:
             raise ValueError("Separador de milissegundos nao encontrado")

        if len(parts) != 4:
            raise ValueError("Formato de tempo invalido")
        ms = parts[3].ljust(3, "0")
        return timedelta(hours=int(parts[0]), minutes=int(parts[1]), seconds=int(parts[2]), milliseconds=int(ms))
    except Exception as e:
        print(f"Erro ao converter tempo SRT {srt_time}: {e}", file=sys.stderr)
        return timedelta()

def timedelta_to_ass_time(td):
    """Converte objeto timedelta para string de tempo ASS (H:MM:SS.cc)."""
    if td is None:
        return "0:00:00.00"
    total_seconds = td.total_seconds()
    hours = int(total_seconds // 3600)
    minutes = int((total_seconds % 3600) // 60)
    seconds = int(total_seconds % 60)
    centiseconds = round((total_seconds * 100) % 100)
    if centiseconds == 100:
        total_seconds += 0.01
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = int(total_seconds % 60)
        centiseconds = 0
    return f"{hours:01d}:{minutes:02d}:{seconds:02d}.{centiseconds:02d}"

def parse_srt(srt_content):
    """Analisa o conteúdo SRT e retorna uma lista de blocos de legenda."""
    blocks = []
    pattern = re.compile(r"(\d+)\r?\n(\d{2}:\d{2}:\d{2}[,.]\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2}[,.]\d{3})\r?\n([\s\S]*?)(?=\r?\n\r?\n|\Z)", re.MULTILINE)
    for match in pattern.finditer(srt_content):
        seq, start_str, end_str, text = match.groups()
        start_time = srt_time_to_timedelta(start_str)
        end_time = srt_time_to_timedelta(end_str)
        cleaned_text = re.sub("<[^>]*>", "", text)
        cleaned_text = " ".join(cleaned_text.strip().split())
        if start_time >= end_time:
            print(f"Aviso: Tempo final <= inicial no bloco {seq}", file=sys.stderr)
            continue
        blocks.append({
            "seq": int(seq),
            "start": start_time,
            "end": end_time,
            "text": cleaned_text
        })
    return blocks

def create_ass_karaoke(srt_blocks, style_name, pos_x, pos_y):
    """Gera linhas de diálogo ASS com efeito de karaoke."""
    dialogue_lines = []
    for block in srt_blocks:
        start_ass = timedelta_to_ass_time(block["start"])
        end_ass = timedelta_to_ass_time(block["end"])
        duration_ms = (block["end"] - block["start"]).total_seconds() * 1000
        words = block["text"].split()
        if not words:
            continue
        word_count = len(words)
        duration_per_word_cs = math.ceil((duration_ms / word_count) / 10) if word_count > 0 else 1
        if duration_per_word_cs <= 0:
             duration_per_word_cs = 1
        karaoke_text = f"{{\\an5\\pos({pos_x:.2f},{pos_y:.2f})}}"
        for i, word in enumerate(words):
            karaoke_text += f"{{\\k{duration_per_word_cs}}}{word}"
            if i < word_count - 1:
                karaoke_text += " "
        dialogue_line = f"Dialogue: 0,{start_ass},{end_ass},{style_name},,0,0,0,,{karaoke_text}"
        dialogue_lines.append(dialogue_line)
    return "\n".join(dialogue_lines)

def generate_ass_file(srt_filepath, ass_filepath, play_res_x, play_res_y, font_size, box_x, box_y, box_width, box_height):
    """Lê SRT, converte para ASS com karaoke, e escreve o arquivo ASS."""
    script_info = f"""[Script Info]
Title: Converted from {os.path.basename(srt_filepath)}
ScriptType: v4.00+
WrapStyle: 2
PlayResX: {play_res_x}
PlayResY: {play_res_y}
ScaledBorderAndShadow: yes
Collisions: Normal

"""
    style_name = "KaraokeStyle"
    styles = f"""[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: {style_name},Arial,{font_size},&H00FFFFFF,&H0000FFFF,&H00000000,&H64000000,0,0,0,0,100,100,0,0,1,2,1,5,10,10,10,1

"""
    pos_x = box_x + (box_width / 2)
    pos_y = box_y + (box_height / 2)

    try:
        with open(srt_filepath, "r", encoding="utf-8") as f:
            srt_content = f.read()
    except FileNotFoundError:
        print(f"Erro: Arquivo SRT nao encontrado: {srt_filepath}", file=sys.stderr)
        return False
    except Exception as e:
        print(f"Erro ao ler o arquivo SRT {srt_filepath}: {e}", file=sys.stderr)
        return False

    srt_blocks = parse_srt(srt_content)
    if not srt_blocks:
        print(f"Aviso: Nenhum bloco de legenda valido encontrado ou parseado em {srt_filepath}", file=sys.stderr)
        ass_dialogue = ""
    else:
        ass_dialogue = create_ass_karaoke(srt_blocks, style_name, pos_x, pos_y)

    ass_content = script_info + styles + "[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n" + ass_dialogue
    try:
        # Garante que o diretório de saída exista antes de escrever
        os.makedirs(os.path.dirname(ass_filepath), exist_ok=True)
        with open(ass_filepath, "w", encoding="utf-8") as f:
            f.write(ass_content)
        print(f"Arquivo ASS gerado com sucesso: {ass_filepath}")
        return True
    except Exception as e:
        print(f"Erro ao escrever o arquivo ASS {ass_filepath}: {e}", file=sys.stderr)
        return False

def main():
    print("Iniciando script de conversao SRT para ASS (Karaoke)...")
    # Usa os caminhos absolutos definidos globalmente
    print(f"Usando diretorio raiz do projeto: {PROJECT_ROOT}")
    print(f"Usando diretorio de entrada/saida (legendas): {INPUT_DIR}")

    # Verifica se o diretório de entrada existe
    if not os.path.isdir(INPUT_DIR):
        print(f"Erro: Diretorio de entrada/saida nao existe: {INPUT_DIR}", file=sys.stderr)
        # Tenta criar o diretório se não existir
        try:
            os.makedirs(INPUT_DIR)
            print(f"Diretório de entrada/saida criado: {INPUT_DIR}", file=sys.stderr)
            print(f"Por favor, coloque os arquivos .srt em {INPUT_DIR}", file=sys.stderr)
        except OSError as e:
            print(f"Erro ao tentar criar o diretório de entrada/saida {INPUT_DIR}: {e}", file=sys.stderr)
            sys.exit(1)
        # Sai mesmo se criou, pois provavelmente está vazio
        print("Saindo. Adicione arquivos .srt e execute novamente.", file=sys.stderr)
        sys.exit(1)

    # Cria o diretório de saída se não existir (redundante com a verificação em generate_ass_file, mas bom ter aqui)
    # Como INPUT_DIR e OUTPUT_DIR são iguais, a verificação acima já cobre isso.
    # if not os.path.exists(OUTPUT_DIR):
    #     try:
    #         os.makedirs(OUTPUT_DIR)
    #         print(f"Diretorio de saida criado: {OUTPUT_DIR}")
    #     except OSError as e:
    #         print(f"Erro ao criar o diretorio de saida {OUTPUT_DIR}: {e}", file=sys.stderr)
    #         sys.exit(1)

    print(f"Procurando arquivos .srt em: {INPUT_DIR}")
    srt_files = glob.glob(os.path.join(INPUT_DIR, "*.srt"))

    if not srt_files:
        print(f"Nenhum arquivo .srt encontrado em {INPUT_DIR}")
        sys.exit(0)

    print(f"Encontrados {len(srt_files)} arquivos SRT para converter:")
    success_count = 0
    failure_count = 0

    for srt_path in srt_files:
        base_name = os.path.splitext(os.path.basename(srt_path))[0]
        # Usa OUTPUT_DIR para o caminho de saída
        ass_path = os.path.join(OUTPUT_DIR, f"{base_name}.ass")
        print(f"Convertendo: {os.path.basename(srt_path)} -> {os.path.basename(ass_path)}")

        # Usa as configurações fixas definidas no início do script
        if generate_ass_file(srt_path, ass_path,
                             play_res_x=PLAY_RES_X, play_res_y=PLAY_RES_Y,
                             font_size=FONT_SIZE, box_x=BOX_X,
                             box_y=BOX_Y, box_width=BOX_WIDTH,
                             box_height=BOX_HEIGHT):
            success_count += 1
        else:
            failure_count += 1
            print(f"Falha ao converter {os.path.basename(srt_path)}")

    print(f"\nConversao concluida. {success_count} sucesso(s), {failure_count} falha(s).")

if __name__ == "__main__":
    main()
