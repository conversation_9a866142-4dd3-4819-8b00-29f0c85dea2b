#!/usr/bin/env python3
"""
Script de execução do sistema de narração COM PROXY OBRIGATÓRIO
"""

import sys
from pathlib import Path

# Adiciona diretório dos scripts ao path
scripts_dir = Path(__file__).parent / "scripts" / "scripts-narracao"
sys.path.insert(0, str(scripts_dir))

# Importa e executa o script principal
try:
    from narrador_roteiros_final import main
    
    # Força uso de proxy
    sys.argv.extend([
        # "--proxy-token", "mt643rsq556yqggigaa3emmmjmdycc2h6868bi7b",  # Já no config
        # "--no-proxy",  # NUNCA usar esta opção
    ])
    
    print("🚀 EXECUTANDO SISTEMA COM PROXY OBRIGATÓRIO")
    print("⚠️  IMPORTANTE: Proxy está sempre habilitado!")
    print()
    
    sys.exit(main())
    
except ImportError as e:
    print(f"❌ Erro ao importar módulo: {e}")
    print("💡 Verifique se todos os arquivos estão no local correto:")
    print(f"   - proxy_manager.py")
    print(f"   - elevenlabs_api_atualizado.py (versão V4)")
    print(f"   - narrador_roteiros_final.py")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ Erro na execução: {e}")
    sys.exit(1)
