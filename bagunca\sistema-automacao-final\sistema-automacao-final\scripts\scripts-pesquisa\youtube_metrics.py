"""
Módulo para cálculo de métricas de vídeos do YouTube.

Este módulo implementa cálculos de métricas como visualizações por hora,
taxa de engajamento e pontuação de viralidade para vídeos do YouTube.
"""

import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("youtube_metrics")

class YouTubeMetricsCalculator:
    """
    Calculadora de métricas para vídeos do YouTube.
    """
    
    def __init__(self, reference_data=None):
        """
        Inicializa a calculadora de métricas.
        
        Args:
            reference_data: Dados de referência para calibrar métricas (opcional)
        """
        self.reference_data = reference_data
        
        # Métricas de referência (valores padrão)
        self.reference_metrics = {
            'avg_views': 100000,
            'avg_likes': 2000,
            'avg_comments': 200,
            'avg_engagement_rate': 2.5,  # em porcentagem
            'avg_views_per_hour': 250
        }
        
        # Calcular métricas de referência se dados fornecidos
        if reference_data:
            self._calculate_reference_metrics()
        
        logger.info("Calculadora de métricas inicializada")
    
    def _calculate_reference_metrics(self) -> None:
        """
        Calcula métricas de referência com base nos dados fornecidos.
        """
        if not self.reference_data:
            return
        
        # Inicializar contadores
        total_views = 0
        total_likes = 0
        total_comments = 0
        total_engagement_rate = 0
        total_views_per_hour = 0
        count = 0
        
        # Calcular métricas para cada vídeo de referência
        for video in self.reference_data:
            try:
                # Extrair dados básicos
                views = int(video.get('view_count', 0))
                likes = int(video.get('like_count', 0))
                comments = int(video.get('comment_count', 0))
                
                # Calcular taxa de engajamento
                engagement_rate = (likes + comments) / views * 100 if views > 0 else 0
                
                # Calcular visualizações por hora
                published_at = video.get('published_at', '')
                if published_at:
                    published_date = datetime.fromisoformat(published_at.replace('Z', '+00:00'))
                    hours_since_published = (datetime.now() - published_date).total_seconds() / 3600
                    views_per_hour = views / hours_since_published if hours_since_published > 0 else 0
                else:
                    views_per_hour = 0
                
                # Acumular valores
                total_views += views
                total_likes += likes
                total_comments += comments
                total_engagement_rate += engagement_rate
                total_views_per_hour += views_per_hour
                count += 1
                
            except Exception as e:
                logger.error(f"Erro ao calcular métricas para vídeo de referência: {str(e)}")
        
        # Calcular médias
        if count > 0:
            self.reference_metrics = {
                'avg_views': total_views / count,
                'avg_likes': total_likes / count,
                'avg_comments': total_comments / count,
                'avg_engagement_rate': total_engagement_rate / count,
                'avg_views_per_hour': total_views_per_hour / count
            }
        
        logger.info(f"Métricas de referência calculadas: Views={self.reference_metrics['avg_views']:.2f}, "
                  f"Likes={self.reference_metrics['avg_likes']:.2f}, "
                  f"Comments={self.reference_metrics['avg_comments']:.2f}, "
                  f"Engagement={self.reference_metrics['avg_engagement_rate']:.2f}%, "
                  f"Views/hora={self.reference_metrics['avg_views_per_hour']:.2f}")
    
    def calculate_views_per_hour(self, video: Dict) -> float:
        """
        Calcula visualizações por hora para um vídeo.
        
        Args:
            video: Dicionário com dados do vídeo
            
        Returns:
            Visualizações por hora
        """
        try:
            # Extrair dados básicos
            views = int(video.get('view_count', 0))
            
            # Extrair data de publicação
            published_at = video.get('published_at', '')
            if not published_at:
                return 0
            
            # Converter para datetime
            if isinstance(published_at, str):
                if 'T' in published_at:
                    published_date = datetime.fromisoformat(published_at.replace('Z', '+00:00'))
                else:
                    published_date = datetime.strptime(published_at, '%Y-%m-%d %H:%M:%S%z')
            else:
                published_date = published_at
            
            # Calcular horas desde a publicação
            hours_since_published = (datetime.now(published_date.tzinfo) - published_date).total_seconds() / 3600
            
            # Calcular visualizações por hora
            views_per_hour = views / hours_since_published if hours_since_published > 0 else 0
            
            return views_per_hour
            
        except Exception as e:
            logger.error(f"Erro ao calcular visualizações por hora: {str(e)}")
            return 0
    
    def calculate_engagement_rate(self, video: Dict) -> float:
        """
        Calcula taxa de engajamento para um vídeo.
        
        Args:
            video: Dicionário com dados do vídeo
            
        Returns:
            Taxa de engajamento (em porcentagem)
        """
        try:
            # Extrair dados básicos
            views = int(video.get('view_count', 0))
            likes = int(video.get('like_count', 0))
            comments = int(video.get('comment_count', 0))
            
            # Calcular taxa de engajamento
            engagement_rate = (likes + comments) / views * 100 if views > 0 else 0
            
            return engagement_rate
            
        except Exception as e:
            logger.error(f"Erro ao calcular taxa de engajamento: {str(e)}")
            return 0
    
    def calculate_virality_score(self, video: Dict) -> float:
        """
        Calcula pontuação de viralidade para um vídeo.
        
        Args:
            video: Dicionário com dados do vídeo
            
        Returns:
            Pontuação de viralidade (0-10)
        """
        try:
            # Calcular métricas básicas
            views_per_hour = self.calculate_views_per_hour(video)
            engagement_rate = self.calculate_engagement_rate(video)
            
            # Normalizar métricas em relação às referências
            normalized_views_per_hour = views_per_hour / self.reference_metrics['avg_views_per_hour'] if self.reference_metrics['avg_views_per_hour'] > 0 else 0
            normalized_engagement_rate = engagement_rate / self.reference_metrics['avg_engagement_rate'] if self.reference_metrics['avg_engagement_rate'] > 0 else 0
            
            # Calcular pontuação de viralidade (70% visualizações por hora, 30% engajamento)
            virality_score = (normalized_views_per_hour * 0.7 + normalized_engagement_rate * 0.3) * 10
            
            # Limitar a pontuação a 10
            virality_score = min(10, virality_score)
            
            return virality_score
            
        except Exception as e:
            logger.error(f"Erro ao calcular pontuação de viralidade: {str(e)}")
            return 0
    
    def calculate_all_metrics(self, video: Dict) -> Dict:
        """
        Calcula todas as métricas para um vídeo.
        
        Args:
            video: Dicionário com dados do vídeo
            
        Returns:
            Dicionário com todas as métricas calculadas
        """
        # Calcular métricas
        views_per_hour = self.calculate_views_per_hour(video)
        engagement_rate = self.calculate_engagement_rate(video)
        virality_score = self.calculate_virality_score(video)
        
        # Extrair duração
        duration = video.get('contentDetails', {}).get('duration', '') if 'contentDetails' in video else video.get('duration', '')
        
        # Retornar todas as métricas
        return {
            'views_per_hour': views_per_hour,
            'engagement_rate': engagement_rate,
            'virality_score': virality_score,
            'duration': duration
        }
    
    def process_videos_dataframe(self, videos_df: pd.DataFrame) -> pd.DataFrame:
        """
        Processa um DataFrame de vídeos, calculando métricas para cada um.
        
        Args:
            videos_df: DataFrame com dados dos vídeos
            
        Returns:
            DataFrame com métricas adicionadas
        """
        if videos_df.empty:
            return videos_df
        
        # Inicializar colunas de métricas
        videos_df['views_per_hour'] = 0.0
        videos_df['engagement_rate'] = 0.0
        videos_df['virality_score'] = 0.0
        
        # Calcular métricas para cada vídeo
        for index, row in videos_df.iterrows():
            # Converter row para dicionário
            video_dict = row.to_dict()
            
            # Calcular métricas
            metrics = self.calculate_all_metrics(video_dict)
            
            # Atualizar DataFrame
            videos_df.at[index, 'views_per_hour'] = metrics['views_per_hour']
            videos_df.at[index, 'engagement_rate'] = metrics['engagement_rate']
            videos_df.at[index, 'virality_score'] = metrics['virality_score']
        
        logger.info(f"Calculadas métricas para {len(videos_df)} vídeos")
        
        return videos_df
