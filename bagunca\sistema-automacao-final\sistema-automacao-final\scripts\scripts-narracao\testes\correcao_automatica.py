#!/usr/bin/env python3
"""
Correção Automática - Voice_ID Inválido

Este script automaticamente:
1. Corrige o voice_id inválido na configuração
2. Testa com voice_id válido 
3. Atualiza a configuração com valores funcionando
4. Executa teste final
"""

import json
import requests
from pathlib import Path
import shutil

def backup_config():
    """Faz backup da configuração atual."""
    config_path = Path("config/config_narracao_com_proxy.json")
    backup_path = Path("config/config_narracao_com_proxy.json.backup")
    
    if config_path.exists():
        shutil.copy2(config_path, backup_path)
        print(f"[BACKUP] Configuração original salva: {backup_path}")
        return True
    return False

def fix_config_immediately():
    """Corrige imediatamente a configuração com voice_id válido."""
    config_path = Path("config/config_narracao_com_proxy.json")
    
    if not config_path.exists():
        print(f"[ERRO] Configuração não encontrada: {config_path}")
        return False
    
    # Lê configuração atual
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print(f"[ANTES] Voice ID atual: {config.get('voice', {}).get('voice_id', 'N/A')}")
    
    # CORREÇÃO: Atualiza com voice_id que sabemos que existe (baseado em documentação ElevenLabs)
    config["voice"]["voice_id"] = "pNInz6obpgDQGcFmaJgB"  # Adam (voice padrão que existe)
    config["voice"]["voice_name"] = "Adam"
    config["voice"]["model_id"] = "eleven_multilingual_v2"
    config["voice"]["output_format"] = "mp3_22050_32"
    
    # Salva configuração corrigida
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"[CORRIGIDO] Voice ID alterado para: {config['voice']['voice_id']}")
    print(f"[CORRIGIDO] Modelo alterado para: {config['voice']['model_id']}")
    print(f"[CORRIGIDO] Formato alterado para: {config['voice']['output_format']}")
    
    return True

def test_corrected_config():
    """Testa a configuração corrigida."""
    print(f"\n[TESTE] Testando configuração corrigida...")
    
    # Lê primeira chave
    keys_path = Path("config/chaves-api-elevenlabs.txt")
    
    if not keys_path.exists():
        print(f"[ERRO] Chaves não encontradas: {keys_path}")
        return False
    
    with open(keys_path, 'r') as f:
        api_key = f.readline().strip()
    
    if not api_key:
        print(f"[ERRO] Nenhuma chave no arquivo")
        return False
    
    # Lê configuração corrigida
    config_path = Path("config/config_narracao_com_proxy.json")
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    voice_id = config["voice"]["voice_id"]
    model_id = config["voice"]["model_id"]
    output_format = config["voice"]["output_format"]
    
    print(f"[TESTE] Chave: {api_key[:8]}...")
    print(f"[TESTE] Voice: {voice_id}")
    print(f"[TESTE] Model: {model_id}")
    print(f"[TESTE] Format: {output_format}")
    
    # Testa TTS
    headers = {
        "xi-api-key": api_key,
        "Content-Type": "application/json",
        "Accept": "audio/mpeg"
    }
    
    payload = {
        "text": "Teste de configuração corrigida.",
        "model_id": model_id,
        "voice_settings": {
            "stability": 0.5,
            "similarity_boost": 0.5
        }
    }
    
    try:
        response = requests.post(
            f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}",
            headers=headers,
            params={"output_format": output_format},
            json=payload,
            timeout=30
        )
        
        print(f"[TESTE] Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"[SUCESSO] ✅ Configuração corrigida funciona!")
            print(f"[INFO] Áudio gerado: {len(response.content)} bytes")
            return True
        else:
            print(f"[FALHA] ❌ Ainda com erro: {response.status_code}")
            try:
                error_data = response.json()
                print(f"[DETALHE] {error_data}")
            except:
                print(f"[DETALHE] {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"[ERRO] Exceção no teste: {e}")
        return False

def try_multiple_voice_ids():
    """Tenta vários voice_ids válidos conhecidos."""
    print(f"\n[BACKUP] Tentando voice_ids alternativos...")
    
    # Voice IDs que existem (baseado na documentação ElevenLabs)
    voice_options = [
        {"id": "pNInz6obpgDQGcFmaJgB", "name": "Adam"},
        {"id": "yoZ06aMxZJJ28mfd3POQ", "name": "Sam"},  
        {"id": "EXAVITQu4vr4xnSDxMaL", "name": "Bella"},
        {"id": "ErXwobaYiN019PkySvjV", "name": "Antoni"},
        {"id": "VR6AewLTigWG4xSOukaG", "name": "Arnold"},
        {"id": "MF3mGyEYCl7XYWbV9V6O", "name": "Elli"},
        {"id": "TxGEqnHWrfWFTfGW9XjX", "name": "Josh"},
        {"id": "AZnzlk1XvdvUeBnXmlld", "name": "Domi"},
    ]
    
    keys_path = Path("config/chaves-api-elevenlabs.txt")
    with open(keys_path, 'r') as f:
        api_key = f.readline().strip()
    
    headers = {
        "xi-api-key": api_key,
        "Content-Type": "application/json",
        "Accept": "audio/mpeg"
    }
    
    for voice in voice_options:
        print(f"[TESTE] Tentando {voice['name']} ({voice['id'][:8]}...)...")
        
        payload = {
            "text": "Teste",
            "model_id": "eleven_multilingual_v2",
            "voice_settings": {"stability": 0.5, "similarity_boost": 0.5}
        }
        
        try:
            response = requests.post(
                f"https://api.elevenlabs.io/v1/text-to-speech/{voice['id']}",
                headers=headers,
                params={"output_format": "mp3_22050_32"},
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"[SUCESSO] ✅ {voice['name']} funciona!")
                
                # Atualiza configuração com voice funcionando
                config_path = Path("config/config_narracao_com_proxy.json")
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                config["voice"]["voice_id"] = voice['id']
                config["voice"]["voice_name"] = voice['name']
                
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                print(f"[ATUALIZADO] Configuração atualizada para {voice['name']}")
                return True
            else:
                print(f"[FALHA] {response.status_code}")
                
        except Exception as e:
            print(f"[ERRO] {e}")
            continue
    
    return False

def main():
    """Executa correção automática completa."""
    print("=" * 60)
    print("CORREÇÃO AUTOMÁTICA - VOICE_ID INVÁLIDO")
    print("=" * 60)
    
    # 1. Backup da configuração
    backup_config()
    
    # 2. Corrige configuração
    if not fix_config_immediately():
        print("[ERRO] Falha ao corrigir configuração")
        return 1
    
    # 3. Testa configuração corrigida
    if test_corrected_config():
        print(f"\n🎉 SUCESSO! Configuração corrigida e testada!")
        print(f"\n✅ EXECUTE AGORA:")
        print(f"   python narrador_com_proxy_corrigido.py")
        return 0
    
    # 4. Se ainda falhou, tenta voice_ids alternativos
    print(f"\n[BACKUP] Primeira correção falhou, tentando alternativas...")
    if try_multiple_voice_ids():
        print(f"\n🎉 SUCESSO! Voice alternativo encontrado!")
        print(f"\n✅ EXECUTE AGORA:")
        print(f"   python narrador_com_proxy_corrigido.py")
        return 0
    
    # 5. Se nada funcionou
    print(f"\n❌ FALHA: Nenhum voice_id funcionou")
    print(f"\nPossíveis problemas:")
    print(f"  1. Contas ElevenLabs com limitações severas")
    print(f"  2. Chaves API expiradas/inválidas")
    print(f"  3. Mudanças na estrutura da API")
    
    print(f"\n🔍 NEXT STEPS:")
    print(f"  1. Verifique https://elevenlabs.io/")
    print(f"  2. Teste com conta paga")
    print(f"  3. Gere novas chaves API")
    
    return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
