# --- Start of file ---
import os
import sys
import logging
import re
from pathlib import Path
import argparse # Use argparse for better argument handling
import json # For logging status summary

# Importa os módulos desenvolvidos
from config_manager import ConfigManager
from api_key_manager import APIKeyManager # Importa a versão aprimorada
from elevenlabs_api_atualizado import ElevenLabsAPI # Importa a versão aprimorada
from processador_arquivos_atualizado import ProcessadorArquivos # Importa a versão aprimorada

# --- Define Project Root ---
SCRIPT_DIR = Path(__file__).resolve().parent
# Assume que a estrutura é <raiz>/scripts/scripts-narracao/narrador_roteiros_final.py
# Portanto, a raiz está dois nível acima de SCRIPT_DIR
PROJECT_ROOT = SCRIPT_DIR.parent.parent

# --- Configuração Inicial ---
# Caminho relativo para o arquivo de configuração principal
CONFIG_FILE_PATH = PROJECT_ROOT / "config" / "config_narracao.json"
config_manager = ConfigManager(CONFIG_FILE_PATH)

# --- Configuração de Logging ---
LOG_DIR = PROJECT_ROOT / "logs"
LOG_DIR.mkdir(parents=True, exist_ok=True) # Garante que o diretório de logs exista
log_file_name = config_manager.get_config("paths", "log_file") or "narracao_v2.log" # Nome diferente para log
log_file_path = LOG_DIR / log_file_name

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file_path), # Use o caminho Path
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("NarradorRoteirosV2") # Nome do logger atualizado

# --- Função clean_text (mantida) ---
def clean_text(raw_text):
    """Limpa o texto removendo símbolos e linhas indesejadas."""
    # Lógica mantida da versão anterior
    cleaned_lines = []
    for line in raw_text.splitlines():
        if re.match(r"^CENA \d+:|^ATO \d+:|^\*\*.+\*\*", line.strip(), re.IGNORECASE):
            cleaned_lines.append(line.strip())
            continue
        if line.strip().startswith(("#", "-")) or re.match(r"^\d+\. ", line.strip()):
             continue
        line = line.replace("*", "")
        cleaned_lines.append(line.strip())
    return "\n".join(filter(None, cleaned_lines))

# --- Função main ---
def main():
    """Função principal do script (versão atualizada)."""

    # --- Argument Parsing ---
    parser = argparse.ArgumentParser(description="Automatiza a narração de roteiros usando ElevenLabs com gerenciamento de API aprimorado.")
    # Diretórios relativos à raiz do projeto (PROJECT_ROOT)
    diretorio_roteiros_default = PROJECT_ROOT / "roteiros_gerados"
    diretorio_saida_default = PROJECT_ROOT / "narracao"
    arquivo_chaves_default = PROJECT_ROOT / "config" / "chaves-api-elevenlabs.txt"

    parser.add_argument(
        "--output",
        type=Path,
        default=diretorio_saida_default,
        help=f"Diretório de saída para os áudios gerados e relatório. Padrão: {diretorio_saida_default}"
    )
    parser.add_argument(
        "--keys",
        type=Path,
        default=arquivo_chaves_default,
        help=f"Caminho para o arquivo de chaves da API ElevenLabs. Padrão: {arquivo_chaves_default}"
    )
    # REMOVIDO: Argumento --usage não é mais necessário
    # parser.add_argument(
    #     "--usage",
    #     type=Path,
    #     default=None, # Não há mais padrão
    #     help="[REMOVIDO] Caminho para o arquivo JSON de controle de uso das chaves API."
    # )

    args = parser.parse_args()

    diretorio_roteiros = diretorio_roteiros_default # Entrada fixa por enquanto
    diretorio_saida = args.output.resolve() # Garante caminho absoluto
    arquivo_chaves = args.keys.resolve()
    # REMOVIDO: usage_data_path não é mais usado
    # usage_data_path = args.usage.resolve() if args.usage else None

    diretorio_saida.mkdir(parents=True, exist_ok=True)

    logger.info(f"=== Iniciando Narração Automática V2 ===")
    logger.info(f"Raiz do Projeto: {PROJECT_ROOT}")
    logger.info(f"Diretório de Roteiros (Entrada): {diretorio_roteiros}")
    logger.info(f"Diretório de Saída: {diretorio_saida}")
    logger.info(f"Arquivo de Chaves API: {arquivo_chaves}")
    # REMOVIDO: Log do usage_data_path
    # if usage_data_path:
    #     logger.info(f"Arquivo de Uso de Chaves (IGNORADO): {usage_data_path}")

    # Verifica se o arquivo de chaves existe
    if not arquivo_chaves.is_file():
        logger.error(f"Arquivo de chaves não encontrado: {arquivo_chaves}")
        print(f"ERRO: Arquivo de chaves não encontrado: {arquivo_chaves}")
        return 1

    # Verifica se o diretório de roteiros existe
    if not diretorio_roteiros.is_dir():
        logger.error(f"Diretório de roteiros não encontrado: {diretorio_roteiros}")
        print(f"ERRO: Diretório de roteiros não encontrado: {diretorio_roteiros}")
        return 1

    try:
        # --- Obtenção de Configurações ---
        api_config = config_manager.get_config("api") or {}
        voice_config = config_manager.get_config("voice") or {}
        processing_config = config_manager.get_config("processing") or {}
        
        base_url = api_config.get("base_url", "https://api.elevenlabs.io")
        max_retries = api_config.get("max_retries", 3)
        retry_delay = api_config.get("retry_delay", 5) # Usando o delay maior da API atualizada
        voice_id = voice_config.get("voice_id", "21m00Tcm4TlvDq81kWAM")
        model_id = voice_config.get("model_id", "eleven_multilingual_v2") # Modelo padrão atualizado
        output_format = voice_config.get("output_format", "mp3_44100_128")
        # Parâmetros do ProcessadorArquivos atualizado
        tamanho_minimo_bloco = processing_config.get("tamanho_minimo_bloco", 300) # NOVO, com default
        tamanho_maximo_bloco = processing_config.get("tamanho_maximo_bloco", 2300) # NOVO, com default
        min_block_fallback = processing_config.get("min_block_size_on_fallback", 50)

        # --- Inicialização dos Módulos (Atualizado) ---
        logger.info("Inicializando gerenciador de chaves API (v2)...")
        # Inicialização SIMPLIFICADA: Apenas o caminho do arquivo de chaves é necessário
        api_key_manager = APIKeyManager(keys_file_path=str(arquivo_chaves))

        # Log do status inicial das chaves (usando o novo método)
        try:
            initial_status = api_key_manager.get_status_summary()
            logger.info(f"Status inicial das chaves (após sincronização):\n{json.dumps(initial_status, indent=2)}")
            # Contagem de chaves válidas (exemplo: com saldo > 0)
            available_count = sum(1 for status in initial_status.values() if isinstance(status.get('remaining'), int) and status['remaining'] > 0)
            total_keys = len(api_key_manager.api_keys)
            logger.info(f"Chaves com saldo > 0: {available_count}/{total_keys}")
        except Exception as status_err:
             logger.warning(f"Não foi possível obter/logar o status inicial das chaves: {status_err}")

        # REMOVIDO: Chamadas aos métodos antigos get_usage_stats e get_available_keys_count
        # stats = api_key_manager.get_usage_stats()
        # chaves_disponiveis = api_key_manager.get_available_keys_count()
        # logger.info(f"Chaves disponíveis: {chaves_disponiveis}/{len(api_key_manager.api_keys)}")

        logger.info("Inicializando cliente da API do ElevenLabs (v2)...")
        elevenlabs_api = ElevenLabsAPI(
            api_key_manager=api_key_manager,
            base_url=base_url,
            model_id=model_id,
            voice_id=voice_id,
            output_format=output_format,
            max_retries=max_retries,
            retry_delay=retry_delay
        )

        logger.info("Inicializando processador de arquivos (v2)...")
        # Inicialização ATUALIZADA do ProcessadorArquivos
        processador = ProcessadorArquivos(
            diretorio_roteiros=diretorio_roteiros,
            diretorio_saida=diretorio_saida,
            tamanho_minimo_bloco=tamanho_minimo_bloco, # NOVO
            tamanho_maximo_bloco=tamanho_maximo_bloco, # NOVO
            api_key_manager=api_key_manager,
            elevenlabs_api=elevenlabs_api,
            modelo_id=model_id,
            min_block_size_on_fallback=min_block_fallback
        )

        # --- Processamento ---
        logger.info("Iniciando processamento adaptativo de arquivos...")
        resultados = processador.processar_todos_arquivos()

        # --- Resumo e Finalização ---
        sucessos = sum(1 for v in resultados.values() if isinstance(v, Path) and v.exists())
        erros = len(resultados) - sucessos

        logger.info(f"Processamento concluído. {len(resultados)} arquivos processados.")
        logger.info(f"Sucessos: {sucessos}")
        logger.info(f"Erros: {erros}")

        # Usa o nome do relatório definido no processador (relatorio_processamento_v2.txt)
        caminho_relatorio = diretorio_saida / 'relatorio_processamento_v2.txt'

        print(f"\nProcessamento concluído!")
        print(f"Total de arquivos processados: {len(resultados)}")
        print(f"Sucessos: {sucessos}")
        print(f"Erros: {erros}")
        print(f"\nOs áudios gerados estão disponíveis em: {diretorio_saida}")
        if caminho_relatorio.exists():
            print(f"Um relatório detalhado foi gerado em: {caminho_relatorio}")
        else:
            print("Não foi possível gerar o relatório de processamento.")

        return 0

    except Exception as e:
        logger.exception("Erro fatal durante a execução:")
        print(f"ERRO FATAL: {str(e)}")
        return 1

# --- Bloco Principal ---
if __name__ == "__main__":
    sys.exit(main())

