# --- Start of file ---
"""
Módulo de Processamento de Arquivos para Narração (Versão Melhorada)

Responsável por:
1. Encontrar arquivos .txt no diretório de roteiros.
2. Limpar tags de bloco do texto.
3. Sincronizar status das chaves API antes de processar cada arquivo.
4. Processar o texto de forma adaptativa, ajustando o tamanho dos blocos
   com base na disponibilidade real de caracteres da chave API selecionada.
5. Chamar a API ElevenLabs para gerar áudio por bloco adaptativo.
6. Concatenar blocos de áudio.
7. Gerar relatório.
"""

import os
import re
import time
import shutil
from typing import List, Dict, Tuple, Optional, Union
from pathlib import Path
import logging
import math

# Importa os módulos necessários (assumindo que estão no mesmo diretório ou PYTHONPATH)
from api_key_manager import APIKeyManager
from elevenlabs_api_atualizado import ElevenLabsAPI

# --- Define Project Root (similar ao script principal) ---
SCRIPT_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = SCRIPT_DIR.parent # Ajustado - assumindo que scripts-narracao está na raiz

# --- Configuração de Logging ---
logger = logging.getLogger("NarradorRoteiros.ProcessadorArquivos")

class ProcessadorArquivos:
    """
    Processa arquivos de texto (.txt) da pasta de roteiros para conversão em áudio,
    utilizando gerenciamento de chave API aprimorado e processamento de blocos adaptativo.
    """

    def __init__(self,
                 diretorio_roteiros: Path,
                 diretorio_saida: Path,
                 tamanho_minimo_bloco: int, # NOVO
                 tamanho_maximo_bloco: int, # Renomeado
                 api_key_manager: APIKeyManager,
                 elevenlabs_api: ElevenLabsAPI,
                 modelo_id: str,
                 min_block_size_on_fallback: int = 50):
        """
        Inicializa o processador de arquivos.

        Args:
            diretorio_roteiros: Path para o diretório de entrada.
            diretorio_saida: Path para o diretório de saída.
            tamanho_minimo_bloco: Tamanho mínimo desejado para um bloco.
            tamanho_maximo_bloco: Tamanho máximo desejado para um bloco (teto).
            api_key_manager: Instância do APIKeyManager aprimorado.
            elevenlabs_api: Instância do cliente da API do ElevenLabs aprimorado.
            modelo_id: ID do modelo ElevenLabs a ser utilizado.
            min_block_size_on_fallback: Tamanho mínimo aceitável para um bloco em caso de fallback.
        """
        self.diretorio_roteiros = diretorio_roteiros
        self.diretorio_saida = diretorio_saida
        self.tamanho_minimo_bloco = tamanho_minimo_bloco # NOVO
        self.tamanho_maximo_bloco = tamanho_maximo_bloco # Renomeado
        self.api_key_manager = api_key_manager
        self.elevenlabs_api = elevenlabs_api
        self.modelo_id = modelo_id
        self.min_block_size_on_fallback = min_block_size_on_fallback
        
        self.diretorio_saida.mkdir(parents=True, exist_ok=True)
        logger.info(f"Processador (v2) inicializado. Entrada: {self.diretorio_roteiros}, Saída: {self.diretorio_saida}, Bloco Min: {self.tamanho_minimo_bloco}, Bloco Máx: {self.tamanho_maximo_bloco}")

    def detectar_idioma_por_caminho(self, caminho_arquivo: Path) -> Optional[str]:
        """Detecta o idioma com base na estrutura de pastas."""
        # Lógica mantida da versão anterior
        if caminho_arquivo.parent == self.diretorio_roteiros:
            logger.debug(f"Idioma 'pt-BR' assumido para {caminho_arquivo.name}") # Usar pt-BR?
            return "pt-BR"
        try:
            relative_path = caminho_arquivo.relative_to(self.diretorio_roteiros)
            if len(relative_path.parts) > 1:
                codigo_idioma = relative_path.parts[0]
                # Validar se é um código válido (ex: 'en', 'es')
                if codigo_idioma in self.elevenlabs_api.LANGUAGE_CODES: # Verifica no mapeamento da API
                     logger.info(f"Idioma detectado pela pasta '{codigo_idioma}' para {caminho_arquivo.name}")
                     return codigo_idioma
                else:
                     logger.warning(f"Pasta '{codigo_idioma}' não corresponde a um código de idioma conhecido. Usando padrão.")
                     return "pt-BR" # Ou None?
        except ValueError:
             pass
        logger.warning(f"Não foi possível detectar o idioma para: {caminho_arquivo}. Usando padrão 'pt-BR'.")
        return "pt-BR"

    def encontrar_arquivos_txt(self) -> List[Path]:
        """Encontra todos os arquivos .txt recursivamente."""
        # Lógica mantida da versão anterior
        if not self.diretorio_roteiros.is_dir():
            logger.warning(f"Diretório de roteiros não encontrado: {self.diretorio_roteiros}")
            return []
        arquivos_txt = list(self.diretorio_roteiros.rglob("*.txt"))
        logger.info(f"Encontrados {len(arquivos_txt)} arquivos .txt em {self.diretorio_roteiros}")
        return arquivos_txt

    def _limpar_tags_bloco(self, texto: str) -> str:
        """Remove as tags de bloco do texto."""
        # Lógica mantida da versão anterior
        padrao_tags = r"^(?:--- BLOCO \d+ ---|### BLOCO \d+:.*|--- FIM DO BLOCO \d+ ---)\s*$"
        texto_sem_tags = re.sub(padrao_tags, '', texto, flags=re.MULTILINE)
        texto_final = re.sub(r'^\s*\n', '', texto_sem_tags, flags=re.MULTILINE)
        logger.debug("Tags de bloco removidas do texto.")
        return texto_final.strip()

    def _find_best_split_point(self, text: str, min_len: int, max_len: int) -> int:
        """
        Encontra o melhor ponto para quebrar o texto dentro do intervalo [min_len, max_len],
        priorizando finais de sentença (., !, ?), depois vírgulas, depois espaços.
        A busca é feita do fim para o começo do intervalo para priorizar blocos maiores.
        Retorna o índice onde o texto deve ser quebrado (o caractere neste índice será o último do bloco).
        """
        text_len = len(text)
        # Ajusta os limites para não exceder o tamanho real do texto
        actual_max_len = min(max_len, text_len)
        actual_min_len = min(min_len, actual_max_len) # min_len não pode ser maior que max_len ajustado

        # Se o texto restante já for menor ou igual ao mínimo desejado, retorna o fim do texto
        if text_len <= actual_min_len:
            return text_len - 1

        # Define a fatia de busca (do min ao max)
        search_slice = text[actual_min_len:actual_max_len]
        slice_len = len(search_slice)

        if slice_len <= 0:
            # Caso extremo: min_len e max_len são iguais ou inválidos
            # Retorna o ponto máximo possível ajustado
            return actual_max_len - 1

        # 1. Procura pelo ÚLTIMO final de sentença (., !, ?) na fatia
        # Ajusta a regex para encontrar a pontuação seguida por espaço ou fim de string
        # Precisamos iterar de trás para frente ou usar rfind/rsearch
        best_split = -1
        for match in reversed(list(re.finditer(r'[.!?](?=\s|$)', search_slice))):
            best_split = match.start() + actual_min_len # Ajusta índice para o texto original
            logger.debug(f"_find_best_split_point: Encontrado fim de sentença em {best_split}")
            return best_split

        # 2. Procura pela ÚLTIMA vírgula na fatia
        last_comma = search_slice.rfind(',')
        if last_comma != -1:
            best_split = last_comma + actual_min_len # Ajusta índice
            logger.debug(f"_find_best_split_point: Encontrada vírgula em {best_split}")
            return best_split

        # 3. Procura pelo ÚLTIMO espaço na fatia
        last_space = search_slice.rfind(' ')
        if last_space != -1:
            best_split = last_space + actual_min_len # Ajusta índice
            logger.debug(f"_find_best_split_point: Encontrado espaço em {best_split}")
            return best_split

        # 4. Fallback: Se nenhum ponto ideal foi encontrado DENTRO do intervalo [min_len, max_len]
        # retorna o índice correspondente ao limite máximo permitido (actual_max_len - 1)
        logger.warning(f"_find_best_split_point: Nenhum ponto de quebra ideal encontrado entre {actual_min_len} e {actual_max_len}. Usando limite máximo.")
        return actual_max_len - 1

    def processar_arquivo(self, caminho_arquivo: Path) -> Union[Path, str]:
        """
        Processa um único arquivo de texto, convertendo-o em áudio de forma adaptativa.

        Args:
            caminho_arquivo: Path para o arquivo de texto.

        Returns:
            Path para o arquivo de áudio final gerado ou string de erro.
        """
        logger.info(f"Processando arquivo (v2): {caminho_arquivo.name}")
        dir_temp = None # Inicializa para o bloco finally
        
        try:
            # 1. Sincronizar Status das Chaves (conforme relatório)
            # A frequência pode ser configurada (ex: 'per_file' vs 'startup')
            # Aqui, sincronizamos antes de cada arquivo.
            logger.info("Sincronizando status das chaves API...")
            try:
                # Usar force_sync=False para respeitar o cache de tempo, a menos que necessário
                self.api_key_manager.sync_all_keys_usage(force_sync=False)
            except Exception as sync_err:
                logger.error(f"Falha crítica ao sincronizar chaves API: {sync_err}. Abortando processamento do arquivo.")
                return f"ERRO: Falha na sincronização das chaves API"
            logger.info("Sincronização concluída.")
            # Opcional: Logar resumo do status das chaves
            # logger.debug(f"Status atual das chaves: {json.dumps(self.api_key_manager.get_status_summary(), indent=2)}")

            # 2. Ler e Preparar Texto
            codigo_idioma = self.detectar_idioma_por_caminho(caminho_arquivo)
            texto_original = caminho_arquivo.read_text(encoding='utf-8')
            texto_limpo = self._limpar_tags_bloco(texto_original)
            
            if not texto_limpo:
                logger.warning(f"Arquivo {caminho_arquivo.name} vazio após limpeza.")
                return f"ERRO: Arquivo vazio ou inválido após limpeza"

            # 3. Preparar Diretório Temporário
            nome_base = caminho_arquivo.stem
            dir_temp = self.diretorio_saida / f"temp_{nome_base}_{int(time.time())}"
            dir_temp.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Diretório temporário criado: {dir_temp}")

            # 4. Loop de Processamento Adaptativo
            arquivos_audio_blocos = []
            texto_restante = texto_limpo
            bloco_index = 0
            sucesso_geral = True

            while texto_restante:
                bloco_index += 1
                logger.info(f"--- Preparando Bloco {bloco_index} para {nome_base} ---")
                available_text_len = len(texto_restante)
                
                # Define os tamanhos alvo para este bloco
                target_min = self.tamanho_minimo_bloco
                target_max = self.tamanho_maximo_bloco

                # Lógica para último bloco potencialmente menor que o mínimo
                if available_text_len < target_min:
                    logger.warning(f"Texto restante ({available_text_len}) menor que o bloco mínimo ({target_min}). Processando como último bloco.")
                    required_chars_for_key_check = available_text_len
                    effective_min_split = 0 # Processa tudo que sobrou
                    effective_max_split = available_text_len
                    # Usa min_block_size_on_fallback como mínimo se for relevante e texto for pequeno
                    minimum_required_for_block = min(self.min_block_size_on_fallback, 1) if available_text_len < self.min_block_size_on_fallback else 1
                else: # Lógica para bloco normal
                    required_chars_for_key_check = min(available_text_len, target_max) # Verifica chave para o máximo desejado
                    effective_min_split = target_min
                    effective_max_split = min(available_text_len, target_max)
                    minimum_required_for_block = target_min # Precisa de saldo para o mínimo

                logger.debug(f"Intervalo alvo: [{target_min}, {target_max}], Texto disponível: {available_text_len}")
                logger.debug(f"Verificando chave para {required_chars_for_key_check} caracteres. Intervalo busca inicial: [{effective_min_split}, {effective_max_split}]")

                try:
                    # Obtém a melhor chave e seu saldo REAL (baseado na última sync ou dedução local)
                    api_key, saldo_real = self.api_key_manager.get_available_key(required_chars=required_chars_for_key_check)
                    logger.info(f"Chave selecionada: {api_key[:8]}... com {saldo_real} caracteres restantes (para verificar {required_chars_for_key_check}).")

                    # Verifica se o saldo da chave é suficiente para o bloco mínimo necessário
                    max_possible_len_with_key = min(available_text_len, saldo_real)
                    
                    if max_possible_len_with_key < minimum_required_for_block:
                        logger.error(f"Saldo da chave ({saldo_real}) insuficiente para o bloco mínimo necessário ({minimum_required_for_block}). Texto disponível: {available_text_len}. Abortando arquivo.")
                        sucesso_geral = False
                        break

                    # Ajusta o intervalo final de busca com base no saldo da chave
                    final_min_split = effective_min_split
                    # O teto é limitado pelo saldo E pelo tamanho máximo do bloco
                    final_max_split = min(effective_max_split, max_possible_len_with_key)

                    # Garante que min não seja maior que max após ajuste de saldo
                    # E ajusta min se for o último bloco pequeno
                    if available_text_len < target_min:
                        final_min_split = 0 # Garante que o último bloco pequeno use o início
                    elif final_min_split > final_max_split:
                         # Isso pode acontecer se o saldo for menor que target_min mas maior que min_block_size_on_fallback
                         logger.warning(f"Intervalo de busca inválido após ajuste de saldo [{final_min_split}, {final_max_split}]. Usando fallback para {max_possible_len_with_key} caracteres.")
                         # Neste caso, tentamos quebrar no máximo possível permitido pelo saldo
                         # A busca deve ser de 1 até o limite do saldo, não min_len até saldo
                         split_index = self._find_best_split_point(texto_restante, 1, max_possible_len_with_key)
                         tamanho_real_bloco = split_index + 1
                         logger.info(f"Ponto de quebra (fallback saldo): {split_index}. Tamanho real: {tamanho_real_bloco}")
                         # Pula a busca normal abaixo
                         bloco_atual = texto_restante[:tamanho_real_bloco].strip()
                         # Continua para a chamada da API (código duplicado abaixo, precisa refatorar ou pular para lá)
                    else:
                        logger.debug(f"Intervalo final de busca (considerando saldo): [{final_min_split}, {final_max_split}]")
                        split_index = self._find_best_split_point(texto_restante, final_min_split, final_max_split)
                        tamanho_real_bloco = split_index + 1
                        logger.info(f"Ponto de quebra encontrado em {split_index}. Tamanho real do bloco: {tamanho_real_bloco}")
                        bloco_atual = texto_restante[:tamanho_real_bloco].strip()

                    # Extrai o bloco de texto real (já feito acima nos dois casos)
                    # bloco_atual = texto_restante[:tamanho_real_bloco].strip()
                    if not bloco_atual:
                         logger.warning("Bloco atual ficou vazio após ajuste e strip. Pulando.")
                         texto_restante = texto_restante[tamanho_real_bloco:] # Avança mesmo assim
                         continue

                    logger.info(f"Processando Bloco {bloco_index} ({len(bloco_atual)} caracteres) com chave {api_key[:8]}...")
                    caminho_audio_bloco = dir_temp / f"bloco_{bloco_index}.mp3"

                    # Chama a API para gerar o áudio do bloco atual, PASSANDO A CHAVE
                    self.elevenlabs_api.text_to_speech(
                        text=bloco_atual,
                        output_path=str(caminho_audio_bloco),
                        model_id=self.modelo_id,
                        language_code=codigo_idioma,
                        api_key=api_key # Passa a chave explicitamente
                    )
                    arquivos_audio_blocos.append(caminho_audio_bloco)
                    logger.debug(f"Bloco {bloco_index} gerado: {caminho_audio_bloco.name}")

                    # ---> DEDUZ O USO LOCALMENTE <--- 
                    self.api_key_manager.deduct_usage(api_key=api_key, characters_used=len(bloco_atual))
                    # ---> FIM DA DEDUÇÃO <--- 

                    # Atualiza o texto restante
                    texto_restante = texto_restante[tamanho_real_bloco:]
                    logger.debug(f"Caracteres restantes no arquivo: {len(texto_restante)}")
                    
                    # Pausa opcional entre chamadas API
                    time.sleep(0.8) # Um pouco mais de pausa pode ser bom

                except ValueError as key_err:
                    # Erro do APIKeyManager (nenhuma chave utilizável)
                    logger.error(f"Erro fatal do APIKeyManager ao obter chave para bloco {bloco_index}: {key_err}")
                    sucesso_geral = False
                    break # Interrompe o processamento deste arquivo
                except Exception as api_err:
                    # Erro da API ElevenLabs durante text_to_speech ou outro erro inesperado
                    logger.error(f"Erro ao processar bloco {bloco_index} de {nome_base}: {api_err}", exc_info=True) # Log com traceback
                    sucesso_geral = False
                    break 

            # 5. Finalização e Concatenação
            if not sucesso_geral:
                logger.warning(f"Processamento de {nome_base} interrompido devido a erro.")
                # Limpa o temp e retorna erro (feito no finally)
                return f"ERRO: Falha durante o processamento adaptativo dos blocos"

            if not arquivos_audio_blocos:
                 logger.warning(f"Nenhum bloco de áudio foi gerado para {nome_base} apesar do loop ter concluído.")
                 return f"ERRO: Nenhum bloco de áudio gerado"

            caminho_audio_final = self.diretorio_saida / f"{nome_base}.mp3"
            
            if len(arquivos_audio_blocos) == 1:
                shutil.move(str(arquivos_audio_blocos[0]), str(caminho_audio_final))
                logger.info(f"Áudio de bloco único movido para: {caminho_audio_final}")
            else:
                logger.info(f"Concatenando {len(arquivos_audio_blocos)} blocos para {caminho_audio_final.name}...")
                try:
                    self.elevenlabs_api.concatenate_audio_files(
                        [str(p) for p in arquivos_audio_blocos], 
                        str(caminho_audio_final)
                    )
                    logger.info(f"Áudio final concatenado com sucesso: {caminho_audio_final}")
                except Exception as concat_err:
                    logger.error(f"Erro ao concatenar áudio para {nome_base}: {concat_err}")
                    # Limpa o temp e retorna erro (feito no finally)
                    return f"ERRO: Falha ao concatenar blocos de áudio"
            
            return caminho_audio_final # Retorna o Path do arquivo final
            
        except Exception as e:
            logger.exception(f"Erro inesperado ao processar arquivo {caminho_arquivo.name}:")
            return f"ERRO GERAL: {str(e)}"
        finally:
            # Garante a limpeza do diretório temporário
            if dir_temp and dir_temp.exists():
                try:
                    shutil.rmtree(dir_temp)
                    logger.debug(f"Diretório temporário removido: {dir_temp}")
                except Exception as clean_err:
                    logger.error(f"Erro ao remover diretório temporário {dir_temp}: {clean_err}")

    def processar_todos_arquivos(self) -> Dict[Path, Union[Path, str]]:
        """
        Processa todos os arquivos de texto encontrados no diretório de roteiros.

        Returns:
            Dicionário mapeando Paths de arquivos de texto para Paths de áudio ou strings de erro.
        """
        arquivos_txt = self.encontrar_arquivos_txt()
        if not arquivos_txt:
            logger.warning("Nenhum arquivo .txt encontrado para processar.")
            return {}
            
        resultados: Dict[Path, Union[Path, str]] = {}
        total_arquivos = len(arquivos_txt)
        
        logger.info(f"Iniciando processamento adaptativo de {total_arquivos} arquivos...")
        
        for i, arquivo_path in enumerate(arquivos_txt):
            logger.info(f"--- Processando arquivo {i+1}/{total_arquivos}: {arquivo_path.name} ---")
            resultado = self.processar_arquivo(arquivo_path)
            resultados[arquivo_path] = resultado
            
            if isinstance(resultado, Path):
                logger.info(f"Arquivo {arquivo_path.name} processado com sucesso -> {resultado.name}")
            else:
                logger.error(f"Falha ao processar {arquivo_path.name}: {resultado}")
            logger.info(f"--- Fim do processamento do arquivo {i+1}/{total_arquivos} ---")

        self._gerar_relatorio(resultados)
        logger.info("Processamento adaptativo de todos os arquivos concluído.")
        return resultados

    def _gerar_relatorio(self, resultados: Dict[Path, Union[Path, str]]) -> None:
        """
        Gera um relatório de processamento na pasta de saída.
        """
        # Lógica mantida da versão anterior
        caminho_relatorio = self.diretorio_saida / "relatorio_processamento_v2.txt" # Nome diferente
        sucessos = 0
        falhas = 0
        try:
            with caminho_relatorio.open('w', encoding='utf-8') as f:
                f.write("Relatório de Processamento de Narração (v2 - Adaptativo)\n")
                f.write("=========================================================\n")
                f.write(f"Processado em: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                if not resultados:
                    f.write("Nenhum arquivo processado.\n")
                else:
                    f.write("Resultados:\n")
                    for arquivo_txt, resultado in resultados.items():
                        status = "SUCESSO" if isinstance(resultado, Path) else "FALHA"
                        detalhe = resultado.name if isinstance(resultado, Path) else resultado
                        f.write(f"- Arquivo: {arquivo_txt.name}\n")
                        f.write(f"  Status: {status}\n")
                        f.write(f"  Detalhe: {detalhe}\n\n")
                        if status == "SUCESSO":
                            sucessos += 1
                        else:
                            falhas += 1
                
                f.write("Resumo:\n")
                f.write(f"- Total de arquivos processados: {len(resultados)}\n")
                f.write(f"- Sucessos: {sucessos}\n")
                f.write(f"- Falhas: {falhas}\n")
            
            logger.info(f"Relatório de processamento gerado em: {caminho_relatorio}")
        except Exception as e:
            logger.error(f"Erro ao gerar relatório de processamento: {e}")

# Exemplo de uso (requer estrutura de pastas e arquivos de configuração)
# Este script é geralmente chamado pelo narrador_roteiros_final.py
if __name__ == "__main__":
    print("Este módulo é destinado a ser importado e usado pelo script principal (narrador_roteiros_final.py).")
    print("Ele contém a lógica para processar arquivos de roteiro em áudio de forma adaptativa.")
    # Para testar isoladamente, seria necessário configurar instâncias de 
    # APIKeyManager, ElevenLabsAPI e os diretórios corretamente.

