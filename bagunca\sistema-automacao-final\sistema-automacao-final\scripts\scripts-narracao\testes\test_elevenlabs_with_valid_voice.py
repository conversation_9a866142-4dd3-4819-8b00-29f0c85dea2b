"""
Teste da integração ElevenLabs + Proxy com voice_id válido
"""

import sys
from pathlib import Path

# Adiciona o caminho dos módulos
sys.path.append(str(Path(__file__).parent))

from api_key_manager import APIKeyManager
from proxy_manager import WebshareProxyManager
from elevenlabs_api_atualizado import ElevenLabsAPI

def main():
    keys_file = "chaves-api-elevenlabs.txt"
    webshare_token = "mt643rsq556yqggigaa3emmmjmdycc2h6868bi7b"
    
    print("🧪 TESTE INTEGRADO: ELEVENLABS + PROXY + VOICE_ID VÁLIDO")
    print("="*70)
    
    try:
        # 1. Inicializa managers
        print("\n1️⃣ Inicializando gerenciadores...")
        key_manager = APIKeyManager(keys_file)
        proxy_manager = WebshareProxyManager(
            api_token=webshare_token,
            use_rotating_endpoint=True,
            enable_fallback=True
        )
        
        # 2. Inicializa cliente ElevenLabs
        print("2️⃣ Inicializando cliente ElevenLabs...")
        elevenlabs_api = ElevenLabsAPI(
            api_key_manager=key_manager,
            proxy_manager=proxy_manager
        )
        
        # 3. Obtém voice_id válido
        print("3️⃣ Obtendo voice_id válido...")
        voices = elevenlabs_api.list_voices()
        
        if not voices.get('voices'):
            print("❌ Nenhuma voz encontrada!")
            return 1
        
        # Pega a primeira voz disponível
        valid_voice = voices['voices'][0]
        voice_id = valid_voice['voice_id']
        voice_name = valid_voice['name']
        
        print(f"✅ Voice selecionada: {voice_name} (ID: {voice_id})")
        
        # 4. Status do sistema
        print("\n4️⃣ Status do sistema:")
        status = elevenlabs_api.get_combined_status()
        keys_info = status['api_keys']
        proxy_info = status['proxies']
        
        print(f"   🔑 Chaves: {keys_info['working']}/{keys_info['total']} funcionais")
        print(f"   🌐 Proxies: {proxy_info['proxy_config']['total_proxies']} disponíveis")
        print(f"   📍 Países: {', '.join(proxy_info['proxy_config']['countries'])}")
        
        # 5. Teste de TTS
        print("\n5️⃣ Testando Text-to-Speech...")
        text = "Olá! Este é um teste da integração completa com proxy rotation e voice_id válido."
        output_path = "teste_integrado_voice_valido.mp3"
        
        print(f"   📝 Texto: {text[:50]}...")
        print(f"   🎵 Voz: {voice_name}")
        print(f"   🌐 Proxy: Ativo com rotação automática")
        print(f"   📁 Saída: {output_path}")
        
        # Executa TTS com voice_id válido
        generated_file = elevenlabs_api.text_to_speech_with_auto_retry(
            text=text,
            output_path=output_path,
            voice_id=voice_id,  # USA VOICE_ID VÁLIDO
            language_code="pt-BR"
        )
        
        print(f"\n🎉 SUCESSO TOTAL!")
        print(f"   ✅ Arquivo gerado: {generated_file}")
        print(f"   📊 Tamanho: {Path(generated_file).stat().st_size / 1024:.1f} KB")
        
        # 6. Estatísticas finais
        print("\n6️⃣ Estatísticas finais:")
        final_status = elevenlabs_api.get_combined_status()
        
        keys_final = final_status['api_keys']
        proxy_final = final_status['proxies']['statistics']
        
        print(f"   🔑 Chaves: {keys_final['working']} funcionais, {keys_final['blocked']} bloqueadas")
        print(f"   🌐 Proxy: {proxy_final['successful_requests']}/{proxy_final['total_requests']} sucessos ({proxy_final['success_rate_percent']}%)")
        
        print(f"\n✨ INTEGRAÇÃO COMPLETAMENTE FUNCIONAL! ✨")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ ERRO: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
