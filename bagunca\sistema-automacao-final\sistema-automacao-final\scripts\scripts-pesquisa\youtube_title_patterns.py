"""
Módulo para extração de padrões específicos de títulos de vídeos do YouTube.

Este módulo implementa técnicas avançadas para extrair padrões de títulos
específicos do nicho de histórias de vingança, para melhorar a relevância
das buscas e filtragens.
"""

import os
import re
import logging
from collections import Counter
from typing import List, Dict, Any, Tuple, Optional, Set

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("youtube_title_patterns")

class YouTubeTitlePatternExtractor:
    """
    Classe para extração de padrões específicos de títulos de vídeos do YouTube.
    """
    
    def __init__(self, reference_data_path: str):
        """
        Inicializa o extrator de padrões de títulos.
        
        Args:
            reference_data_path: Caminho para o arquivo CSV com dados de referência
        """
        self.reference_data_path = reference_data_path
        
        # Carregar dados de referência
        self.reference_data = self._load_reference_data()
        
        # Extrair títulos dos vídeos de referência
        self.reference_titles = [video.get('title', '') for video in self.reference_data if 'title' in video]
        
        # Padrões narrativos comuns em histórias de vingança
        self.narrative_patterns = [
            r"my (.*?) (changed|told|left|banned|emptied|found|discovered)",
            r"(.*?) had no idea (.*?)",
            r"until (.*?) (saw|found|realized|discovered)",
            r"(.*?) what (was|is) coming",
            r"(.*?) told me to leave",
            r"(.*?) changed my",
            r"(.*?) emptied",
            r"(.*?) banned me",
            r"(.*?) left me"
        ]
        
        logger.info(f"YouTubeTitlePatternExtractor inicializado com {len(self.reference_titles)} títulos de referência")
    
    def _load_reference_data(self) -> List[Dict]:
        """
        Carrega os dados de referência do CSV.
        
        Returns:
            Lista de dicionários com dados de referência
        """
        import pandas as pd
        
        try:
            df = pd.read_csv(self.reference_data_path)
            logger.info(f"Dados de referência carregados: {len(df)} vídeos")
            
            # Converter para lista de dicionários
            reference_data = df.to_dict('records')
            
            return reference_data
        except Exception as e:
            logger.error(f"Erro ao carregar dados de referência: {str(e)}")
            return []
    
    def extract_common_phrases(self) -> List[str]:
        """
        Extrai frases comuns dos títulos de referência.
        
        Returns:
            Lista de frases comuns
        """
        common_phrases = []
        
        # Extrair frases entre aspas
        for title in self.reference_titles:
            quotes = re.findall(r'"([^"]*)"', title)
            for quote in quotes:
                if len(quote.split()) >= 3 and quote not in common_phrases:
                    common_phrases.append(quote)
        
        # Extrair frases após hífen ou travessão
        for title in self.reference_titles:
            if '-' in title or '—' in title:
                parts = re.split(r'[-—]', title)
                if len(parts) > 1 and len(parts[1].strip().split()) >= 3:
                    phrase = parts[1].strip()
                    if phrase not in common_phrases:
                        common_phrases.append(phrase)
        
        logger.info(f"Extraídas {len(common_phrases)} frases comuns dos títulos")
        return common_phrases
    
    def extract_narrative_structures(self) -> List[str]:
        """
        Extrai estruturas narrativas dos títulos de referência.
        
        Returns:
            Lista de estruturas narrativas
        """
        narrative_structures = []
        
        # Aplicar padrões narrativos aos títulos
        for pattern in self.narrative_patterns:
            for title in self.reference_titles:
                matches = re.findall(pattern, title, re.IGNORECASE)
                if matches:
                    for match in matches:
                        if isinstance(match, tuple):
                            # Se o match for uma tupla, juntar as partes
                            structure = ' '.join(match)
                        else:
                            structure = match
                        
                        if structure and structure not in narrative_structures:
                            narrative_structures.append(structure)
        
        logger.info(f"Extraídas {len(narrative_structures)} estruturas narrativas dos títulos")
        return narrative_structures
    
    def extract_character_relationships(self) -> List[str]:
        """
        Extrai relações de personagens dos títulos de referência.
        
        Returns:
            Lista de relações de personagens
        """
        # Relações de personagens comuns em histórias de vingança
        relationship_terms = [
            "mother-in-law", "mil", "father-in-law", "fil", 
            "wife", "husband", "sister", "brother", 
            "daughter", "son", "parent", "child",
            "boss", "colleague", "friend", "neighbor",
            "ex", "girlfriend", "boyfriend", "partner"
        ]
        
        found_relationships = []
        
        # Buscar relações nos títulos
        for term in relationship_terms:
            for title in self.reference_titles:
                if term.lower() in title.lower():
                    # Extrair contexto (5 palavras antes e depois)
                    words = title.split()
                    for i, word in enumerate(words):
                        if term.lower() in word.lower():
                            start = max(0, i - 5)
                            end = min(len(words), i + 6)
                            context = ' '.join(words[start:end])
                            if context not in found_relationships:
                                found_relationships.append(context)
        
        logger.info(f"Extraídas {len(found_relationships)} relações de personagens dos títulos")
        return found_relationships
    
    def extract_emotional_triggers(self) -> List[str]:
        """
        Extrai gatilhos emocionais dos títulos de referência.
        
        Returns:
            Lista de gatilhos emocionais
        """
        # Gatilhos emocionais comuns em histórias de vingança
        emotional_terms = [
            "revenge", "karma", "payback", "justice", "betrayal",
            "shocked", "surprised", "devastated", "heartbroken",
            "angry", "furious", "outraged", "humiliated",
            "regret", "remorse", "guilt", "shame",
            "victory", "triumph", "vindication", "satisfaction"
        ]
        
        found_triggers = []
        
        # Buscar gatilhos nos títulos
        for term in emotional_terms:
            for title in self.reference_titles:
                if term.lower() in title.lower():
                    # Extrair contexto (3 palavras antes e depois)
                    words = title.split()
                    for i, word in enumerate(words):
                        if term.lower() in word.lower():
                            start = max(0, i - 3)
                            end = min(len(words), i + 4)
                            context = ' '.join(words[start:end])
                            if context not in found_triggers:
                                found_triggers.append(context)
        
        logger.info(f"Extraídos {len(found_triggers)} gatilhos emocionais dos títulos")
        return found_triggers
    
    def extract_title_keywords(self) -> List[str]:
        """
        Extrai palavras-chave dos títulos de referência.
        
        Returns:
            Lista de palavras-chave
        """
        # Combinar todos os títulos
        all_text = ' '.join(self.reference_titles).lower()
        
        # Remover caracteres especiais
        all_text = re.sub(r'[^\w\s]', ' ', all_text)
        
        # Dividir em palavras
        words = all_text.split()
        
        # Contar frequência das palavras
        word_counts = Counter(words)
        
        # Palavras comuns a serem ignoradas
        common_words = {
            'the', 'and', 'that', 'was', 'for', 'with', 'this', 'what',
            'when', 'where', 'while', 'your', 'their', 'then', 'than',
            'have', 'were', 'they', 'them', 'there', 'these', 'those',
            'some', 'will', 'would', 'could', 'should', 'about', 'after',
            'before', 'because', 'under', 'over', 'other', 'again', 'against',
            'between', 'through', 'during', 'today', 'tomorrow', 'video',
            'channel', 'subscribe', 'like', 'comment', 'watch', 'please', 'thanks'
        }
        
        # Extrair palavras-chave mais frequentes (excluindo palavras comuns)
        keywords = [word for word, count in word_counts.most_common(50) 
                   if word not in common_words and len(word) > 3 and count > 1]
        
        logger.info(f"Extraídas {len(keywords)} palavras-chave dos títulos")
        return keywords
    
    def extract_all_patterns(self) -> Dict[str, List[str]]:
        """
        Extrai todos os tipos de padrões dos títulos de referência.
        
        Returns:
            Dicionário com diferentes tipos de padrões
        """
        patterns = {
            'common_phrases': self.extract_common_phrases(),
            'narrative_structures': self.extract_narrative_structures(),
            'character_relationships': self.extract_character_relationships(),
            'emotional_triggers': self.extract_emotional_triggers(),
            'keywords': self.extract_title_keywords()
        }
        
        # Limitar o número de padrões de cada tipo
        for key in patterns:
            if len(patterns[key]) > 20:
                patterns[key] = patterns[key][:20]
        
        logger.info(f"Extraídos {sum(len(v) for v in patterns.values())} padrões no total")
        return patterns
    
    def generate_search_queries(self, max_queries: int = 50) -> List[str]:
        """
        Gera consultas de busca com base nos padrões extraídos.
        
        Args:
            max_queries: Número máximo de consultas a gerar
            
        Returns:
            Lista de consultas de busca
        """
        queries = []
        
        # Extrair todos os padrões
        patterns = self.extract_all_patterns()
        
        # 1. Adicionar frases comuns
        queries.extend(patterns['common_phrases'][:10])
        
        # 2. Adicionar estruturas narrativas
        queries.extend(patterns['narrative_structures'][:10])
        
        # 3. Adicionar relações de personagens
        queries.extend(patterns['character_relationships'][:10])
        
        # 4. Adicionar gatilhos emocionais
        queries.extend(patterns['emotional_triggers'][:10])
        
        # 5. Adicionar combinações de palavras-chave
        keywords = patterns['keywords']
        if len(keywords) >= 2:
            for i in range(min(10, len(keywords))):
                for j in range(i+1, min(15, len(keywords))):
                    queries.append(f"{keywords[i]} {keywords[j]}")
        
        # 6. Adicionar consultas específicas para o nicho
        niche_queries = [
            "revenge story long format",
            "family drama stories 20 minutes",
            "mother in law revenge stories long",
            "relationship revenge long videos",
            "karma stories youtube long format",
            "real life revenge stories 20 minutes",
            "family conflict stories long format",
            "marriage drama long videos",
            "revenge narration long format",
            "true revenge stories 20 minutes"
        ]
        queries.extend(niche_queries)
        
        # Remover duplicatas
        unique_queries = list(set(queries))
        
        # Limitar ao número máximo de consultas
        if len(unique_queries) > max_queries:
            unique_queries = unique_queries[:max_queries]
        
        logger.info(f"Geradas {len(unique_queries)} consultas de busca com base nos padrões extraídos")
        return unique_queries
