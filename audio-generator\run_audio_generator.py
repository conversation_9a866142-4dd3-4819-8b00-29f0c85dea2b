#!/usr/bin/env python3
"""
Script principal para executar o sistema de geração de áudio
Versão simplificada e organizada
"""

import os
import sys
from pathlib import Path

# Adiciona o diretório de scripts ao PATH
SCRIPT_DIR = Path(__file__).resolve().parent
SCRIPTS_DIR = SCRIPT_DIR / "scripts"
sys.path.insert(0, str(SCRIPTS_DIR))

def main():
    """Função principal que executa o sistema de narração sequencial"""
    print("🎤 Sistema de Geração de Áudio")
    print("=" * 50)
    
    # Importa e executa o script principal
    try:
        from arquivo_3_script_principal import main as run_narrador
        run_narrador()
    except ImportError as e:
        print(f"❌ Erro ao importar módulos: {e}")
        print("Verifique se todos os arquivos estão na pasta 'scripts'")
        return 1
    except Exception as e:
        print(f"❌ Erro durante execução: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
