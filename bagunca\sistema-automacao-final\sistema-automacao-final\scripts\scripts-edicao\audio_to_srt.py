#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import assemblyai as aai
import os
import sys
import math
import glob # Para encontrar arquivos
import io

# Configurar stdout para UTF-8 no Windows
if sys.platform == "win32":
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# --- Configurações de Caminhos Relativos à Raiz do Projeto ---
# A raiz do projeto está duas pastas acima do diretório do script
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))

INPUT_DIR = os.path.join(PROJECT_ROOT, "narracao") # Pasta de entrada para áudios (na raiz)
OUTPUT_DIR = os.path.join(PROJECT_ROOT, "legendas") # Salvar SRTs na pasta legendas (na raiz)
API_KEY_FILE = os.path.join(PROJECT_ROOT, "config", "chave-api-assemblyai.txt") # Arquivo da chave API em config/
MAX_WORDS_PER_LINE = 6 # Máximo de palavras por linha no SRT
# -----------------------------------------------------------

# Função para formatar milissegundos para o formato de tempo SRT HH:MM:SS,ms
def format_srt_time(milliseconds):
    if milliseconds < 0:
        milliseconds = 0 # Garante que não haja tempos negativos
    seconds = milliseconds / 1000
    minutes = seconds // 60
    hours = minutes // 60
    milliseconds_rem = milliseconds % 1000
    seconds_rem = math.floor(seconds % 60)
    minutes_rem = math.floor(minutes % 60)
    return f"{int(hours):02}:{int(minutes_rem):02}:{int(seconds_rem):02},{int(milliseconds_rem):03}"

# Função para ler a chave da API de um arquivo
def get_api_key(key_file):
    # O caminho já é absoluto devido ao os.path.join com PROJECT_ROOT
    abs_key_file = key_file
    if not os.path.exists(abs_key_file):
        print(f"Erro: Arquivo de chave API nao encontrado: {abs_key_file}", file=sys.stderr)
        # Tenta criar o diretório config se não existir
        config_dir = os.path.dirname(abs_key_file)
        if not os.path.exists(config_dir):
            try:
                os.makedirs(config_dir)
                print(f"Diretório de configuração criado: {config_dir}", file=sys.stderr)
                print(f"Por favor, coloque o arquivo '{os.path.basename(abs_key_file)}' contendo a chave da API em {config_dir}", file=sys.stderr)
            except OSError as e:
                 print(f"Erro ao tentar criar o diretório de configuração {config_dir}: {e}", file=sys.stderr)
        else:
             print(f"Verifique se o arquivo '{os.path.basename(abs_key_file)}' existe em {config_dir}", file=sys.stderr)
        return None
    try:
        with open(abs_key_file, "r", encoding="utf-8") as f:
            api_key = f.read().strip()
            if not api_key:
                print(f"Erro: Chave API vazia encontrada no arquivo {abs_key_file}", file=sys.stderr)
                return None
            return api_key
    except Exception as e:
        print(f"Erro ao ler o arquivo de chave API {abs_key_file}: {e}", file=sys.stderr)
        return None

# Função para gerar conteúdo SRT
def generate_srt(transcript, max_words_per_line):
    if not transcript or not transcript.words:
        print("Aviso: Nenhuma palavra foi detectada na transcricao.", file=sys.stderr)
        return ""

    srt_content = []
    segment_count = 1
    current_line_words = []

    for i, word in enumerate(transcript.words):
        current_line_words.append(word)

        # Cria uma nova linha se atingir o máximo de palavras ou for a última palavra
        if len(current_line_words) == max_words_per_line or i == len(transcript.words) - 1:
            start_time = current_line_words[0].start
            end_time = current_line_words[-1].end
            line_text = " ".join([w.text for w in current_line_words])

            srt_content.append(str(segment_count))
            srt_content.append(f"{format_srt_time(start_time)} --> {format_srt_time(end_time)}")
            srt_content.append(line_text)
            srt_content.append("")

            segment_count += 1
            current_line_words = []

    return "\n".join(srt_content)

def process_audio_file(audio_path, output_dir, config, max_words):
    """Processa um único arquivo de áudio, salva o SRT no diretório de saída."""
    base_name = os.path.splitext(os.path.basename(audio_path))[0]
    # Salva o SRT no diretório de saída especificado (já é absoluto)
    output_srt_path = os.path.join(output_dir, f"{base_name}.srt")

    print(f"\nProcessando arquivo: {os.path.basename(audio_path)}")
    print(f"Arquivo SRT de saida sera: {output_srt_path}")

    transcriber = aai.Transcriber()

    try:
        transcript = transcriber.transcribe(audio_path, config=config)
    except Exception as e:
        print(f"Erro durante a chamada da API de transcricao para {os.path.basename(audio_path)}: {e}", file=sys.stderr)
        if hasattr(e, 'response') and hasattr(e.response, 'json'):
             try:
                 error_details = e.response.json()
                 print(f"Detalhes do erro da API: {error_details}", file=sys.stderr)
             except:
                 pass # Falha ao obter detalhes JSON
        return False # Indica falha

    if transcript.status == aai.TranscriptStatus.error:
        print(f"Erro na transcricao retornado pela API para {os.path.basename(audio_path)}: {transcript.error}", file=sys.stderr)
        return False # Indica falha
    elif transcript.status == aai.TranscriptStatus.completed:
        detected_lang = getattr(transcript, 'language_code', None)
        detected_confidence = getattr(transcript, 'language_confidence', None)
        if detected_lang:
             print(f"Idioma detectado: {detected_lang} (Confianca: {detected_confidence})")
        else:
             print("Aviso: Deteccao automatica de idioma habilitada, mas nenhum idioma foi retornado pela API.")

        print(f"Transcricao concluida com sucesso para {os.path.basename(audio_path)}.")
        srt_data = generate_srt(transcript, max_words_per_line=max_words)

        try:
            # Garante que o diretório de saída exista antes de escrever
            os.makedirs(os.path.dirname(output_srt_path), exist_ok=True)
            with open(output_srt_path, "w", encoding="utf-8") as f:
                f.write(srt_data)
            print(f"Arquivo SRT gerado com sucesso: {output_srt_path}")
            return True # Indica sucesso
        except IOError as e:
            print(f"Erro ao escrever o arquivo SRT {output_srt_path}: {e}", file=sys.stderr)
            return False # Indica falha
    else:
        print(f"Status da transcricao inesperado para {os.path.basename(audio_path)}: {transcript.status}", file=sys.stderr)
        return False # Indica falha

def main():
    print("Iniciando script de transcricao de audio para SRT...")
    # Usa os caminhos absolutos definidos globalmente
    print(f"Usando diretorio raiz do projeto: {PROJECT_ROOT}")
    print(f"Usando diretorio de entrada (narracao): {INPUT_DIR}")
    print(f"Usando diretorio de saida (legendas): {OUTPUT_DIR}")
    print(f"Usando arquivo de chave API (config): {API_KEY_FILE}")
    print(f"Maximo de palavras por linha: {MAX_WORDS_PER_LINE}")

    # Verifica se o diretório de entrada existe
    if not os.path.isdir(INPUT_DIR):
        print(f"Erro: Diretorio de entrada nao existe: {INPUT_DIR}", file=sys.stderr)
        # Tenta criar o diretório se não existir
        try:
            os.makedirs(INPUT_DIR)
            print(f"Diretório de entrada criado: {INPUT_DIR}", file=sys.stderr)
            print(f"Por favor, coloque os arquivos de áudio (.mp3, .wav) em {INPUT_DIR}", file=sys.stderr)
        except OSError as e:
            print(f"Erro ao tentar criar o diretório de entrada {INPUT_DIR}: {e}", file=sys.stderr)
            sys.exit(1)
        # Sai mesmo se criou, pois provavelmente está vazio
        print("Saindo. Adicione arquivos de áudio e execute novamente.", file=sys.stderr)
        sys.exit(1)


    # Cria o diretório de saída se não existir (redundante com a verificação em process_audio_file, mas bom ter aqui)
    if not os.path.exists(OUTPUT_DIR):
        try:
            os.makedirs(OUTPUT_DIR)
            print(f"Diretorio de saida criado: {OUTPUT_DIR}")
        except OSError as e:
            print(f"Erro ao criar o diretorio de saida {OUTPUT_DIR}: {e}", file=sys.stderr)
            sys.exit(1)

    # A chave da API é lida do caminho configurado
    api_key = get_api_key(API_KEY_FILE)
    if not api_key:
        print(f"Erro critico: Nao foi possivel obter a chave da API do arquivo {API_KEY_FILE}", file=sys.stderr)
        sys.exit(1)
    aai.settings.api_key = api_key

    print("Configurando para usar deteccao automatica de idioma para todos os arquivos.")
    config = aai.TranscriptionConfig(language_detection=True)

    print(f"Procurando arquivos .mp3 e .wav em: {INPUT_DIR}")
    # Busca arquivos no diretório de entrada especificado
    audio_files = glob.glob(os.path.join(INPUT_DIR, "*.mp3")) + glob.glob(os.path.join(INPUT_DIR, "*.wav"))

    if not audio_files:
        print(f"Nenhum arquivo .mp3 ou .wav encontrado em {INPUT_DIR}")
        sys.exit(0)

    print(f"Encontrados {len(audio_files)} arquivos de audio para processar:")
    for f in audio_files:
        print(f" - {os.path.basename(f)}")

    success_count = 0
    failure_count = 0
    for audio_path in audio_files:
        # Passa caminhos absolutos para a função de processamento
        if process_audio_file(audio_path, OUTPUT_DIR, config, MAX_WORDS_PER_LINE):
            success_count += 1
        else:
            failure_count += 1

    print(f"\nProcessamento concluido. {success_count} sucesso(s), {failure_count} falha(s).")

if __name__ == "__main__":
    main()