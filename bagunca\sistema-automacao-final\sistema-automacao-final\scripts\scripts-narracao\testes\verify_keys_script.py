#!/usr/bin/env python3
"""
Script para verificar o status real das chaves API da ElevenLabs
e identificar quais estão bloqueadas vs funcionais
"""

import requests
import json
import time
from pathlib import Path

def check_single_key(api_key: str) -> dict:
    """Verifica uma única chave API"""
    headers = {"xi-api-key": api_key}
    
    try:
        # Testa endpoint de subscription
        response = requests.get(
            "https://api.elevenlabs.io/v1/user/subscription", 
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            return {
                "status": "OK",
                "limit": data.get("character_limit", 0),
                "used": data.get("character_count", 0),
                "remaining": data.get("character_limit", 0) - data.get("character_count", 0),
                "tier": data.get("tier", "unknown")
            }
        elif response.status_code == 401:
            try:
                error_detail = response.json()
                return {
                    "status": "BLOCKED",
                    "error": error_detail.get("detail", {}).get("message", "Unauthorized"),
                    "status_code": 401
                }
            except:
                return {
                    "status": "BLOCKED", 
                    "error": "401 Unauthorized",
                    "status_code": 401
                }
        else:
            return {
                "status": "ERROR",
                "error": f"HTTP {response.status_code}",
                "status_code": response.status_code
            }
            
    except requests.exceptions.RequestException as e:
        return {
            "status": "NETWORK_ERROR",
            "error": str(e)
        }

def check_all_keys(keys_file_path: str):
    """Verifica todas as chaves no arquivo"""
    
    if not Path(keys_file_path).exists():
        print(f"❌ Arquivo de chaves não encontrado: {keys_file_path}")
        return
    
    # Carrega as chaves
    with open(keys_file_path, 'r') as f:
        keys = [line.strip() for line in f.readlines() if line.strip() and not line.startswith('#')]
    
    if not keys:
        print("❌ Nenhuma chave encontrada no arquivo")
        return
    
    print(f"🔍 Verificando {len(keys)} chaves API...")
    print("=" * 80)
    
    results = {
        "working": [],
        "blocked": [],
        "error": []
    }
    
    for i, key in enumerate(keys, 1):
        print(f"\n[{i:2d}/{len(keys)}] Verificando chave: {key[:12]}...")
        
        result = check_single_key(key)
        key_info = {
            "key": key,
            "partial_key": f"{key[:8]}...{key[-4:]}",
            "result": result
        }
        
        if result["status"] == "OK":
            results["working"].append(key_info)
            print(f"    ✅ OK - Limite: {result['limit']}, Usado: {result['used']}, Restante: {result['remaining']}")
            print(f"    📊 Tier: {result['tier']}")
            
        elif result["status"] == "BLOCKED":
            results["blocked"].append(key_info)
            print(f"    🚫 BLOQUEADA - {result['error']}")
            
        else:
            results["error"].append(key_info)
            print(f"    ❌ ERRO - {result['error']}")
        
        # Pausa para evitar rate limiting
        time.sleep(0.5)
    
    # Resumo final
    print("\n" + "=" * 80)
    print("📊 RESUMO FINAL:")
    print(f"✅ Chaves funcionais: {len(results['working'])}")
    print(f"🚫 Chaves bloqueadas: {len(results['blocked'])}")
    print(f"❌ Chaves com erro: {len(results['error'])}")
    
    # Detalhes das chaves funcionais
    if results["working"]:
        print(f"\n✅ CHAVES FUNCIONAIS ({len(results['working'])}):")
        total_remaining = 0
        for key_info in results["working"]:
            remaining = key_info["result"]["remaining"]
            total_remaining += remaining
            print(f"   {key_info['partial_key']} - {remaining:,} caracteres restantes")
        print(f"   💡 Total disponível: {total_remaining:,} caracteres")
    
    # Detalhes das chaves bloqueadas
    if results["blocked"]:
        print(f"\n🚫 CHAVES BLOQUEADAS ({len(results['blocked'])}):")
        for key_info in results["blocked"]:
            print(f"   {key_info['partial_key']} - {key_info['result']['error']}")
    
    # Salva relatório
    report_path = "key_verification_report.json"
    with open(report_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Relatório detalhado salvo em: {report_path}")
    
    return results

if __name__ == "__main__":
    # Ajuste o caminho conforme sua estrutura
    keys_file = "config/chaves-api-elevenlabs.txt"
    
    print("🔍 Verificador de Status das Chaves API ElevenLabs")
    print("=" * 50)
    
    results = check_all_keys(keys_file)
    
    if results and len(results["working"]) == 0:
        print("\n⚠️  ATENÇÃO: Nenhuma chave funcional encontrada!")
        print("Recomendações:")
        print("1. Verifique se está usando VPN/Proxy")
        print("2. Considere aguardar algumas horas")
        print("3. Entre em contato com o suporte da ElevenLabs")
        print("4. Considere upgrade para plano pago")
    elif results and len(results["working"]) < len(results["working"]) + len(results["blocked"]):
        print("\n⚠️  Algumas chaves estão bloqueadas, mas outras funcionam")
        print("Recomendação: Use apenas as chaves funcionais")
