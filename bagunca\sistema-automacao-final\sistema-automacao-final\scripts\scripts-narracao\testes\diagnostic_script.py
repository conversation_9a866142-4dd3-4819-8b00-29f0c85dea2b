#!/usr/bin/env python3
"""
Script de Diagnóstico para ElevenLabs API

Este script ajuda a identificar problemas com:
1. Chaves API inválidas
2. Voice IDs não funcionais
3. Problemas de proxy
4. Formatos de saída não suportados
5. Problemas de conectividade
"""

import os
import json
import requests
import time
import sys
from pathlib import Path

# Configurações
BASE_URL = "https://api.elevenlabs.io"
DIAGNOSTIC_TEXT = "Este é um teste de diagnóstico da API ElevenLabs."

# Voice IDs conhecidos que funcionam (públicos da ElevenLabs)
KNOWN_VOICE_IDS = {
    "Rachel": "21m00Tcm4TlvDq81kWAM",
    "Drew": "29vD33N1CtxCmqQRPOHJ", 
    "Clyde": "2EiwWnXFnvU5JabPnv8n",
    "<PERSON>": "5Q0t7uMcjvnagumLfvZi",
    "Domi": "AZnzlk1XvdvUeBnXmlld",
    "<PERSON>": "CYw3kZ02Hs0563khs1Fj",
    "Fin": "D38z5RcWu1voky8WS1ja",
    "Sarah": "EXAVITQu4vr4xnSDxMaL",
    "Antoni": "ErXwobaYiN019PkySvjV",
    "Thomas": "GBv7mTt0atIp3Br8iCZE",
    "Charlotte": "IKne3meq5aSn9XLyUdCD",
    "Matilda": "XrExE9yKIg1WjnnlVkGX",
    "James": "ZQe5CZNOzWyzPSCn5a3c",
    "Joseph": "Zlb1dXrM653N07WRdFW3",
    "Jeremy": "bVMeCyTHy58xNoL34h3p",
    "Michael": "flq6f7yk4E4fJM5XTYuZ",
    "Ethan": "g5CIjZEefAph4nQFvHAz",
    "Gigi": "jBpfuIE2acCO8z3wKNLl",
    "Freya": "jsCqWAovK2LkecY7zXl4",
    "Grace": "oWAxZDx7w5VEj9dCyTzz",
    "Daniel": "onwK4e9ZLuTAKqWW03F9",
    "Lily": "pFZP5JQG7iQjIQuC4Bku",
    "Serena": "pMsXgVXv3BLzUgSXRplE",
    "Adam": "pNInz6obpgDQGcFmaJgB",
    "Nicole": "piTKgcLEGmPE4e6mEKli",
    "Jessie": "t0jbNlBVZ17f02VDIeMI",
    "Ryan": "wViXBPUzp2ZZixB1xQuM",
    "Sam": "yoZ06aMxZJJ28mfd3POQ"
}

def load_api_keys(keys_file: str) -> list:
    """Carrega chaves API do arquivo."""
    try:
        with open(keys_file, 'r') as f:
            keys = [line.strip() for line in f.readlines() if line.strip() and not line.startswith('#')]
        print(f"[INFO] Carregadas {len(keys)} chaves do arquivo {keys_file}")
        return keys
    except Exception as e:
        print(f"[ERROR] Erro ao carregar chaves: {e}")
        return []

def test_api_key(api_key: str) -> dict:
    """Testa uma chave API verificando a subscription."""
    try:
        headers = {"xi-api-key": api_key}
        response = requests.get(f"{BASE_URL}/v1/user/subscription", headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            return {
                "status": "OK",
                "character_limit": data.get("character_limit", 0),
                "character_count": data.get("character_count", 0),
                "remaining": data.get("character_limit", 0) - data.get("character_count", 0),
                "tier": data.get("tier", "unknown")
            }
        elif response.status_code == 401:
            return {"status": "INVALID", "error": "401 Unauthorized"}
        else:
            return {"status": "ERROR", "error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {"status": "ERROR", "error": str(e)}

def test_voice_id(api_key: str, voice_id: str, voice_name: str) -> dict:
    """Testa se um voice ID funciona."""
    try:
        headers = {
            "xi-api-key": api_key,
            "Content-Type": "application/json"
        }
        
        payload = {
            "text": DIAGNOSTIC_TEXT,
            "model_id": "eleven_multilingual_v2"
        }
        
        endpoint = f"{BASE_URL}/v1/text-to-speech/{voice_id}"
        params = {"output_format": "mp3_44100_128"}
        
        response = requests.post(endpoint, headers=headers, json=payload, params=params, timeout=30)
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            file_size = len(response.content)
            return {
                "status": "OK",
                "file_size": file_size,
                "content_type": content_type
            }
        else:
            try:
                error_data = response.json()
                error_msg = error_data.get("detail", {}).get("message", response.text)
            except:
                error_msg = response.text
                
            return {
                "status": "ERROR",
                "http_code": response.status_code,
                "error": error_msg
            }
            
    except Exception as e:
        return {"status": "ERROR", "error": str(e)}

def list_available_voices(api_key: str) -> dict:
    """Lista vozes disponíveis na conta."""
    try:
        headers = {"xi-api-key": api_key}
        response = requests.get(f"{BASE_URL}/v1/voices", headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            voices = data.get("voices", [])
            return {
                "status": "OK",
                "count": len(voices),
                "voices": [(v.get("voice_id"), v.get("name")) for v in voices]
            }
        else:
            return {"status": "ERROR", "error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {"status": "ERROR", "error": str(e)}

def test_proxy_connection(proxy_config: dict) -> dict:
    """Testa conectividade via proxy."""
    try:
        response = requests.get("https://httpbin.org/ip", proxies=proxy_config, timeout=10)
        
        if response.status_code == 200:
            ip_data = response.json()
            return {
                "status": "OK",
                "detected_ip": ip_data.get("origin", "unknown")
            }
        else:
            return {"status": "ERROR", "error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {"status": "ERROR", "error": str(e)}

def main():
    """Função principal do diagnóstico."""
    print("=" * 80)
    print("DIAGNÓSTICO DA API ELEVENLABS")
    print("=" * 80)
    print()
    
    # 1. Verificar arquivo de chaves
    script_dir = Path(__file__).resolve().parent
    project_root = script_dir.parent.parent
    keys_file = project_root / "config" / "chaves-api-elevenlabs.txt"
    
    if not keys_file.exists():
        print(f"[ERROR] Arquivo de chaves não encontrado: {keys_file}")
        print("Crie o arquivo com suas chaves API, uma por linha.")
        return 1
    
    api_keys = load_api_keys(str(keys_file))
    if not api_keys:
        print("[ERROR] Nenhuma chave API encontrada no arquivo.")
        return 1
    
    print(f"[INFO] Testando {len(api_keys)} chaves API...")
    print()
    
    # 2. Testar chaves API
    working_keys = []
    for i, key in enumerate(api_keys):
        print(f"[TEST] Chave {i+1}/{len(api_keys)}: {key[:8]}...")
        result = test_api_key(key)
        
        if result["status"] == "OK":
            print(f"  [OK] Válida - {result['remaining']} caracteres restantes (tier: {result['tier']})")
            working_keys.append(key)
        else:
            print(f"  [ERROR] {result['error']}")
        
        time.sleep(0.5)  # Para não exceder rate limit
    
    print()
    print(f"[SUMMARY] {len(working_keys)}/{len(api_keys)} chaves funcionais")
    
    if not working_keys:
        print("[ERROR] Nenhuma chave funcional encontrada!")
        return 1
    
    # 3. Testar voice IDs com a primeira chave funcional
    test_key = working_keys[0]
    print(f"\n[INFO] Testando voice IDs com chave {test_key[:8]}...")
    
    # Listar vozes disponíveis primeiro
    voices_result = list_available_voices(test_key)
    if voices_result["status"] == "OK":
        print(f"[INFO] {voices_result['count']} vozes disponíveis na conta:")
        for voice_id, voice_name in voices_result['voices'][:5]:  # Mostra apenas as primeiras 5
            print(f"  - {voice_name}: {voice_id}")
        if voices_result['count'] > 5:
            print(f"  ... e mais {voices_result['count'] - 5} vozes")
        print()
    
    # Testar voice IDs conhecidos
    print("[INFO] Testando voice IDs públicos conhecidos...")
    working_voices = []
    
    for voice_name, voice_id in list(KNOWN_VOICE_IDS.items())[:5]:  # Testa apenas os primeiros 5
        print(f"[TEST] {voice_name} ({voice_id})...")
        result = test_voice_id(test_key, voice_id, voice_name)
        
        if result["status"] == "OK":
            print(f"  [OK] Funciona - {result['file_size']} bytes gerados")
            working_voices.append((voice_name, voice_id))
        else:
            print(f"  [ERROR] {result.get('error', 'Unknown error')}")
        
        time.sleep(1)  # Pausa entre testes
    
    print()
    print(f"[SUMMARY] {len(working_voices)} voice IDs funcionais testados")
    
    if working_voices:
        print("[RECOMMENDATION] Voice IDs recomendados para usar:")
        for voice_name, voice_id in working_voices:
            print(f"  - {voice_name}: {voice_id}")
    
    # 4. Testar configuração atual do sistema
    config_file = project_root / "config" / "config_narracao_com_proxy.json"
    if config_file.exists():
        print(f"\n[INFO] Verificando configuração atual: {config_file}")
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            current_voice_id = config.get("voice", {}).get("voice_id", "")
            current_model = config.get("voice", {}).get("model_id", "")
            current_format = config.get("voice", {}).get("output_format", "")
            
            print(f"[CONFIG] Voice ID atual: {current_voice_id}")
            print(f"[CONFIG] Model ID atual: {current_model}")
            print(f"[CONFIG] Output format atual: {current_format}")
            
            if current_voice_id:
                print(f"[TEST] Testando configuração atual...")
                result = test_voice_id(test_key, current_voice_id, "Configuração Atual")
                
                if result["status"] == "OK":
                    print(f"  [OK] Configuração atual funciona!")
                else:
                    print(f"  [ERROR] Problema com configuração atual: {result.get('error')}")
                    print("  [HINT] Considere usar um dos voice IDs que funcionaram acima")
            
        except Exception as e:
            print(f"[ERROR] Erro ao ler configuração: {e}")
    
    # 5. Dicas finais
    print("\n" + "=" * 80)
    print("DICAS PARA RESOLVER PROBLEMAS:")
    print("=" * 80)
    
    if not working_voices:
        print("1. [VOICE_ID] Todos os voice IDs testados falharam:")
        print("   - Sua conta pode não ter acesso a vozes públicas")
        print("   - Use o endpoint /v1/voices para listar suas vozes disponíveis")
        print("   - Considere criar vozes personalizadas na sua conta")
    
    print("2. [ERRO_400] Se estiver recebendo erro 400:")
    print("   - Verifique se o voice_id existe e é acessível pela sua conta")
    print("   - Confirme se o model_id é suportado ('eleven_multilingual_v2' recomendado)")
    print("   - Teste com output_format simples como 'mp3_44100_128'")
    print("   - Reduza o tamanho do texto (máximo 5000 caracteres)")
    
    print("3. [CONFIGURAÇÃO] Para corrigir problemas:")
    print("   - Use voice IDs que funcionaram neste teste")
    print("   - Mantenha model_id como 'eleven_multilingual_v2'")
    print("   - Use output_format 'mp3_44100_128' (mais compatível)")
    print("   - Verifique se há caracteres especiais no texto")
    
    print("\n[INFO] Diagnóstico concluído!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
