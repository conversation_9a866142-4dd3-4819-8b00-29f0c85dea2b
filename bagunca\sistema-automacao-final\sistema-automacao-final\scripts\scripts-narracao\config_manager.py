"""
Módulo de configuração para o sistema de narração de roteiros

Este módulo é responsável por:
1. <PERSON><PERSON><PERSON> as configurações do arquivo config.json
2. Fornecer acesso às configurações para os outros módulos
3. Criar configurações padrão caso o arquivo não exista
"""

import os
import json
import logging
from typing import Dict, Any, Optional

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("narrador_roteiros.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("ConfigManager")


class ConfigManager:
    """
    Gerenciador de configurações para o sistema de narração de roteiros.
    """

    def __init__(self, config_file_path: str = "config.json"):
        """
        Inicializa o gerenciador de configurações.

        Args:
            config_file_path: Caminho para o arquivo de configuração
        """
        self.config_file_path = config_file_path
        self.config = self._load_config()

    def _create_default_config(self) -> Dict[str, Any]:
        """
        Cria uma configuração padrão.

        Returns:
            Dicionário com a configuração padrão
        """
        default_config = {
            "api": {
                "base_url": "https://api.elevenlabs.io",
                "max_retries": 3,
                "retry_delay": 2
            },
            "voice": {
                "voice_id": "Ir1QNHvhaJXbAGhT50w3",
                "voice_name": "Rachel",
                "model_id": "eleven_multilingual_v2",
                "output_format": "mp3_44100_128"
            },
            "processing": {
                "max_block_size": 2500,
                "respect_punctuation": True,
                "default_language": "pt"
            },
            "paths": {
                "output_directory": "audios_gerados",
                "log_file": "narrador_roteiros.log",
                "usage_data_file": "api_keys_usage.json"
            }
        }
        
        # Salva a configuração padrão
        try:
            with open(self.config_file_path, 'w') as f:
                json.dump(default_config, f, indent=2)
            logger.info(f"Arquivo de configuração padrão criado: {self.config_file_path}")
        except Exception as e:
            logger.error(f"Erro ao criar arquivo de configuração padrão: {str(e)}")
        
        return default_config

    def _load_config(self) -> Dict[str, Any]:
        """
        Carrega as configurações do arquivo.

        Returns:
            Dicionário com as configurações
        """
        try:
            if os.path.exists(self.config_file_path):
                with open(self.config_file_path, 'r') as f:
                    config = json.load(f)
                logger.info(f"Configurações carregadas de: {self.config_file_path}")
                return config
            else:
                logger.warning(f"Arquivo de configuração não encontrado: {self.config_file_path}")
                return self._create_default_config()
        except Exception as e:
            logger.error(f"Erro ao carregar configurações: {str(e)}")
            return self._create_default_config()

    def get_config(self, section: Optional[str] = None, key: Optional[str] = None) -> Any:
        """
        Obtém uma configuração.

        Args:
            section: Seção da configuração (opcional)
            key: Chave da configuração (opcional)

        Returns:
            Valor da configuração, seção ou configuração completa
        """
        if section is None:
            return self.config
        
        if section not in self.config:
            logger.warning(f"Seção não encontrada na configuração: {section}")
            return None
        
        if key is None:
            return self.config[section]
        
        if key not in self.config[section]:
            logger.warning(f"Chave não encontrada na seção {section}: {key}")
            return None
        
        return self.config[section][key]

    def save_config(self) -> bool:
        """
        Salva as configurações no arquivo.

        Returns:
            True se as configurações foram salvas com sucesso, False caso contrário
        """
        try:
            with open(self.config_file_path, 'w') as f:
                json.dump(self.config, f, indent=2)
            logger.info(f"Configurações salvas em: {self.config_file_path}")
            return True
        except Exception as e:
            logger.error(f"Erro ao salvar configurações: {str(e)}")
            return False

    def update_config(self, section: str, key: str, value: Any) -> bool:
        """
        Atualiza uma configuração.

        Args:
            section: Seção da configuração
            key: Chave da configuração
            value: Novo valor

        Returns:
            True se a configuração foi atualizada com sucesso, False caso contrário
        """
        try:
            if section not in self.config:
                self.config[section] = {}
            
            self.config[section][key] = value
            return self.save_config()
        except Exception as e:
            logger.error(f"Erro ao atualizar configuração: {str(e)}")
            return False


# Exemplo de uso
if __name__ == "__main__":
    # Inicializa o gerenciador de configurações
    config_manager = ConfigManager()
    
    # Obtém a configuração completa
    config = config_manager.get_config()
    print("Configuração completa:")
    print(json.dumps(config, indent=2))
    
    # Obtém uma seção específica
    api_config = config_manager.get_config("api")
    print("\nConfiguração da API:")
    print(json.dumps(api_config, indent=2))
    
    # Obtém um valor específico
    voice_id = config_manager.get_config("voice", "voice_id")
    print(f"\nID da voz: {voice_id}")
    
    # Atualiza uma configuração
    config_manager.update_config("processing", "max_block_size", 3000)
    print("\nTamanho máximo de bloco atualizado para 3000 caracteres")
    
    # Verifica a atualização
    max_block_size = config_manager.get_config("processing", "max_block_size")
    print(f"Novo tamanho máximo de bloco: {max_block_size}")
