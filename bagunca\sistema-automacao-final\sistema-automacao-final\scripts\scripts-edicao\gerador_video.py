import os
import re
import subprocess
import sys
import time
import traceback
import glob
import shlex
from pathlib import Path

# --- Configurações de Caminhos Relativos à Raiz do Projeto ---
# A raiz do projeto está duas pastas acima do diretório do script
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
# -----------------------------------------------------------

class VideoCompilerCLI:
    def __init__(self):
        self.files = {
            'musica': None,
            'narracao': None,
            'fundo': None,
            'personagem': None,
            'particulas': None,
            'elementos': None, # Logo agora é 'elementos' e opcional
            'legenda': None,
            'output': None # Armazenará o diretório de saída
        }

        # Diretórios padrão RELATIVOS À RAIZ DO PROJETO (PROJECT_ROOT)
        self.default_dirs = {
            'musica': [os.path.join("recursos", "musica")],
            'narracao': ["narracao"],
            'fundo': [os.path.join("recursos", "video")],
            'personagem': [os.path.join("recursos", "personagem")],
            'particulas': [os.path.join("recursos", "particulas")],
            'elementos': [os.path.join("recursos", "elementos")], # Pasta para elementos opcionais
            'legenda': ["legendas"],
            'output': ["videos_gerados"]
        }

        # Extensões de arquivo para cada tipo
        self.file_extensions = {
            'musica': ["mp3", "wav", "m4a", "ogg", "aac"],
            'narracao': ["mp3", "wav", "m4a", "ogg", "aac"],
            'fundo': ["mp4", "mov", "avi", "mkv"],
            'personagem': ["png", "webp"],
            'particulas': ["mp4", "mov", "avi", "mkv"],
            'elementos': ["png", "webp"], # Extensões para elementos
            'legenda': ["ass"],
            'output': ["mp4"]
        }

        # Índices para revezamento entre arquivos
        self.indices = {
            'musica': 0,
            'narracao': 0,
            'fundo': 0,
            'personagem': 0,
            'particulas': 0,
            'elementos': 0,
            'legenda': 0
        }

        # Listas de arquivos encontrados (caminhos absolutos)
        self.file_lists = {
            'musica': [],
            'narracao': [],
            'fundo': [],
            'personagem': [],
            'particulas': [],
            'elementos': [],
            'legenda': []
        }

    def find_files(self, base_dir, relative_dir_names, extensions):
        """Encontra arquivos com as extensões dadas nos diretórios especificados (relativos ao base_dir)."""
        files = []
        # Procura nas pastas específicas (construindo caminho absoluto)
        for rel_dir_name in relative_dir_names:
            dir_path = os.path.join(base_dir, rel_dir_name)
            if os.path.isdir(dir_path):
                for ext in extensions:
                    # Usar caminho absoluto para glob
                    files.extend(glob.glob(os.path.join(dir_path, f"*.{ext}")))
            # else:
            #     print(f"Aviso: Diretório não encontrado: {dir_path}") # Opcional: log se diretório não existe

        # Retorna caminhos absolutos para uso direto
        return [os.path.abspath(f) for f in files]

    def auto_detect_files(self):
        """Procura automaticamente os arquivos necessários nas pastas relativas a PROJECT_ROOT"""
        print(f"\nProcurando arquivos na raiz do projeto: {PROJECT_ROOT}")

        # Encontrar arquivos para cada tipo
        for key in self.files.keys():
            if key == 'output':
                # Para o arquivo de saída, criar diretório e definir caminho absoluto
                output_dir_rel = self.default_dirs['output'][0]
                output_dir_abs = os.path.join(PROJECT_ROOT, output_dir_rel)
                try:
                    os.makedirs(output_dir_abs, exist_ok=True)
                    self.files[key] = output_dir_abs # Armazena o caminho absoluto do diretório
                    print(f"→ Diretório de saída definido: {self.files[key]}")
                except OSError as e:
                    print(f"Erro ao criar diretório de saída {output_dir_abs}: {e}", file=sys.stderr)
                    # Decide se quer parar ou continuar sem diretório de saída
                    self.files[key] = None # Define como None se falhar
                    print(f"✗ Falha ao definir diretório de saída para: {key}")
                continue

            # Encontrar todos os arquivos do tipo atual usando PROJECT_ROOT como base
            found_files = self.find_files(
                PROJECT_ROOT,
                self.default_dirs[key],
                self.file_extensions[key]
            )

            # Armazenar a lista de arquivos encontrados (caminhos absolutos)
            self.file_lists[key] = found_files

            if found_files:
                # Selecionar o primeiro arquivo para uso inicial (será atualizado no loop)
                self.files[key] = found_files[0]
                print(f"✓ Encontrado {key}: {os.path.basename(self.files[key])} ({len(found_files)} arquivo(s) em {self.default_dirs[key][0]})")
            elif key != 'elementos': # Elementos são opcionais
                print(f"✗ Não encontrado: {key} (esperado em {self.default_dirs[key][0]})")
            else:
                 print(f"✓ {key} (opcional): Nenhum arquivo encontrado em {self.default_dirs[key][0]}.")

        return self.files

    def validate_inputs(self):
        """Verifica se todos os arquivos OBRIGATÓRIOS foram encontrados"""
        # 'elementos' e 'output' não são obrigatórios nesta fase
        required_keys = [k for k in self.files.keys() if k not in ['elementos', 'output']]
        missing_files = [k for k in required_keys if not self.file_lists[k]]

        if missing_files:
            print(f"\nErro: Os seguintes tipos de arquivos/pastas são necessários mas não foram encontrados:")
            for k in missing_files:
                 print(f"  - {k} (esperado em: {os.path.join(PROJECT_ROOT, self.default_dirs[k][0])})")
            return False
        # Verifica também se o diretório de saída foi criado com sucesso
        if not self.files.get('output') or not os.path.isdir(self.files['output']):
             print(f"\nErro: Diretório de saída não pôde ser criado ou definido: {os.path.join(PROJECT_ROOT, self.default_dirs['output'][0])}")
             return False
        return True

    def get_media_duration(self, file_path):
        """Retorna a duração de um arquivo de mídia em segundos usando ffprobe."""
        # file_path já deve ser absoluto vindo de find_files
        # abs_file_path = os.path.abspath(file_path)
        command = [
            "ffprobe",
            "-v", "error",
            "-show_entries", "format=duration",
            "-of", "default=noprint_wrappers=1:nokey=1",
            file_path # Usa o caminho absoluto diretamente
        ]
        try:
            result = subprocess.run(command, capture_output=True, text=True, check=True, encoding='utf-8')
            duration = float(result.stdout.strip())
            return duration
        except FileNotFoundError:
            print(f"Erro: ffprobe não encontrado. Certifique-se que o FFmpeg está instalado e no PATH.", file=sys.stderr)
            sys.exit(1)
        except subprocess.CalledProcessError as e:
            print(f"Erro ao executar ffprobe para {file_path}: {e}", file=sys.stderr)
            print(f"Stderr: {e.stderr}", file=sys.stderr)
            return None
        except ValueError as e:
            print(f"Erro ao converter a duração de {file_path} para float: {e}", file=sys.stderr)
            # Tenta imprimir o output mesmo assim
            try:
                print(f"Output do ffprobe: {result.stdout.strip()}", file=sys.stderr)
            except NameError:
                pass # result pode não estar definido
            return None

    def render_video(self, current_files, output_path):
        """Renderiza um único vídeo com os arquivos fornecidos (caminhos absolutos), mostrando progresso."""
        print(f"\nIniciando renderização para: {os.path.basename(output_path)}")
        try:
            # Obter a duração da narração
            print("Analisando duração da narração...")
            narracao_duration = self.get_media_duration(current_files['narracao'])
            if narracao_duration is None or narracao_duration <= 0:
                print(f"Erro ou duração inválida para {current_files['narracao']}. Pulando.", file=sys.stderr)
                return False
            print(f"Duração detectada: {narracao_duration:.3f}s")

            # --- Tratamento de Legendas --- 
            legenda_ass_path = None
            legenda_filter = ""
            legenda_path_orig = current_files.get("legenda") # Pega o caminho absoluto do arquivo .ass

            if legenda_path_orig and legenda_path_orig.lower().endswith(".ass"):
                if os.path.exists(legenda_path_orig):
                    legenda_ass_path = legenda_path_orig
                    print(f"  Usando Legenda: {os.path.basename(legenda_ass_path)}")
                    # Escapa o caminho para o filtro ffmpeg (necessário para Windows e caminhos com caracteres especiais)
                    legenda_escaped = legenda_ass_path.replace('\\', '/').replace(':', '\\:').replace("'", "\\\'")
                    legenda_filter = f",subtitles='{legenda_escaped}':force_style='PrimaryStyle=KaraokeStyle,Alignment=5'"
                else:
                    print(f"Aviso: Arquivo de legenda ASS especificado não encontrado: {legenda_path_orig}. Gerando sem legendas.")
            elif legenda_path_orig:
                print(f"Aviso: Arquivo de legenda inválido ou não é .ass: {legenda_path_orig}. Ignorando.")
            else:
                print("Nenhuma legenda (.ass) encontrada ou configurada para esta narração.")
            # --- Fim Tratamento de Legendas ---

            # --- Construir Comando FFmpeg --- #
            # Todos os caminhos em current_files são absolutos
            ffmpeg_cmd = ["ffmpeg", "-y"]
            filter_complex_parts = []
            stream_index = 0
            input_streams = [] # Para rastrear os inputs

            # Função auxiliar para adicionar input e retornar índice
            def add_input(file_path, options=None):
                nonlocal stream_index
                current_index = len(input_streams)
                if options:
                    ffmpeg_cmd.extend(options)
                ffmpeg_cmd.extend(["-i", file_path])
                input_streams.append(file_path)
                # stream_index += 1 # Não incrementar aqui, usar current_index
                return current_index

            # Input 0: Vídeo de fundo (loop)
            idx_fundo = add_input(current_files['fundo'], ["-stream_loop", "-1"])
            filter_complex_parts.append(f"[{idx_fundo}:v]scale=1280:720:force_original_aspect_ratio=decrease,pad=1280:720:(ow-iw)/2:(oh-ih)/2,setsar=1,eq=brightness=-0.05:contrast=1[fundo{idx_fundo}]")
            last_video_stream = f"[fundo{idx_fundo}]"

            # Input 1: Partículas (loop)
            idx_particulas = add_input(current_files['particulas'], ["-stream_loop", "-1"])
            filter_complex_parts.append(f"[{idx_particulas}:v]scale=1280:720,colorkey=black:0.1:0.1[particulas{idx_particulas}]")
            filter_complex_parts.append(f"{last_video_stream}[particulas{idx_particulas}]overlay=0:0:shortest=0[bg_part{idx_particulas}]")
            last_video_stream = f"[bg_part{idx_particulas}]"

            # Input 2: Personagem
            idx_personagem = add_input(current_files['personagem'])
            filter_complex_parts.append(f"[{idx_personagem}:v]format=rgba,pad=ceil(iw*1.4):ceil(ih*1.4):(ow-iw)/2:(oh-ih)/2:color=0x00000000,boxblur=5:1,eq=brightness=0.07[aura{idx_personagem}]")
            filter_complex_parts.append(f"[{idx_personagem}:v]format=rgba,scale=1280:720[personagem{idx_personagem}]")
            filter_complex_parts.append(f"{last_video_stream}[aura{idx_personagem}]overlay=(W-w)/2:(H-h)/2:shortest=0[with_aura{idx_personagem}]")
            filter_complex_parts.append(f"[with_aura{idx_personagem}][personagem{idx_personagem}]overlay=(W-w)/2:(H-h)/2:shortest=0[base{idx_personagem}]")
            last_video_stream = f"[base{idx_personagem}]"

            # Input 3: Elementos (Opcional)
            idx_elementos = -1
            if current_files.get('elementos'):
                idx_elementos = add_input(current_files['elementos'])
                filter_complex_parts.append(f"[{idx_elementos}:v]scale=-1:70[elementos{idx_elementos}]")
                filter_complex_parts.append(f"{last_video_stream}[elementos{idx_elementos}]overlay=10:10:shortest=0[with_element{idx_elementos}]")
                last_video_stream = f"[with_element{idx_elementos}]"

            # Input 4: Música
            idx_musica = add_input(current_files['musica'])

            # Input 5: Narração
            idx_narracao = add_input(current_files['narracao'])

            # Adicionar filtro de legenda (se houver)
            filter_complex_parts.append(f"{last_video_stream}null{legenda_filter}[video_final]")

            # Mixagem de áudio
            filter_complex_parts.append(f"[{idx_musica}:a]volume=0.03[music]")
            filter_complex_parts.append(f"[{idx_narracao}:a][music]amix=inputs=2:duration=first:dropout_transition=3[audio_final]")

            # Finalizar comando FFmpeg
            ffmpeg_cmd.extend([
                "-t", str(narracao_duration),
                "-filter_complex", ";".join(filter_complex_parts),
                "-map", "[video_final]",
                "-map", "[audio_final]",
                "-c:v", "libx264",
                "-preset", "veryfast",
                "-crf", "23",
                "-c:a", "aac",
                "-b:a", "192k",
                "-r", "27",
                "-pix_fmt", "yuv420p",
                output_path # Caminho absoluto para o arquivo de saída
            ])

            print("\n  Comando FFmpeg:")
            print(" ".join(shlex.quote(str(arg)) for arg in ffmpeg_cmd))

            # --- Executar FFmpeg com Popen --- 
            print("\nIniciando renderização com FFmpeg (isso pode demorar)...")
            start_time = time.time()

            process = subprocess.Popen(
                ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                bufsize=1,
                universal_newlines=True,
                encoding="utf-8",
                errors='replace' # Adicionado para lidar com possíveis erros de decodificação
            )

            # Monitoramento de progresso
            last_progress_line = ""
            for line in process.stdout:
                if "frame=" in line or "time=" in line or "size=" in line:
                    current_progress_line = line.strip()
                    if current_progress_line != last_progress_line:
                        print(f"\rProgresso: {current_progress_line}", end="")
                        last_progress_line = current_progress_line
                # else:
                    # Opcional: imprimir outras linhas se precisar debugar
                    # print(line.strip())

            process.wait()
            total_time = time.time() - start_time
            print() # Nova linha após progresso

            if process.returncode == 0:
                print(f"\nVídeo renderizado com sucesso em {time.strftime('%H:%M:%S', time.gmtime(total_time))}!")
                print(f"Arquivo salvo em: {os.path.abspath(output_path)}")
                return True
            else:
                print(f"\nErro: FFmpeg retornou código {process.returncode}")
                # A saída de erro já foi (ou deveria ter sido) impressa no loop acima
                return False

        except Exception as e:
            print(f"\nErro inesperado durante a renderização: {str(e)}")
            traceback.print_exc()
            return False

    def process_all_narracao_files(self):
        """Processa todos os arquivos de narração, revezando os outros recursos"""
        if not self.file_lists['narracao']:
            print("Nenhum arquivo de narração encontrado para processar.")
            return False

        total_narracoes = len(self.file_lists['narracao'])
        print(f"\nProcessando {total_narracoes} arquivos de narração em lote...")

        # Reiniciar índices
        self.indices = {k: 0 for k in self.indices}
        output_base_dir = self.files['output'] # Diretório base de saída (absoluto)

        success_count = 0
        fail_count = 0

        for i, narracao_file in enumerate(self.file_lists['narracao']):
            print(f"\n--- Processando Narração {i+1}/{total_narracoes}: {os.path.basename(narracao_file)} ---")

            current_files = {}
            # Selecionar arquivos para esta iteração, usando revezamento
            for key in self.files.keys():
                if key == 'output': continue # Ignora output aqui

                file_list = self.file_lists[key]
                if not file_list:
                    if key != 'elementos': # Se for obrigatório e não tiver, é um erro (já validado antes)
                        print(f"Erro crítico: Lista de arquivos vazia para '{key}'. Interrompendo.", file=sys.stderr)
                        return False
                    else:
                        current_files[key] = None # Elemento é opcional
                        continue

                # Lógica de revezamento
                current_index = self.indices[key]
                selected_file = file_list[current_index % len(file_list)]
                current_files[key] = selected_file
                print(f"  Usando {key}: {os.path.basename(selected_file)}")

                # Atualizar índice apenas se não for narração (narração avança a cada loop principal)
                if key != 'narracao':
                    self.indices[key] += 1

            # Definir caminho de saída específico para esta narração
            narracao_basename = Path(narracao_file).stem
            output_filename = f"{narracao_basename}.mp4"
            output_path = os.path.join(output_base_dir, output_filename)

            # Verificar se legenda correspondente existe (mesmo nome base)
            legenda_basename = f"{narracao_basename}.ass"
            legenda_path = os.path.join(PROJECT_ROOT, self.default_dirs['legenda'][0], legenda_basename)
            if os.path.exists(legenda_path):
                current_files['legenda'] = legenda_path # Adiciona/sobrescreve a legenda
                print(f"  Legenda correspondente encontrada: {os.path.basename(legenda_path)}")
            else:
                current_files['legenda'] = None # Garante que não use legenda antiga se não houver correspondente
                print(f"  Aviso: Legenda correspondente não encontrada ({legenda_basename}).")

            # Renderizar o vídeo
            if self.render_video(current_files, output_path):
                success_count += 1
            else:
                fail_count += 1
                print(f"Falha ao processar narração: {os.path.basename(narracao_file)}")

        print(f"\n--- Processamento em Lote Concluído ---")
        print(f"Total de vídeos que deveriam ser gerados: {total_narracoes}")
        print(f"Sucessos: {success_count}")
        print(f"Falhas: {fail_count}")
        print(f"Vídeos gerados estão em: {output_base_dir}")
        return success_count > 0 and fail_count == 0 # Retorna True se tudo ocorreu bem

def main():
    print("Iniciando Gerador de Vídeo Automático...")
    compiler = VideoCompilerCLI()

    # Detectar arquivos usando PROJECT_ROOT como base
    compiler.auto_detect_files()

    # Validar se os arquivos obrigatórios foram encontrados
    if not compiler.validate_inputs():
        print("\nVerifique os arquivos ausentes e tente novamente.")
        sys.exit(1)

    # Processar todos os arquivos de narração
    compiler.process_all_narracao_files()

if __name__ == "__main__":
    main()