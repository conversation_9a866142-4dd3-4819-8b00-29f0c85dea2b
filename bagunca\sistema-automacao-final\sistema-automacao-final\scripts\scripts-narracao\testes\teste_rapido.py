#!/usr/bin/env python3
"""
Teste Rápido - Corrige Voice_ID e Testa Imediatamente

Este script testa diretamente com voice_id correto para
confirmar que o problema é só o voice_id inválido.
"""

import requests
import json
from pathlib import Path

def test_with_correct_voice_id():
    """Testa TTS com voice_id correto."""
    
    # Lê primeira chave do arquivo
    keys_file = Path("C:\\Users\\<USER>\\Desktop\\automação completa\\sistema-automacao-final\\sistema-automacao-final\\config\\chaves-api-elevenlabs.txt")
    
    if not keys_file.exists():
        print("[ERRO] Arquivo de chaves não encontrado:", keys_file)
        return False
    
    with open(keys_file, 'r') as f:
        api_key = f.readline().strip()
    
    if not api_key:
        print("[ERRO] Nenhuma chave encontrada no arquivo")
        return False
    
    print(f"[TESTE] Usando chave: {api_key[:8]}...")
    
    # Voice IDs mais comuns da ElevenLabs para testar
    voice_ids_to_test = [
        ("21m00Tcm4TlvDq81kWAM", "Rachel (padrão)"),
        ("AZnzlk1XvdvUeBnXmlld", "Domi"),
        ("EXAVITQu4vr4xnSDxMaL", "Bella"),
        ("ErXwobaYiN019PkySvjV", "Antoni"),
        ("MF3mGyEYCl7XYWbV9V6O", "Elli"),
        ("TxGEqnHWrfWFTfGW9XjX", "Josh"),
        ("VR6AewLTigWG4xSOukaG", "Arnold"),
        ("pNInz6obpgDQGcFmaJgB", "Adam"),
        ("yoZ06aMxZJJ28mfd3POQ", "Sam")
    ]
    
    # Configurações para testar
    test_configs = [
        {"model": "eleven_monolingual_v1", "format": "mp3_22050_32"},
        {"model": "eleven_multilingual_v1", "format": "mp3_22050_32"},
        {"model": "eleven_multilingual_v2", "format": "mp3_22050_32"}
    ]
    
    headers = {
        "xi-api-key": api_key,
        "Content-Type": "application/json",
        "Accept": "audio/mpeg"
    }
    
    test_text = "Este é um teste rápido do sistema."
    
    print("\n[TESTE] Testando voice_ids e configurações...")
    
    for voice_id, voice_name in voice_ids_to_test:
        print(f"\n[VOICE] Testando {voice_name} ({voice_id})...")
        
        for config in test_configs:
            model = config["model"]
            format_type = config["format"]
            
            payload = {
                "text": test_text,
                "model_id": model,
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.5
                }
            }
            
            try:
                response = requests.post(
                    f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}",
                    headers=headers,
                    params={"output_format": format_type},
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    print(f"  ✅ FUNCIONOU! Model: {model}, Format: {format_type}")
                    print(f"     Áudio gerado: {len(response.content)} bytes")
                    
                    # Salva configuração funcionando
                    working_config = {
                        "voice_id": voice_id,
                        "voice_name": voice_name,
                        "model_id": model,
                        "output_format": format_type
                    }
                    
                    config_file = Path("../../config/working_config.json")
                    with open(config_file, 'w') as f:
                        json.dump(working_config, f, indent=2)
                    
                    print(f"  📁 Configuração salva em: {config_file}")
                    return True
                    
                elif response.status_code == 400:
                    print(f"  ❌ Erro 400: {model} + {format_type}")
                elif response.status_code == 401:
                    print(f"  🔑 Erro 401: Chave inválida")
                    return False
                else:
                    print(f"  ⚠️  Erro {response.status_code}: {model} + {format_type}")
                    
            except Exception as e:
                print(f"  💥 Exceção: {e}")
                continue
    
    print("\n❌ Nenhuma configuração funcionou!")
    return False

def update_main_config():
    """Atualiza configuração principal com configuração funcionando."""
    working_config_file = Path("../../config/working_config.json")
    main_config_file = Path("../../config/config_narracao_com_proxy.json")
    
    if not working_config_file.exists():
        print("[AVISO] Configuração funcionando não encontrada")
        return False
    
    if not main_config_file.exists():
        print("[AVISO] Configuração principal não encontrada")
        return False
    
    # Lê configuração funcionando
    with open(working_config_file, 'r') as f:
        working_config = json.load(f)
    
    # Lê configuração principal
    with open(main_config_file, 'r') as f:
        main_config = json.load(f)
    
    # Atualiza configuração principal
    main_config["voice"]["voice_id"] = working_config["voice_id"]
    main_config["voice"]["voice_name"] = working_config["voice_name"]
    main_config["voice"]["model_id"] = working_config["model_id"]
    main_config["voice"]["output_format"] = working_config["output_format"]
    
    # Salva configuração principal atualizada
    with open(main_config_file, 'w') as f:
        json.dump(main_config, f, indent=2)
    
    print(f"[OK] Configuração principal atualizada!")
    print(f"  Voice: {working_config['voice_name']}")
    print(f"  ID: {working_config['voice_id']}")
    print(f"  Model: {working_config['model_id']}")
    print(f"  Format: {working_config['output_format']}")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("TESTE RÁPIDO - CORREÇÃO DE VOICE_ID")
    print("=" * 60)
    
    success = test_with_correct_voice_id()
    
    if success:
        print("\n🎉 SUCESSO! Configuração funcionando encontrada!")
        
        if update_main_config():
            print("\n✅ PRONTO! Agora execute:")
            print("   python narrador_com_proxy_corrigido.py")
        else:
            print("\n⚠️  Configure manualmente o voice_id encontrado")
    else:
        print("\n💥 FALHA! Possíveis problemas:")
        print("   1. Chaves API inválidas/expiradas")
        print("   2. Conta ElevenLabs suspensa")
        print("   3. Problema de conectividade")
        print("\n🔍 Verifique suas chaves em: https://elevenlabs.io/")
