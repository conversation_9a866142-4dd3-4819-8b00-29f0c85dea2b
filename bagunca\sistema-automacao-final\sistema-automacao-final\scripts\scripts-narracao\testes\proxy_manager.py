"""
Módulo de Gerenciamento de Proxies Webshare.io

Este módulo é responsável por:
1. Gerenciar proxies da Webshare.io com rotação automática
2. Fornecer configuração de proxy para requisições HTTP
3. Monitorar uso e status dos proxies
4. Implementar fallback em caso de falhas
"""

import os
import time
import requests
import logging
from typing import Dict, List, Optional, Tuple, Any
import json
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class ProxyConfig:
    """Configuração de um proxy"""
    proxy_address: str
    port: int
    username: str
    password: str
    country: str
    city: str
    valid: bool
    last_verification: str

class WebshareProxyManager:
    """
    Gerenciador de proxies da Webshare.io com rotação automática.
    """
    
    WEBSHARE_API_BASE = "https://proxy.webshare.io/api/v2"
    ROTATING_ENDPOINT = "p.webshare.io"
    ROTATING_PORT = 80
    
    def __init__(self, 
                 api_token: str,
                 use_rotating_endpoint: bool = True,
                 enable_fallback: bool = True,
                 request_timeout: int = 30):
        """
        Inicializa o gerenciador de proxies.
        
        Args:
            api_token: Token de API da Webshare.io
            use_rotating_endpoint: Se True, usa endpoint rotativo automático
            enable_fallback: Se True, permite fallback para proxies diretos
            request_timeout: Timeout para requisições em segundos
        """
        self.api_token = api_token
        self.use_rotating_endpoint = use_rotating_endpoint
        self.enable_fallback = enable_fallback
        self.request_timeout = request_timeout
        
        # Configurações do proxy
        self.rotating_proxy_config = None
        self.direct_proxies: List[ProxyConfig] = []
        self.current_direct_proxy_index = 0
        
        # Estatísticas
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.last_rotation_time = time.time()
        
        # Cache de configuração
        self._config_cache = None
        self._config_cache_time = 0
        self._config_cache_duration = 3600  # 1 hora
        
        logger.info("Inicializando Webshare Proxy Manager...")
        self._initialize_proxies()

    def _get_api_headers(self) -> Dict[str, str]:
        """Retorna headers para requisições à API da Webshare."""
        return {
            "Authorization": f"Token {self.api_token}",
            "Content-Type": "application/json"
        }

    def _initialize_proxies(self) -> None:
        """Inicializa a configuração dos proxies."""
        try:
            # Obtém configuração da conta
            config = self._get_proxy_config()
            
            if config:
                self.rotating_proxy_config = {
                    "username": config.get("username"),
                    "password": config.get("password"),
                    "countries": config.get("countries", {}),
                    "total_proxies": sum(config.get("countries", {}).values())
                }
                
                logger.info(f"✅ Configuração obtida: {self.rotating_proxy_config['total_proxies']} proxies disponíveis")
                logger.info(f"📍 Países disponíveis: {list(self.rotating_proxy_config['countries'].keys())}")
                
                # Se fallback estiver habilitado, carrega lista de proxies diretos
                if self.enable_fallback:
                    self._load_direct_proxies()
            else:
                raise Exception("Falha ao obter configuração da API")
                
        except Exception as e:
            logger.error(f"Erro ao inicializar proxies: {e}")
            raise

    def _get_proxy_config(self) -> Optional[Dict[str, Any]]:
        """
        Obtém configuração da conta da Webshare (com cache).
        """
        current_time = time.time()
        
        # Verifica cache
        if (self._config_cache and 
            current_time - self._config_cache_time < self._config_cache_duration):
            return self._config_cache
        
        try:
            response = requests.get(
                f"{self.WEBSHARE_API_BASE}/proxy/config/",
                headers=self._get_api_headers(),
                timeout=self.request_timeout
            )
            
            if response.status_code == 200:
                config = response.json()
                # Atualiza cache
                self._config_cache = config
                self._config_cache_time = current_time
                return config
            else:
                logger.error(f"Erro ao obter configuração: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Erro na requisição de configuração: {e}")
            return None

    def _load_direct_proxies(self) -> None:
        """Carrega lista de proxies diretos para fallback."""
        try:
            response = requests.get(
                f"{self.WEBSHARE_API_BASE}/proxy/list/?mode=direct&page_size=100",
                headers=self._get_api_headers(),
                timeout=self.request_timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                proxies_data = data.get("results", [])
                
                self.direct_proxies = [
                    ProxyConfig(
                        proxy_address=p["proxy_address"],
                        port=p["port"],
                        username=p["username"],
                        password=p["password"],
                        country=p.get("country_code", "unknown"),
                        city=p.get("city_name", "unknown"),
                        valid=p.get("valid", False),
                        last_verification=p.get("last_verification", "")
                    )
                    for p in proxies_data
                    if p.get("valid", False)  # Apenas proxies válidos
                ]
                
                logger.info(f"📋 Carregados {len(self.direct_proxies)} proxies diretos para fallback")
                
            else:
                logger.warning(f"Não foi possível carregar proxies diretos: HTTP {response.status_code}")
                
        except Exception as e:
            logger.warning(f"Erro ao carregar proxies diretos: {e}")

    def get_rotating_proxy_dict(self) -> Dict[str, str]:
        """
        Retorna configuração de proxy para uso com requests (rotação automática).
        
        Returns:
            Dicionário com configuração de proxy no formato requests
        """
        if not self.rotating_proxy_config:
            raise Exception("Configuração de proxy não disponível")
        
        username = self.rotating_proxy_config["username"]
        password = self.rotating_proxy_config["password"]
        
        # Formato especial para rotação automática
        proxy_url = f"http://{username}-rotate:{password}@{self.ROTATING_ENDPOINT}:{self.ROTATING_PORT}"
        
        return {
            "http": proxy_url,
            "https": proxy_url
        }

    def get_country_specific_proxy_dict(self, country_code: str) -> Dict[str, str]:
        """
        Retorna configuração de proxy para país específico.
        
        Args:
            country_code: Código do país (ex: 'US', 'BR', 'DE')
            
        Returns:
            Dicionário com configuração de proxy
        """
        if not self.rotating_proxy_config:
            raise Exception("Configuração de proxy não disponível")
        
        available_countries = self.rotating_proxy_config.get("countries", {})
        if country_code not in available_countries:
            logger.warning(f"País {country_code} não disponível. Países: {list(available_countries.keys())}")
            return self.get_rotating_proxy_dict()
        
        username = self.rotating_proxy_config["username"]
        password = self.rotating_proxy_config["password"]
        
        # Formato para país específico
        proxy_url = f"http://{username}-country-{country_code}:{password}@{self.ROTATING_ENDPOINT}:{self.ROTATING_PORT}"
        
        return {
            "http": proxy_url,
            "https": proxy_url
        }

    def get_direct_proxy_dict(self) -> Optional[Dict[str, str]]:
        """
        Retorna configuração de proxy direto (fallback).
        
        Returns:
            Dicionário com configuração de proxy direto ou None se não disponível
        """
        if not self.direct_proxies:
            return None
        
        # Rotaciona entre proxies diretos
        proxy = self.direct_proxies[self.current_direct_proxy_index]
        self.current_direct_proxy_index = (self.current_direct_proxy_index + 1) % len(self.direct_proxies)
        
        proxy_url = f"http://{proxy.username}:{proxy.password}@{proxy.proxy_address}:{proxy.port}"
        
        return {
            "http": proxy_url,
            "https": proxy_url
        }

    def make_request_with_proxy(self, 
                              method: str, 
                              url: str, 
                              use_country_proxy: Optional[str] = None,
                              max_retries: int = 3,
                              **kwargs) -> requests.Response:
        """
        Faz requisição HTTP com proxy e retry automático.
        
        Args:
            method: Método HTTP ('GET', 'POST', etc.)
            url: URL da requisição
            use_country_proxy: Código do país para proxy específico (opcional)
            max_retries: Número máximo de tentativas
            **kwargs: Argumentos adicionais para requests
            
        Returns:
            Response da requisição
            
        Raises:
            Exception: Se todas as tentativas falharem
        """
        self.total_requests += 1
        
        # Tenta primeiro com proxy rotativo
        for attempt in range(max_retries):
            try:
                # Seleciona configuração de proxy
                if use_country_proxy:
                    proxy_config = self.get_country_specific_proxy_dict(use_country_proxy)
                    proxy_type = f"country-{use_country_proxy}"
                else:
                    proxy_config = self.get_rotating_proxy_dict()
                    proxy_type = "rotating"
                
                # Configura timeout se não especificado
                if 'timeout' not in kwargs:
                    kwargs['timeout'] = self.request_timeout
                
                # Faz a requisição
                response = requests.request(
                    method=method,
                    url=url,
                    proxies=proxy_config,
                    **kwargs
                )
                
                # Sucesso
                self.successful_requests += 1
                logger.debug(f"✅ Requisição bem-sucedida via proxy {proxy_type} (tentativa {attempt + 1})")
                return response
                
            except Exception as e:
                logger.warning(f"❌ Falha na tentativa {attempt + 1} com proxy {proxy_type}: {e}")
                
                # Se é a última tentativa com proxy rotativo, tenta fallback
                if attempt == max_retries - 1 and self.enable_fallback:
                    return self._try_direct_proxy_fallback(method, url, **kwargs)
                
                # Aguarda antes da próxima tentativa
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Backoff exponencial
        
        self.failed_requests += 1
        raise Exception(f"Falha em todas as {max_retries} tentativas de requisição com proxy")

    def _try_direct_proxy_fallback(self, method: str, url: str, **kwargs) -> requests.Response:
        """Tenta requisição com proxy direto como fallback."""
        if not self.direct_proxies:
            raise Exception("Nenhum proxy direto disponível para fallback")
        
        logger.info("🔄 Tentando fallback com proxy direto...")
        
        # Tenta alguns proxies diretos
        for _ in range(min(3, len(self.direct_proxies))):
            try:
                proxy_config = self.get_direct_proxy_dict()
                if proxy_config:
                    response = requests.request(
                        method=method,
                        url=url,
                        proxies=proxy_config,
                        **kwargs
                    )
                    self.successful_requests += 1
                    logger.info("✅ Sucesso com proxy direto fallback")
                    return response
                    
            except Exception as e:
                logger.warning(f"Falha no proxy direto: {e}")
                continue
        
        raise Exception("Falha também nos proxies diretos de fallback")

    def get_proxy_status(self) -> Dict[str, Any]:
        """
        Retorna status e estatísticas dos proxies.
        
        Returns:
            Dicionário com informações de status
        """
        config = self._get_proxy_config()
        
        success_rate = (self.successful_requests / self.total_requests * 100) if self.total_requests > 0 else 0
        
        return {
            "proxy_config": {
                "rotating_available": bool(self.rotating_proxy_config),
                "total_proxies": self.rotating_proxy_config.get("total_proxies", 0) if self.rotating_proxy_config else 0,
                "countries": list(self.rotating_proxy_config.get("countries", {}).keys()) if self.rotating_proxy_config else [],
                "direct_proxies_loaded": len(self.direct_proxies),
                "fallback_enabled": self.enable_fallback
            },
            "statistics": {
                "total_requests": self.total_requests,
                "successful_requests": self.successful_requests,
                "failed_requests": self.failed_requests,
                "success_rate_percent": round(success_rate, 2),
                "uptime_minutes": round((time.time() - self.last_rotation_time) / 60, 2)
            },
            "account_info": config if config else None
        }

    def test_proxy_connection(self, test_url: str = "https://httpbin.org/ip") -> Dict[str, Any]:
        """
        Testa conectividade dos proxies.
        
        Args:
            test_url: URL para teste de conectividade
            
        Returns:
            Resultado dos testes
        """
        results = {
            "rotating_proxy": None,
            "direct_proxy": None,
            "no_proxy": None
        }
        
        # Teste sem proxy
        try:
            response = requests.get(test_url, timeout=10)
            results["no_proxy"] = {
                "success": True,
                "ip": response.json().get("origin"),
                "response_time": response.elapsed.total_seconds()
            }
        except Exception as e:
            results["no_proxy"] = {"success": False, "error": str(e)}
        
        # Teste com proxy rotativo
        try:
            proxy_config = self.get_rotating_proxy_dict()
            response = requests.get(test_url, proxies=proxy_config, timeout=15)
            results["rotating_proxy"] = {
                "success": True,
                "ip": response.json().get("origin"),
                "response_time": response.elapsed.total_seconds()
            }
        except Exception as e:
            results["rotating_proxy"] = {"success": False, "error": str(e)}
        
        # Teste com proxy direto (se disponível)
        if self.direct_proxies:
            try:
                proxy_config = self.get_direct_proxy_dict()
                if proxy_config:
                    response = requests.get(test_url, proxies=proxy_config, timeout=15)
                    results["direct_proxy"] = {
                        "success": True,
                        "ip": response.json().get("origin"),
                        "response_time": response.elapsed.total_seconds()
                    }
            except Exception as e:
                results["direct_proxy"] = {"success": False, "error": str(e)}
        
        return results

# Exemplo de uso
if __name__ == "__main__":
    # Configuração de logging
    logging.basicConfig(level=logging.INFO)
    
    # Inicializa com a API key fornecida
    API_TOKEN = "mt643rsq556yqggigaa3emmmjmdycc2h6868bi7b"
    
    try:
        proxy_manager = WebshareProxyManager(
            api_token=API_TOKEN,
            use_rotating_endpoint=True,
            enable_fallback=True
        )
        
        print("\n" + "="*60)
        print("🚀 WEBSHARE PROXY MANAGER INICIALIZADO")
        print("="*60)
        
        # Mostra status
        status = proxy_manager.get_proxy_status()
        print(f"\n📊 STATUS DOS PROXIES:")
        print(f"   Total de proxies: {status['proxy_config']['total_proxies']}")
        print(f"   Países disponíveis: {', '.join(status['proxy_config']['countries'])}")
        print(f"   Proxies diretos: {status['proxy_config']['direct_proxies_loaded']}")
        print(f"   Fallback habilitado: {status['proxy_config']['fallback_enabled']}")
        
        # Teste de conectividade
        print(f"\n🧪 TESTANDO CONECTIVIDADE...")
        test_results = proxy_manager.test_proxy_connection()
        
        for proxy_type, result in test_results.items():
            if result:
                status_icon = "✅" if result.get("success") else "❌"
                if result.get("success"):
                    print(f"   {status_icon} {proxy_type}: IP {result.get('ip')} ({result.get('response_time', 0):.2f}s)")
                else:
                    print(f"   {status_icon} {proxy_type}: {result.get('error', 'Erro desconhecido')}")
        
        # Exemplo de requisição
        print(f"\n🌐 EXEMPLO DE REQUISIÇÃO COM PROXY:")
        try:
            response = proxy_manager.make_request_with_proxy(
                method="GET",
                url="https://httpbin.org/ip"
            )
            ip_info = response.json()
            print(f"   ✅ Requisição bem-sucedida!")
            print(f"   📍 IP utilizado: {ip_info.get('origin')}")
            print(f"   ⏱️  Tempo de resposta: {response.elapsed.total_seconds():.2f}s")
        except Exception as e:
            print(f"   ❌ Erro na requisição: {e}")
        
        # Estatísticas finais
        final_status = proxy_manager.get_proxy_status()
        stats = final_status["statistics"]
        print(f"\n📈 ESTATÍSTICAS:")
        print(f"   Total de requisições: {stats['total_requests']}")
        print(f"   Sucessos: {stats['successful_requests']}")
        print(f"   Falhas: {stats['failed_requests']}")
        print(f"   Taxa de sucesso: {stats['success_rate_percent']}%")
        
    except Exception as e:
        print(f"\n❌ ERRO: {e}")
        print("Verifique sua API key e conexão com a internet.")
