"""
Teste simples usando a versão original (sem proxy) para pegar voice_id válido
"""

import sys
from pathlib import Path

# Adiciona o caminho dos módulos
sys.path.append(str(Path(__file__).parent))

from api_key_manager import APIKeyManager
from elevenlabs_api_atualizado import ElevenLabsAPI  # Versão original sem proxy

def main():
    keys_file = "chaves-api-elevenlabs.txt"
    
    print("🔍 TESTE SIMPLES: LISTANDO VOZES (SEM PROXY)")
    print("="*60)
    
    try:
        # 1. Inicializa manager de chaves
        print("1️⃣ Inicializando gerenciador de chaves...")
        key_manager = APIKeyManager(keys_file)
        
        # 2. Inicializa cliente ElevenLabs (versão original - SEM proxy_manager)
        print("2️⃣ Inicializando cliente ElevenLabs (versão original)...")
        elevenlabs_api = ElevenLabsAPI(
            api_key_manager=key_manager
            # NÃO passa proxy_manager - usa versão original
        )
        
        # 3. Lista vozes
        print("3️⃣ Obtendo lista de vozes...")
        voices = elevenlabs_api.list_voices()
        
        print(f"\n✅ VOZES DISPONÍVEIS ({len(voices.get('voices', []))} encontradas):")
        print("-" * 60)
        
        # Mostra primeiras 10 vozes
        for i, voice in enumerate(voices.get('voices', [])[:10], 1):
            voice_id = voice.get('voice_id', 'N/A')
            name = voice.get('name', 'N/A')
            category = voice.get('category', 'N/A')
            
            print(f"{i:2d}. {name}")
            print(f"    ID: {voice_id}")
            print(f"    Categoria: {category}")
            print()
        
        # Pega primeiro voice_id válido
        if voices.get('voices'):
            first_voice = voices['voices'][0]
            voice_id = first_voice['voice_id']
            voice_name = first_voice['name']
            
            print(f"🎯 VOICE_ID VÁLIDO ENCONTRADO:")
            print(f"   ID: {voice_id}")
            print(f"   Nome: {voice_name}")
            
            # 4. Teste rápido de TTS (SEM proxy)
            print(f"\n4️⃣ Testando TTS com voice_id válido...")
            text = "Olá! Este é um teste com voice_id válido."
            output_path = "teste_voice_valido_sem_proxy.mp3"
            
            generated_file = elevenlabs_api.text_to_speech_with_auto_retry(
                text=text,
                output_path=output_path,
                voice_id=voice_id,
                language_code="pt-BR"
            )
            
            print(f"✅ SUCESSO! Arquivo gerado: {generated_file}")
            print(f"📊 Tamanho: {Path(generated_file).stat().st_size / 1024:.1f} KB")
            
            # 5. Salva voice_id para usar depois
            with open("voice_id_valido.txt", "w") as f:
                f.write(f"{voice_id}\n")
                f.write(f"# {voice_name}\n")
            
            print(f"\n💾 Voice ID salvo em: voice_id_valido.txt")
            print(f"\n🔧 PRÓXIMO PASSO:")
            print(f"   Use este voice_id no config.json:")
            print(f"   \"voice_id\": \"{voice_id}\"")
            
        return 0
        
    except Exception as e:
        print(f"\n❌ ERRO: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
