"""
Módulo de Comunicação com a API do ElevenLabs (Versão Melhorada)

Este módulo é responsável por:
1. Estabelecer a comunicação com a API do ElevenLabs usando o APIKeyManager aprimorado.
2. Enviar requisições de conversão de texto em áudio.
3. Processar as respostas e salvar os arquivos de áudio.
4. Lidar com erros e retentativas em caso de falhas.
5. Suportar configuração de idioma para modelos compatíveis.
"""

import os
import time
import requests
import json
from typing import Dict, List, Optional, Tuple, Union, BinaryIO
from pathlib import Path

# Importa o gerenciador de chaves API sequencial
from api_key_manager_sequential import SequentialAPIKeyManager


class ElevenLabsAPI:
    """
    Cliente para comunicação com a API do ElevenLabs.
    Utiliza o APIKeyManager para seleção de chave baseada em disponibilidade real.
    """

    # Mapeamento de códigos de idioma para language_code do ElevenLabs
    LANGUAGE_CODES = {
        "en": "en", "de": "de", "es": "es", "fr": "fr", "hi": "hi",
        "it": "it", "ja": "ja", "ko": "ko", "nl": "nl", "no": "no",
        "pl": "pl", "pt-BR": "pt-BR", "ro": "ro", "ru": "ru", "sv": "sv",
        "tr": "tr", "zh": "zh"
    }

    def __init__(self,
                 api_key_manager: SequentialAPIKeyManager,
                 base_url: str = "https://api.elevenlabs.io",
                 model_id: str = "eleven_multilingual_v2",
                 voice_id: str = "Ir1QNHvhaJXbAGhT50w3",  # Sara Martin 1
                 output_format: str = "mp3_44100_128",
                 max_retries: int = 3,
                 retry_delay: int = 5): # Aumentado delay para dar tempo à API/sincronização
        """
        Inicializa o cliente da API do ElevenLabs.

        Args:
            api_key_manager: Instância do APIKeyManager aprimorado.
            base_url: URL base da API do ElevenLabs.
            model_id: ID do modelo a ser utilizado.
            voice_id: ID da voz a ser utilizada.
            output_format: Formato de saída do áudio.
            max_retries: Número máximo de tentativas em caso de falha.
            retry_delay: Tempo de espera entre tentativas (em segundos).
        """
        self.api_key_manager = api_key_manager
        self.base_url = base_url
        self.model_id = model_id
        self.voice_id = voice_id
        self.output_format = output_format
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def _get_headers(self, api_key: str) -> Dict[str, str]:
        """
        Retorna os cabeçalhos HTTP para as requisições.
        """
        return {
            "xi-api-key": api_key,
            "Content-Type": "application/json",
            "Accept": "audio/mpeg" # Ou outro formato se output_format for diferente
        }

    def list_voices(self) -> Dict:
        """
        Lista todas as vozes disponíveis.
        Utiliza uma chave API obtida pelo gerenciador.
        """
        endpoint = f"{self.base_url}/v1/voices" # Corrigido para v1
        try:
            # Obtém uma chave API (não precisa de contagem específica aqui)
            # Usamos 1 caractere como requisito mínimo apenas para obter uma chave válida.
            api_key, _ = self.api_key_manager.get_available_key(required_chars=1)
            
            headers = self._get_headers(api_key)
            headers.pop("Accept") # GET não precisa de Accept: audio/mpeg
            
            response = requests.get(endpoint, headers=headers)
            response.raise_for_status()
            return response.json()
        except ValueError as e:
            error_msg = f"Erro ao obter chave para listar vozes: {e}"
            print(error_msg)
            raise Exception(error_msg)
        except requests.exceptions.RequestException as e:
            error_msg = f"Erro ao listar vozes: {e}"
            print(error_msg)
            raise Exception(error_msg)

    def text_to_speech(self,
                       text: str,
                       output_path: str,
                       api_key: str, # NOVO: Chave API explícita
                       voice_id: Optional[str] = None,
                       model_id: Optional[str] = None,
                       output_format: Optional[str] = None,
                       language_code: Optional[str] = None,
                       voice_settings: Optional[Dict] = None) -> str:
        """
        Converte texto em áudio e salva o resultado em um arquivo.
        Utiliza a chave API fornecida explicitamente.

        Args:
            text: Texto a ser convertido em áudio.
            output_path: Caminho para salvar o arquivo de áudio.
            api_key: A chave API da ElevenLabs a ser usada para esta requisição.
            voice_id: ID da voz a ser utilizada (opcional).
            model_id: ID do modelo a ser utilizado (opcional).
            output_format: Formato de saída do áudio (opcional).
            language_code: Código do idioma para modelos compatíveis (opcional).
            voice_settings: Configurações da voz (stability, similarity_boost, style, use_speaker_boost) (opcional).

        Returns:
            Caminho para o arquivo de áudio gerado.

        Raises:
            Exception: Se ocorrer um erro na conversão após retentativas.
            ValueError: Se os parâmetros de entrada forem inválidos.
        """
        # Validações iniciais
        if not text:
            raise ValueError("O texto para conversão não pode estar vazio.")
        if not output_path:
             raise ValueError("O caminho de saída do áudio deve ser especificado.")

        # Usa os valores padrão se não especificados
        voice_id = voice_id or self.voice_id
        model_id = model_id or self.model_id
        output_format = output_format or self.output_format
        
        # Endpoint para conversão de texto em áudio
        endpoint = f"{self.base_url}/v1/text-to-speech/{voice_id}"
        
        # Dados da requisição (payload)
        payload = {
            "text": text,
            "model_id": model_id,
            # "output_format": output_format # O formato é geralmente definido no header Accept ou query param
        }

        # Adiciona configurações de voz se fornecidas
        if voice_settings:
            payload["voice_settings"] = voice_settings
            print(f"Usando configurações de voz personalizadas: {voice_settings}")
        else:
            # Configurações padrão básicas
            payload["voice_settings"] = {
                "stability": 0.9,
                "similarity_boost": 0.95
            }
        query_params = {"output_format": output_format}
        
        # Adiciona o código de idioma se aplicável
        # Modelos que suportam language_code explicitamente (verificar documentação atual da ElevenLabs)
        models_supporting_lang_code = ["eleven_turbo_v2_5", "eleven_flash_v2_5"] 
        if language_code and model_id in models_supporting_lang_code:
            if language_code in self.LANGUAGE_CODES:
                payload["language_code"] = self.LANGUAGE_CODES[language_code]
                print(f"Usando idioma: {language_code} ({self.LANGUAGE_CODES[language_code]})")
            else:
                print(f"Aviso: Código de idioma não suportado: {language_code}. Usando detecção automática do modelo.")
        
        # Calcula os caracteres necessários
        char_count = len(text)
        print(f"Requisição TTS: {char_count} caracteres.")

        last_error = None
        # Tentativas em caso de falha
        for attempt in range(self.max_retries):
            try:
                # 1. A chave API é fornecida diretamente via parâmetro.
                # Removido: api_key, remaining_chars = self.api_key_manager.get_available_key(required_chars=char_count)
                # Removido: print(f"Tentativa {attempt + 1}/{self.max_retries}: Usando chave {api_key[:8]}... (Restantes: {remaining_chars}) para {char_count} caracteres.")
                print(f"Tentativa {attempt + 1}/{self.max_retries}: Usando chave fornecida {api_key[:8]}... para {char_count} caracteres.")

                # 2. Faz a requisição POST usando a chave fornecida
                response = requests.post(
                    endpoint, 
                    headers=self._get_headers(api_key),
                    params=query_params, 
                    json=payload,
                    timeout=60 # Timeout para a requisição
                )
                
                # 3. Verifica a resposta
                response.raise_for_status() # Levanta exceção para erros HTTP (4xx ou 5xx)

                # 4. Sucesso: Salva o áudio
                os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
                with open(output_path, "wb") as f:
                    f.write(response.content)
                
                print(f"Áudio gerado com sucesso: {output_path}")
                # IMPORTANTE: Não chamamos mais update_usage aqui!
                return output_path # Retorna sucesso

            except requests.exceptions.HTTPError as e:
                last_error = e
                status_code = e.response.status_code
                error_text = e.response.text
                print(f"Erro HTTP na conversão (tentativa {attempt+1}/{self.max_retries}): {status_code} - {error_text}")

                # Erros que indicam problema com a chave atual - não adianta tentar novamente
                if status_code in [401, 403]:
                    print(f"ERRO FATAL: Chave {api_key[:8]}... inválida/bloqueada (erro {status_code}). Interrompendo tentativas.")
                    break  # Sai do loop de tentativas - chave inválida
                elif status_code == 400:
                    # Verifica se é erro de voice_limit_reached ou similar
                    if "voice_limit_reached" in error_text or "custom voices" in error_text:
                        print(f"ERRO FATAL: Chave {api_key[:8]}... atingiu limite de vozes customizadas. Interrompendo tentativas.")
                        break  # Sai do loop - chave não pode usar esta voz
                    elif "insufficient_quota" in error_text or "quota" in error_text.lower():
                        print(f"ERRO FATAL: Chave {api_key[:8]}... sem quota suficiente. Interrompendo tentativas.")
                        break  # Sai do loop - chave sem quota
                    else:
                        print(f"Erro 400 com chave {api_key[:8]}...: {error_text}")
                        # Outros erros 400 podem ser temporários, continua tentando
                elif status_code == 429:
                    print(f"ERRO FATAL: Rate limit atingido para chave {api_key[:8]}... Interrompendo tentativas.")
                    break  # Sai do loop - chave com rate limit
                # Outros erros HTTP podem ser temporários (500, 502, etc.) - continua tentando

            except requests.exceptions.RequestException as e:
                # Erros de conexão, timeout, etc.
                last_error = e
                print(f"Erro de Rede/Conexão na conversão (tentativa {attempt+1}/{self.max_retries}): {str(e)}")
            
            except ValueError as e:
                # Erro vindo do get_available_key (nenhuma chave utilizável)
                last_error = e
                print(f"Erro do APIKeyManager (tentativa {attempt+1}/{self.max_retries}): {str(e)}")
                # Não adianta tentar de novo se não há chaves, encerra o loop
                break 
            
            except Exception as e:
                # Outros erros inesperados
                last_error = e
                print(f"Erro inesperado na conversão (tentativa {attempt+1}/{self.max_retries}): {str(e)}")

            # Aguarda antes de tentar novamente (se não for o último loop e não for erro fatal do KeyManager)
            if attempt < self.max_retries - 1 and not isinstance(last_error, ValueError):
                print(f"Aguardando {self.retry_delay} segundos antes da próxima tentativa...")
                time.sleep(self.retry_delay)
        
        # Se chegou aqui, todas as tentativas falharam
        final_error_msg = f"Falha na conversão de texto para áudio após {self.max_retries} tentativas. Último erro: {str(last_error)}"
        print(final_error_msg)

        # Preserva informações específicas do erro para tratamento posterior
        if hasattr(last_error, 'response'):
            try:
                error_response = last_error.response.text
                status_code = last_error.response.status_code

                if status_code == 401:
                    raise Exception(f"401_BLOCKED: {final_error_msg}")
                elif status_code == 400 and ("voice_limit_reached" in error_response or "custom voices" in error_response):
                    raise Exception(f"voice_limit_reached: {final_error_msg}")
                elif status_code == 400 and ("insufficient_quota" in error_response or "quota" in error_response.lower()):
                    raise Exception(f"insufficient_quota: {final_error_msg}")
                elif status_code == 429:
                    raise Exception(f"rate_limit: {final_error_msg}")
            except AttributeError:
                pass  # Se falhar ao analisar, usa verificação de string

        # Verificação por string como fallback
        error_str = str(last_error)
        if "401" in error_str or "Unauthorized" in error_str or "detected_unusual_activity" in error_str:
            raise Exception(f"401_BLOCKED: {final_error_msg}")
        elif "voice_limit_reached" in error_str or "custom voices" in error_str:
            raise Exception(f"voice_limit_reached: {final_error_msg}")
        elif "insufficient_quota" in error_str or ("quota" in error_str.lower() and "400" in error_str):
            raise Exception(f"insufficient_quota: {final_error_msg}")
        elif "429" in error_str or "rate limit" in error_str.lower():
            raise Exception(f"rate_limit: {final_error_msg}")
        else:
            raise Exception(final_error_msg)

    def concatenate_audio_files(self, 
                               input_files: List[str], 
                               output_file: str) -> str:
        """
        Concatena múltiplos arquivos de áudio em um único arquivo.
        Requer a biblioteca pydub.
        """
        try:
            from pydub import AudioSegment
        except ImportError:
            raise ImportError("Módulo pydub não encontrado. Instale-o com 'pip install pydub'. Você também pode precisar do ffmpeg.")

        if not input_files:
            raise ValueError("A lista de arquivos de entrada para concatenação está vazia.")

        try:
            combined = None
            for i, file_path in enumerate(input_files):
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"Arquivo de áudio não encontrado para concatenação: {file_path}")
                try:
                    # Tenta detectar o formato, mas assume mp3 como fallback
                    audio = AudioSegment.from_file(file_path)
                except Exception as e_load:
                    print(f"Aviso: Falha ao carregar {file_path} com autodetection ({e_load}). Tentando como MP3.")
                    try:
                         audio = AudioSegment.from_mp3(file_path)
                    except Exception as e_mp3:
                         raise Exception(f"Falha ao carregar arquivo de áudio {file_path}: {e_mp3}")
                
                if i == 0:
                    combined = audio
                else:
                    combined += audio
            
            if combined is None:
                 raise ValueError("Nenhum segmento de áudio foi carregado para concatenação.")

            # Cria o diretório de saída se não existir
            os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
            
            # Exporta o resultado (assumindo MP3 como formato de saída)
            combined.export(output_file, format="mp3")
            
            print(f"Áudios concatenados com sucesso: {output_file}")
            return output_file
            
        except FileNotFoundError as e:
             raise e # Repassa o erro de arquivo não encontrado
        except Exception as e:
            error_msg = f"Erro ao concatenar áudios: {str(e)}"
            print(error_msg)
            raise Exception(error_msg)


# Exemplo de uso (requer um arquivo 'chaves-api-elevenlabs.txt' válido)
if __name__ == "__main__":
    keys_file = "chaves-api-elevenlabs.txt"
    # Cria o arquivo se não existir para teste
    if not os.path.exists(keys_file):
        print(f"Criando arquivo de exemplo {keys_file}. Adicione suas chaves API nele.")
        with open(keys_file, 'w') as f:
            f.write("# Adicione suas chaves API da ElevenLabs aqui, uma por linha\n")
            f.write("sua_chave_api_1\n")
            f.write("sua_chave_api_2\n")

    try:
        # Inicializa o gerenciador de chaves (agora com consulta real)
        key_manager = SequentialAPIKeyManager(keys_file)
        
        # Inicializa o cliente da API
        elevenlabs_api = ElevenLabsAPI(key_manager)
        
        # Exemplo de conversão de texto em áudio
        print("\n--- Exemplo de Conversão TTS ---")
        text = "Este é um teste da nova integração com o gerenciador de chaves aprimorado. Esperamos que funcione corretamente."
        output_path = "exemplo_audio_melhorado.mp3"
        
        generated_file = elevenlabs_api.text_to_speech(text, output_path, language_code="pt-BR")
        print(f"Áudio gerado: {generated_file}")

        # Exemplo de concatenação (requer pydub e ffmpeg)
        # print("\n--- Exemplo de Concatenação ---")
        # try:
        #     # Gerar um segundo áudio para concatenar
        #     text2 = "Este é o segundo segmento de áudio."
        #     output_path2 = "exemplo_audio_melhorado_2.mp3"
        #     generated_file2 = elevenlabs_api.text_to_speech(text2, output_path2, language_code="pt-BR")
        #     
        #     concat_output = "exemplo_concatenado.mp3"
        #     elevenlabs_api.concatenate_audio_files([generated_file, generated_file2], concat_output)
        # except ImportError as e:
        #      print(f"Não foi possível testar a concatenação: {e}")
        # except Exception as e:
        #      print(f"Erro durante o teste de concatenação: {e}")

    except (FileNotFoundError, ValueError) as e:
        print(f"Erro na inicialização ou execução: {e}")
    except Exception as e:
        print(f"Erro inesperado no exemplo: {e}")

