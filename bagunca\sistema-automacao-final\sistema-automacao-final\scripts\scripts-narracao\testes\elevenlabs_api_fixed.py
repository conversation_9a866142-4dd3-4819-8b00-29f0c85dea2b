"""
Módulo de Comunicação com a API do ElevenLabs (Versão Corrigida - sem language_code)

Correções V3.1:
1. Remove language_code que causa erro 400
2. Usa voice_id válido por padrão
3. Mantém funcionalidade de retry automático
4. Compatível com configurações atuais
"""

import os
import time
import requests
import json
from typing import Dict, List, Optional, Tuple, Union, BinaryIO
from pathlib import Path
import logging

# Importa o gerenciador de chaves API aprimorado
from api_key_manager import APIKeyManager

logger = logging.getLogger(__name__)

class ElevenLabsAPI:
    """
    Cliente para comunicação com a API do ElevenLabs com retry automático (versão corrigida).
    """

    # REMOVIDO: LANGUAGE_CODES - não usar mais language_code

    def __init__(self, 
                 api_key_manager: APIKeyManager,
                 base_url: str = "https://api.elevenlabs.io",
                 model_id: str = "eleven_multilingual_v2",
                 voice_id: str = "9BWtsMINqrJLrRacOk9x",  # ATUALIZADO: Aria (voice válida)
                 output_format: str = "mp3_44100_128",
                 max_retries: int = 3,
                 retry_delay: int = 2,
                 max_key_retries: int = 5):
        """
        Inicializa o cliente da API do ElevenLabs (versão corrigida).

        Args:
            api_key_manager: Instância do APIKeyManager aprimorado.
            voice_id: ATUALIZADO para usar Aria por padrão
            max_key_retries: Máximo de chaves diferentes para tentar em caso de bloqueio.
        """
        self.api_key_manager = api_key_manager
        self.base_url = base_url
        self.model_id = model_id
        self.voice_id = voice_id
        self.output_format = output_format
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.max_key_retries = max_key_retries

    def _get_headers(self, api_key: str) -> Dict[str, str]:
        """Retorna os cabeçalhos HTTP para as requisições."""
        return {
            "xi-api-key": api_key,
            "Content-Type": "application/json",
            "Accept": "audio/mpeg"
        }

    def list_voices(self) -> Dict:
        """Lista todas as vozes disponíveis usando uma chave funcional."""
        endpoint = f"{self.base_url}/v1/voices"
        try:
            api_key, _ = self.api_key_manager.get_available_key(required_chars=1)
            headers = self._get_headers(api_key)
            headers.pop("Accept")
            
            response = requests.get(endpoint, headers=headers)
            response.raise_for_status()
            return response.json()
        except ValueError as e:
            error_msg = f"Erro ao obter chave para listar vozes: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except requests.exceptions.RequestException as e:
            error_msg = f"Erro ao listar vozes: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)

    def text_to_speech_with_auto_retry(self, 
                                     text: str, 
                                     output_path: str,
                                     voice_id: Optional[str] = None,
                                     model_id: Optional[str] = None,
                                     output_format: Optional[str] = None) -> str:  # REMOVIDO: language_code
        """
        Converte texto em áudio com retry automático usando chaves diferentes.
        
        VERSÃO CORRIGIDA: Remove language_code que causa erro 400.

        Args:
            text: Texto a ser convertido em áudio.
            output_path: Caminho para salvar o arquivo de áudio.
            voice_id: ID da voz a ser utilizada (opcional).
            model_id: ID do modelo a ser utilizado (opcional).
            output_format: Formato de saída do áudio (opcional).
            # REMOVIDO: language_code

        Returns:
            Caminho para o arquivo de áudio gerado.

        Raises:
            Exception: Se ocorrer um erro na conversão após todas as tentativas.
            ValueError: Se os parâmetros de entrada forem inválidos.
        """
        # Validações iniciais
        if not text:
            raise ValueError("O texto para conversão não pode estar vazio.")
        if not output_path:
            raise ValueError("O caminho de saída do áudio deve ser especificado.")

        # Usa os valores padrão se não especificados
        voice_id = voice_id or self.voice_id
        model_id = model_id or self.model_id
        output_format = output_format or self.output_format
        
        char_count = len(text)
        logger.info(f"Iniciando TTS (v3.1): {char_count} caracteres, tentando até {self.max_key_retries} chaves diferentes")
        
        # Loop principal: tenta diferentes chaves
        keys_tried = set()
        last_error = None
        
        for key_attempt in range(self.max_key_retries):
            try:
                # Obtém uma nova chave (APIKeyManager filtra automaticamente as bloqueadas)
                api_key, remaining_chars = self.api_key_manager.get_available_key(required_chars=char_count)
                
                # Evita tentar a mesma chave novamente
                if api_key in keys_tried:
                    logger.warning(f"Chave {api_key[:8]}... já foi tentada, solicitando próxima...")
                    # Marca temporariamente como bloqueada para forçar nova seleção
                    self.api_key_manager.mark_key_as_blocked(api_key, "Temporary block for retry")
                    continue
                    
                keys_tried.add(api_key)
                logger.info(f"Tentativa de chave {key_attempt + 1}/{self.max_key_retries}: {api_key[:8]}... ({remaining_chars} chars disponíveis)")
                
                # Tenta usar esta chave com retries para erros de rede
                try:
                    result_path = self._text_to_speech_single_key(
                        text=text,
                        output_path=output_path,
                        api_key=api_key,
                        voice_id=voice_id,
                        model_id=model_id,
                        output_format=output_format
                        # REMOVIDO: language_code=language_code
                    )
                    
                    # Sucesso! Deduz o uso e retorna
                    self.api_key_manager.deduct_usage(api_key=api_key, characters_used=char_count)
                    logger.info(f"✅ TTS concluído com sucesso usando chave {api_key[:8]}...")
                    return result_path
                    
                except requests.exceptions.HTTPError as e:
                    if e.response.status_code == 401:
                        # Esta chave foi bloqueada/é inválida
                        error_detail = ""
                        try:
                            error_data = e.response.json()
                            error_detail = error_data.get("detail", {}).get("message", "")
                        except:
                            pass
                            
                        logger.warning(f"🚫 Chave {api_key[:8]}... bloqueada/inválida: {error_detail}")
                        self.api_key_manager.mark_key_as_blocked(api_key, f"401 Error: {error_detail}")
                        last_error = e
                        continue  # Tenta próxima chave
                        
                    else:
                        # Outros erros HTTP (429, 500, etc.) - pode ser temporário
                        logger.error(f"Erro HTTP {e.response.status_code} com chave {api_key[:8]}...: {e.response.text}")
                        last_error = e
                        # Para 429 (rate limit), espera mais tempo antes de tentar nova chave
                        if e.response.status_code == 429:
                            logger.info("Rate limit detectado, aguardando antes de tentar nova chave...")
                            time.sleep(self.retry_delay * 2)
                        continue
                        
                except Exception as e:
                    # Outros erros (rede, etc.)
                    logger.error(f"Erro inesperado com chave {api_key[:8]}...: {str(e)}")
                    last_error = e
                    continue
                    
            except ValueError as e:
                # Erro do APIKeyManager (sem chaves disponíveis)
                logger.error(f"APIKeyManager: {str(e)}")
                last_error = e
                break  # Não adianta continuar se não há chaves
        
        # Se chegou aqui, todas as tentativas falharam
        working_keys = len(self.api_key_manager.working_keys)
        blocked_keys = len(self.api_key_manager.blocked_keys)
        
        final_error_msg = (
            f"Falha na conversão TTS após tentar {len(keys_tried)} chaves diferentes. "
            f"Status: {working_keys} funcionais, {blocked_keys} bloqueadas. "
            f"Último erro: {str(last_error)}"
        )
        logger.error(final_error_msg)
        raise Exception(final_error_msg)

    def _text_to_speech_single_key(self, 
                                 text: str, 
                                 output_path: str,
                                 api_key: str,
                                 voice_id: str,
                                 model_id: str,
                                 output_format: str) -> str:  # REMOVIDO: language_code
        """
        Faz a conversão TTS usando uma chave específica com retries para erros de rede.
        
        VERSÃO CORRIGIDA: Remove language_code problemático.
        """
        endpoint = f"{self.base_url}/v1/text-to-speech/{voice_id}"
        
        # Preparar payload - SEM LANGUAGE_CODE
        payload = {
            "text": text,
            "model_id": model_id,
        }
        query_params = {"output_format": output_format}
        
        # REMOVIDO: Todo o código de language_code
        logger.debug(f"Payload: {payload} | Params: {query_params}")
        
        # Retries para esta chave específica (apenas para erros de rede/timeout)
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"Tentativa {attempt + 1}/{self.max_retries} com chave {api_key[:8]}...")
                
                response = requests.post(
                    endpoint,
                    headers=self._get_headers(api_key),
                    params=query_params,
                    json=payload,
                    timeout=60
                )
                
                response.raise_for_status()  # Levanta HTTPError para 4xx/5xx
                
                # Sucesso - salva o arquivo
                os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
                with open(output_path, "wb") as f:
                    f.write(response.content)
                
                logger.info(f"Áudio salvo: {output_path}")
                return output_path
                
            except requests.exceptions.HTTPError as e:
                # HTTPError são repassados imediatamente (não há retry para 401, 429, etc.)
                raise e
                
            except requests.exceptions.RequestException as e:
                # Erros de rede/timeout - pode fazer retry
                if attempt < self.max_retries - 1:
                    logger.warning(f"Erro de rede (tentativa {attempt + 1}): {str(e)}, tentando novamente em {self.retry_delay}s...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"Erro de rede após {self.max_retries} tentativas: {str(e)}")
                    raise e
        
        # Nunca deveria chegar aqui devido ao loop, mas...
        raise Exception("Erro inesperado no loop de retries")

    def text_to_speech(self, 
                       text: str, 
                       output_path: str,
                       api_key: str,
                       voice_id: Optional[str] = None,
                       model_id: Optional[str] = None,
                       output_format: Optional[str] = None) -> str:  # REMOVIDO: language_code
        """
        Método de compatibilidade com a versão anterior.
        
        RECOMENDAÇÃO: Use text_to_speech_with_auto_retry() para melhor gerenciamento
        automático de chaves.
        """
        logger.warning("Usando text_to_speech() legacy. Recomendado: text_to_speech_with_auto_retry()")
        
        return self._text_to_speech_single_key(
            text=text,
            output_path=output_path,
            api_key=api_key,
            voice_id=voice_id or self.voice_id,
            model_id=model_id or self.model_id,
            output_format=output_format or self.output_format
            # REMOVIDO: language_code=language_code
        )

    def concatenate_audio_files(self, 
                               input_files: List[str], 
                               output_file: str) -> str:
        """
        Concatena múltiplos arquivos de áudio em um único arquivo.
        (Função mantida igual à versão anterior)
        """
        try:
            from pydub import AudioSegment
        except ImportError:
            raise ImportError("Módulo pydub não encontrado. Instale-o com 'pip install pydub'.")

        if not input_files:
            raise ValueError("A lista de arquivos de entrada para concatenação está vazia.")

        try:
            combined = None
            for i, file_path in enumerate(input_files):
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"Arquivo de áudio não encontrado: {file_path}")
                
                try:
                    audio = AudioSegment.from_file(file_path)
                except Exception as e_load:
                    logger.warning(f"Falha ao carregar {file_path} com autodetection ({e_load}). Tentando como MP3.")
                    try:
                        audio = AudioSegment.from_mp3(file_path)
                    except Exception as e_mp3:
                        raise Exception(f"Falha ao carregar arquivo de áudio {file_path}: {e_mp3}")
                
                if i == 0:
                    combined = audio
                else:
                    combined += audio
            
            if combined is None:
                raise ValueError("Nenhum segmento de áudio foi carregado para concatenação.")

            os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
            combined.export(output_file, format="mp3")
            
            logger.info(f"Áudios concatenados com sucesso: {output_file}")
            return output_file
            
        except Exception as e:
            error_msg = f"Erro ao concatenar áudios: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)

# Exemplo de uso CORRIGIDO
if __name__ == "__main__":
    keys_file = "chaves-api-elevenlabs.txt"
    
    try:
        # Inicializa com detecção automática de bloqueios
        key_manager = APIKeyManager(keys_file)
        
        # Verifica se há chaves funcionais
        if not key_manager.working_keys:
            print("❌ Nenhuma chave funcional disponível!")
            exit(1)
        
        print(f"✅ {len(key_manager.working_keys)} chaves funcionais encontradas")
        
        # Inicializa API client CORRIGIDO
        elevenlabs_api = ElevenLabsAPI(key_manager)
        
        # Teste com retry automático - SEM LANGUAGE_CODE
        text = "Este é um teste da versão corrigida sem language code."
        output_path = "teste_versao_corrigida.mp3"
        
        print(f"\n🎵 Testando TTS corrigido (sem language_code)...")
        try:
            generated_file = elevenlabs_api.text_to_speech_with_auto_retry(
                text=text, 
                output_path=output_path
                # REMOVIDO: language_code="pt-BR"
            )
            print(f"✅ Áudio gerado: {generated_file}")
            
            # Verifica tamanho do arquivo
            if Path(generated_file).exists():
                size_kb = Path(generated_file).stat().st_size / 1024
                print(f"📊 Tamanho: {size_kb:.1f} KB")
                
                if size_kb > 1:
                    print(f"🎉 SUCESSO TOTAL - Sistema funcionando!")
                else:
                    print(f"⚠️ Arquivo muito pequeno")
            
        except Exception as e:
            print(f"❌ Falha no TTS: {e}")

    except Exception as e:
        print(f"❌ Erro na inicialização: {e}")
