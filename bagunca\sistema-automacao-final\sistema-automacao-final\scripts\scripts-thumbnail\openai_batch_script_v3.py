import openai
import os
import time
import re

# --- Configuração Importante ---
# Determina o diretório do script atual
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# Determina a raiz do projeto (dois níveis acima do diretório do script)
PROJECT_ROOT = os.path.abspath(os.path.join(SCRIPT_DIR, "..", ".."))

# Caminho para a chave da API (agora na pasta config na raiz)
API_KEY_FILE = os.path.join(PROJECT_ROOT, "config", "chaves-api-openai.txt") 
ASSISTANT_ID = "asst_kI9iFdgYYCOYgI6KIiIJCZtn" # Manter este ID ou mover para config?

# Caminhos para os arquivos de entrada (agora na raiz do projeto)
TITLES_FILE = os.path.join(PROJECT_ROOT, "generated_titles_agent.txt")
PREMISES_FILE = os.path.join(PROJECT_ROOT, "generated_premises.md")

# Arquivos de saída e controle de progresso (agora na raiz e em logs/)
OUTPUT_FILE = os.path.join(PROJECT_ROOT, "texto-thumbnails.txt") 
PROGRESS_FILE = os.path.join(PROJECT_ROOT, "logs", "thumbnail_generation_progress.txt")

def ler_api_key(filepath):
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read().strip()
    except FileNotFoundError:
        print(f"Erro Crítico: Arquivo da chave API não encontrado em {filepath}")
        print("Por favor, crie o arquivo e insira sua chave da API OpenAI.")
        return None
    except Exception as e:
        print(f"Erro ao ler o arquivo da chave API: {e}")
        return None

API_KEY = ler_api_key(API_KEY_FILE)
client = None
if API_KEY:
    openai.api_key = API_KEY
    client = openai.OpenAI(api_key=API_KEY)
else:
    print(f"Chave da API OpenAI não lida ou não encontrada em {API_KEY_FILE}. O script não poderá gerar textos via OpenAI.")

def ler_titulos(filepath):
    """Lê títulos do arquivo gerado pelo agent, lidando com variações de espaço."""
    titulos = []
    print(f"Tentando ler títulos de: {filepath}")
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            # Regex aprimorado para encontrar títulos no formato:
            # ## Premissa X
            #    Título da premissa X   
            # Captura o título (grupo 1) que está na linha seguinte ao cabeçalho da premissa,
            # permitindo espaços antes/depois do título na linha.
            matches = re.findall(r"^##\\s+Premissa\\s+\\d+\\s*\\n\\s*(.*?)\\s*?$", content, re.MULTILINE)
            
            print(f"Número de correspondências de títulos encontradas pela regex: {len(matches)}")

            for title_match in matches:
                cleaned_title = title_match.strip()
                if cleaned_title: # Garante que não adicionamos strings vazias
                    titulos.append(cleaned_title) 
                else:
                    print("Aviso: Correspondência de título encontrada, mas o título estava vazio após a limpeza.")

            if not titulos:
                # Adiciona uma verificação extra: Se a regex principal falhar, tenta ler linha por linha
                print(f"Regex principal falhou. Tentando leitura linha por linha como fallback...")
                lines = content.splitlines()
                for i, line in enumerate(lines):
                    # Debug print adicionado aqui
                    print(f"  Fallback Check: Linha {i+1}: '{line.strip()}'") 
                    if re.match(r"^##\s+Premissa\s+\d+\s*$", line.strip()):
                        print(f"    Fallback Match encontrado para header: '{line.strip()}'") # Debug print
                        if i + 1 < len(lines):
                            next_line_title = lines[i+1].strip()
                            print(f"    Fallback Próxima Linha ({i+2}): '{next_line_title}'") # Debug print
                            if next_line_title and not next_line_title.startswith("##") and not next_line_title.startswith("#"):
                                titulos.append(next_line_title)
                                print(f"      Título encontrado via fallback: '{next_line_title[:50]}...' ") # Debug print
                            else:
                                print(f"      Fallback Ignorado: Próxima linha vazia ou começa com ## ou #.") # Debug print
                        else:
                             print(f"    Fallback Ignorado: Nenhuma próxima linha disponível.") # Debug print
                    # else: # Debug opcional para linhas não correspondentes
                    #    print(f"    Fallback Sem correspondência para header.")
                if not titulos:                    print(f"Aviso Crítico: Nenhum título válido encontrado (nem via fallback) em {filepath}. Verifique o arquivo.")
            else:
                print(f"Total de títulos extraídos com sucesso via regex principal: {len(titulos)}")

    except FileNotFoundError:
        print(f"Erro Crítico: Arquivo de títulos não encontrado em {filepath}")
        return []
    except Exception as e:
        print(f"Erro inesperado ao ler ou processar o arquivo de títulos {filepath}: {e}")
        return []
    # Garante que não haja duplicatas se ambos os métodos encontrarem algo (improvável, mas seguro)
    return list(dict.fromkeys(titulos))
def ler_premissas(filepath):
    """Lê premissas do arquivo markdown, tentando múltiplos formatos."""
    premissas = []
    print(f"Tentando ler premissas de: {filepath}")
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()

            # Formato Principal (agent.py adaptado): num. **Premissa X:** Corpo (Estrutura:...) opcional
            # Captura o corpo (grupo 1)
            matches_main = re.findall(
                r"^\d+\.\s+\*\*Premissa\s+\d+:\*\*\s+(.*?)(?:\s+\(Estrutura:.*?\))?$",
                content,
                re.MULTILINE | re.DOTALL
            )
            if matches_main:
                print(f"Formato Principal encontrado ({len(matches_main)} correspondências).")
                for body in matches_main:
                    cleaned_body = body.strip()
                    if cleaned_body:
                        premissas.append(cleaned_body)

            # Fallback 1: Formato original do openai_batch_script (corpo entre **Premissa X:** e (Estrutura:...))
            if not premissas:
                matches_alt1 = re.findall(
                    r"^\d+\.\s*\*\*Premissa\s+\d+:\*\*\s*(.*?)\s*\(Estrutura:.*?\)\s*$",
                    content,
                    re.MULTILINE | re.DOTALL
                )
                if matches_alt1:
                    print(f"Formato Alternativo 1 (com Estrutura obrigatório) encontrado ({len(matches_alt1)} correspondências).")
                    for body in matches_alt1:
                        cleaned_body = body.strip()
                        if cleaned_body:
                            premissas.append(cleaned_body)

            # Fallback 2: Formato antigo do agent.py (Markdown Header: # Premissa X: Corpo)
            if not premissas:
                 matches_alt2 = re.findall(
                    r"^#+\s+Premissa\s+\d+\s*:?\s*(.*?)(?=\n\n#+|$)", # Simplificado, : opcional
                    content,
                    re.MULTILINE | re.DOTALL,
                )
                 if matches_alt2:
                    print(f"Formato Alternativo 2 (Markdown Header) encontrado ({len(matches_alt2)} correspondências).")
                    for body in matches_alt2:
                        cleaned_body = body.strip()
                        if cleaned_body:
                            premissas.append(cleaned_body)

            # Fallback 3: Formato antigo do agent.py (Numbered List: 1. **Título**: Corpo) - Adaptado
            # Tentativa de capturar corpo após qualquer marcador inicial como "1. **...**:"
            if not premissas:
                 matches_alt3 = re.findall(
                    r"^\d+\.\s+\*\*.*?\*\*:\s+(.*?)(?=\n\n\d+\.|$)",
                    content,
                    re.MULTILINE | re.DOTALL,
                 )
                 if matches_alt3:
                    print(f"Formato Alternativo 3 (Numbered List) encontrado ({len(matches_alt3)} correspondências).")
                    for body in matches_alt3:
                        cleaned_body = body.strip()
                        if cleaned_body:
                            premissas.append(cleaned_body)


            if not premissas:
                print(f"Aviso Crítico: Nenhuma premissa encontrada em nenhum formato conhecido em {filepath}.")
            else:
                 print(f"Total de premissas extraídas com sucesso: {len(premissas)}")

    except FileNotFoundError:
        print(f"Erro Crítico: Arquivo de premissas não encontrado em {filepath}")
        return []
    except Exception as e:
        print(f"Erro inesperado ao ler ou processar o arquivo de premissas {filepath}: {e}")
        return []
    return premissas

def get_last_processed_index(progress_file_path):
    progress_dir = os.path.dirname(progress_file_path)
    if not os.path.exists(progress_dir):
        try:
            os.makedirs(progress_dir)
            print(f"Diretório de logs criado: {progress_dir}")
        except OSError as e:
            print(f"Erro ao criar diretório de logs {progress_dir}: {e}")
            return -1 # Retorna -1 se não conseguir criar o diretório
    try:
        with open(progress_file_path, 'r') as f:
            return int(f.read().strip())
    except FileNotFoundError:
        return -1
    except ValueError:
        print(f"Aviso: Arquivo de progresso ({progress_file_path}) corrompido. Reiniciando do zero.")
        return -1

def save_progress(index, progress_file_path):
    progress_dir = os.path.dirname(progress_file_path)
    if not os.path.exists(progress_dir):
        try:
            os.makedirs(progress_dir)
        except OSError as e:
            print(f"Erro ao criar diretório para salvar progresso {progress_dir}: {e}")
            return # Não salva se não conseguir criar diretório
    with open(progress_file_path, 'w') as f:
        f.write(str(index))

def gerar_texto_thumbnail_openai(titulo, tema):
    global client
    if not client:
        print("Cliente OpenAI não inicializado. Verifique a API Key.")
        return None
    try:
        thread = client.beta.threads.create()
        user_message_content = f"Gere um texto de thumbnail com o seguinte Título e Tema:\nTítulo: {titulo}\nTema: {tema}"
        
        client.beta.threads.messages.create(
            thread_id=thread.id,
            role="user",
            content=user_message_content
        )
        
        run = client.beta.threads.runs.create(
            thread_id=thread.id,
            assistant_id=ASSISTANT_ID,
        )
        
        max_retries = 7
        retry_delay = 10
        attempt = 0
        
        while run.status in ["queued", "in_progress"] and attempt < max_retries:
            time.sleep(retry_delay)
            run = client.beta.threads.runs.retrieve(thread_id=thread.id, run_id=run.id)
            attempt +=1

        if run.status == "completed":
            messages = client.beta.threads.messages.list(thread_id=thread.id)
            for msg in messages.data:
                if msg.role == "assistant":
                    if msg.content and len(msg.content) > 0 and hasattr(msg.content[0], 'text'):
                        return msg.content[0].text.value
            print(f"Nenhuma resposta do assistente para: {titulo}")
            return None
        elif run.status == "failed":
            print(f"A Run FALHOU para '{titulo}'. Status: {run.status}. Erro: {run.last_error.message if run.last_error else 'N/A'}")
            return None
        else:
            print(f"A Run não foi concluída com sucesso para '{titulo}'. Status: {run.status}")
            return None

    except Exception as e:
        print(f"Ocorreu um erro ao gerar para '{titulo}': {e}")
        return None

def executar_geracao_textos():
    global client
    if not client:
        print(f"Cliente OpenAI não inicializado. Não é possível gerar textos. Verifique a API Key em {API_KEY_FILE}.")
        return False

    # Verifica se diretório de logs existe, cria se necessário
    logs_dir = os.path.dirname(PROGRESS_FILE)
    if not os.path.exists(logs_dir):
        try:
            os.makedirs(logs_dir)
            print(f"Diretório criado: {logs_dir}")
        except OSError as e:
            print(f"Erro ao criar diretório {logs_dir}: {e}")
            # Não retorna False aqui, pois a falta do log não impede a execução principal
            # Mas o arquivo de progresso não será salvo se falhar depois.

    titulos = ler_titulos(TITLES_FILE)
    premissas = ler_premissas(PREMISES_FILE)

    if not titulos:
        print(f"Nenhum título encontrado em {TITLES_FILE}. Encerrando geração de textos.")
        return False
    if not premissas:
        print(f"Nenhuma premissa encontrada em {PREMISES_FILE}. Encerrando geração de textos.")
        return False

    print(f"Títulos encontrados: {len(titulos)}")
    print(f"Premissas encontradas: {len(premissas)}")

    if len(titulos) != len(premissas):
        print(f"Aviso: O número de títulos ({len(titulos)}) não corresponde ao número de premissas ({len(premissas)}). Usando o menor número.")
        min_len = min(len(titulos), len(premissas))
        titulos = titulos[:min_len]
        premissas = premissas[:min_len]

    last_processed_index = get_last_processed_index(PROGRESS_FILE)
    start_index = last_processed_index + 1

    print(f"Iniciando/Continuando geração de textos do índice: {start_index}")

    BATCH_SIZE = 5
    output_mode = 'a' if start_index > 0 else 'w'

    # Garante que o arquivo de saída exista antes de abrir no modo append
    if output_mode == 'a' and not os.path.exists(OUTPUT_FILE):
        print(f"Arquivo de saída {OUTPUT_FILE} não encontrado para append. Criando novo arquivo.")
        output_mode = 'w'

    if start_index == 0 and output_mode == 'w':
        print(f"Limpando arquivo de saída existente: {OUTPUT_FILE} pois estamos começando do zero.")
        # O modo 'w' já faz isso. Apenas remove o progresso.
        if os.path.exists(PROGRESS_FILE):
            try:
                os.remove(PROGRESS_FILE)
                print(f"Arquivo de progresso {PROGRESS_FILE} removido.")
            except OSError as e:
                print(f"Erro ao remover arquivo de progresso {PROGRESS_FILE}: {e}")
    
    try:
        with open(OUTPUT_FILE, output_mode, encoding="utf-8") as f_out:
            for i in range(start_index, len(titulos)):
                titulo = titulos[i]
                premissa = premissas[i]
                
                print(f"Processando item {i+1}/{len(titulos)} (Índice {i}): Título: {titulo[:50]}...")
                texto_gerado = gerar_texto_thumbnail_openai(titulo, premissa)
                
                if texto_gerado:
                    texto_limpo = texto_gerado.strip()
                    f_out.write(f"{texto_limpo}\n---\n\n")
                    print(f"Texto gerado e salvo para item {i+1}.")
                else:
                    print(f"Falha ao gerar texto para item {i+1}. Título: {titulo}")
                    # Escreve uma entrada de falha no arquivo de saída
                    f_out.write(f"[PRINCIPAL]\nFALHA AO GERAR PARA O TÍTULO (Índice {i}): {titulo}\n[FINAL]\nVERIFICAR LOGS...\n---\n\n")
                
                save_progress(i, PROGRESS_FILE)

                # Pausa entre requisições para evitar limites de taxa
                if (i + 1 - start_index) % BATCH_SIZE == 0 and i < len(titulos) -1:
                    print(f"Pausa de 20 segundos após processar o lote (item {i+1})...")
                    time.sleep(20)
                elif i < len(titulos) -1:
                    time.sleep(5) # Pausa menor entre itens individuais
    except IOError as e:
        print(f"Erro de I/O ao escrever no arquivo de saída {OUTPUT_FILE}: {e}")
        return False
    except Exception as e:
        print(f"Erro inesperado durante a escrita no arquivo de saída: {e}")
        return False

    print(f"\nProcessamento de geração de textos concluído. Textos compilados salvos em {OUTPUT_FILE}")
    # Verifica se todos os itens foram processados para remover o arquivo de progresso
    if get_last_processed_index(PROGRESS_FILE) == len(titulos) - 1:
        print("Todos os itens processados para geração de texto. Removendo arquivo de progresso.")
        if os.path.exists(PROGRESS_FILE):
            try:
                os.remove(PROGRESS_FILE)
                print(f"Arquivo de progresso {PROGRESS_FILE} removido.")
            except OSError as e:
                print(f"Erro ao remover arquivo de progresso {PROGRESS_FILE}: {e}")
    return True
