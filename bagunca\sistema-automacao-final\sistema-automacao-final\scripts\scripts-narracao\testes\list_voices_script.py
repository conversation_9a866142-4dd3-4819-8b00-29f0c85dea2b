"""
Script para listar vozes disponíveis da ElevenLabs
"""

import sys
from pathlib import Path
import json

# Adiciona o caminho dos módulos
sys.path.append(str(Path(__file__).parent))

from api_key_manager import APIKeyManager
from proxy_manager_webshare import WebshareProxyManager
from elevenlabs_api_atualizado import ElevenLabsAPI

def main():
    keys_file = "chaves-api-elevenlabs.txt"
    webshare_token = "mt643rsq556yqggigaa3emmmjmdycc2h6868bi7b"
    
    print("🔍 LISTANDO VOZES DISPONÍVEIS DA ELEVENLABS")
    print("="*60)
    
    try:
        # Inicializa managers
        print("1. Inicializando gerenciador de chaves...")
        key_manager = APIKeyManager(keys_file)
        
        print("2. Inicializando gerenciador de proxies...")
        proxy_manager = WebshareProxyManager(
            api_token=webshare_token,
            use_rotating_endpoint=True,
            enable_fallback=True
        )
        
        print("3. Inicializando cliente ElevenLabs...")
        elevenlabs_api = ElevenLabsAPI(
            api_key_manager=key_manager,
            proxy_manager=proxy_manager
        )
        
        print("4. Obtendo lista de vozes...")
        voices = elevenlabs_api.list_voices()
        
        print(f"\n✅ VOZES DISPONÍVEIS ({len(voices.get('voices', []))} encontradas):")
        print("-" * 60)
        
        for i, voice in enumerate(voices.get('voices', []), 1):
            voice_id = voice.get('voice_id', 'N/A')
            name = voice.get('name', 'N/A')
            category = voice.get('category', 'N/A')
            language = voice.get('labels', {}).get('language', 'N/A') if voice.get('labels') else 'N/A'
            
            print(f"{i:2d}. {name}")
            print(f"    ID: {voice_id}")
            print(f"    Categoria: {category}")
            print(f"    Idioma: {language}")
            print()
        
        # Sugere algumas vozes boas
        print("🎯 SUGESTÕES DE VOZES PARA PORTUGUÊS:")
        print("-" * 40)
        
        portuguese_voices = []
        for voice in voices.get('voices', []):
            labels = voice.get('labels', {})
            if labels:
                language = labels.get('language', '').lower()
                if 'portuguese' in language or 'portuguese' in voice.get('name', '').lower():
                    portuguese_voices.append(voice)
        
        if portuguese_voices:
            for voice in portuguese_voices[:5]:  # Top 5
                print(f"• {voice['name']} (ID: {voice['voice_id']})")
        else:
            # Se não encontrar portuguesas, sugere algumas universais
            print("Nenhuma voz específica para português encontrada.")
            print("Sugestões de vozes universais:")
            for voice in voices.get('voices', [])[:5]:
                print(f"• {voice['name']} (ID: {voice['voice_id']})")
        
        # Mostra o primeiro voice_id válido para teste
        if voices.get('voices'):
            first_voice = voices['voices'][0]
            print(f"\n🧪 PARA TESTE RÁPIDO, USE:")
            print(f"Voice ID: {first_voice['voice_id']}")
            print(f"Nome: {first_voice['name']}")
            
            # Salva a lista completa em arquivo
            with open('vozes_disponiveis.json', 'w', encoding='utf-8') as f:
                json.dump(voices, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Lista completa salva em: vozes_disponiveis.json")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
