#!/usr/bin/env python3
"""
Script para concatenar os blocos de áudio que já foram gerados com sucesso
"""

import os
import sys
from pathlib import Path
from pydub import AudioSegment

def concatenate_successful_blocks():
    """
    Concatena os blocos 1-40 que foram gerados com sucesso
    """
    # Caminho base (ajuste conforme necessário)
    base_dir = Path("narracao")
    temp_dir = None
    
    # Procura o diretório temporário mais recente
    for temp_folder in base_dir.glob("temp_roteiro*"):
        if temp_folder.is_dir():
            temp_dir = temp_folder
            break
    
    if not temp_dir:
        print("❌ Diretório temporário não encontrado!")
        print("Procure por pastas como: temp_roteiro Portuges_*")
        return False
    
    print(f"📁 Encontrado diretório: {temp_dir}")
    
    # Lista todos os blocos disponíveis
    block_files = []
    for i in range(1, 50):  # Verifica até bloco 50
        block_file = temp_dir / f"bloco_{i}.mp3"
        if block_file.exists():
            block_files.append(block_file)
            print(f"✅ Bloco {i} encontrado")
        else:
            print(f"❌ Bloco {i} não encontrado - parando aqui")
            break
    
    if not block_files:
        print("❌ Nenhum bloco encontrado!")
        return False
    
    print(f"\n🔗 Concatenando {len(block_files)} blocos...")
    
    try:
        # Carrega o primeiro bloco
        combined = AudioSegment.from_mp3(str(block_files[0]))
        print(f"📀 Carregado bloco 1")
        
        # Adiciona os demais blocos
        for i, block_file in enumerate(block_files[1:], 2):
            audio_block = AudioSegment.from_mp3(str(block_file))
            combined += audio_block
            print(f"📀 Adicionado bloco {i}")
        
        # Salva o resultado
        output_file = base_dir / "roteiro_Portuges_PARCIAL.mp3"
        combined.export(str(output_file), format="mp3")
        
        # Estatísticas
        duration_seconds = len(combined) / 1000
        duration_minutes = duration_seconds / 60
        
        print(f"\n🎉 SUCESSO!")
        print(f"📄 Arquivo gerado: {output_file}")
        print(f"⏱️  Duração: {duration_minutes:.1f} minutos ({duration_seconds:.1f} segundos)")
        print(f"🔢 Blocos concatenados: {len(block_files)}")
        
        return str(output_file)
        
    except Exception as e:
        print(f"❌ Erro ao concatenar: {e}")
        return False

def estimate_remaining_content():
    """
    Estima quanto conteúdo ainda resta para processar
    """
    print(f"\n📊 ANÁLISE DO PROGRESSO:")
    print(f"✅ Blocos processados: 40")
    print(f"🚫 Blocos falharam: 1+ (a partir do bloco 41)")
    print(f"📈 Progresso estimado: ~85-90% do arquivo")
    print(f"⏰ Áudio gerado: ~40-50 minutos (estimativa)")
    
    print(f"\n💡 PRÓXIMOS PASSOS RECOMENDADOS:")
    print(f"1. Concatenar os blocos existentes (script acima)")
    print(f"2. Aguardar 2-4 horas para as chaves 'esfriarem'")
    print(f"3. Processar apenas o texto restante com uma chave")
    print(f"4. OU usar uma das 23 chaves restantes imediatamente")

if __name__ == "__main__":
    print("🔗 CONCATENADOR DE BLOCOS EXISTENTES")
    print("=" * 50)
    
    # Verifica se pydub está instalado
    try:
        from pydub import AudioSegment
    except ImportError:
        print("❌ ERRO: pydub não está instalado!")
        print("Instale com: pip install pydub")
        print("Você também pode precisar do ffmpeg")
        sys.exit(1)
    
    result = concatenate_successful_blocks()
    
    if result:
        print(f"\n✅ Você já tem a maior parte do seu áudio!")
        estimate_remaining_content()
    else:
        print(f"\n❌ Não foi possível concatenar os blocos")
        print(f"Verifique se o diretório 'narracao' existe")
        print(f"e se contém uma pasta 'temp_roteiro...' com arquivos bloco_*.mp3")
