"""
Módulo para filtragem refinada de vídeos do YouTube com foco no nicho de histórias de vingança.

Este módulo implementa filtros avançados para selecionar vídeos relevantes para o nicho
de histórias de vingança, com base em análise de títulos, descrições e outros metadados.
"""

import os
import re
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("youtube_revenge_filter")

class YouTubeRevengeFilter:
    """
    Filtro refinado para vídeos do YouTube com foco no nicho de histórias de vingança.
    """
    
    def __init__(self, reference_data_path: str, metrics_calculator=None):
        """
        Inicializa o filtro de nicho de vingança.
        
        Args:
            reference_data_path: Caminho para o arquivo CSV com dados de referência
            metrics_calculator: Calculadora de métricas (opcional)
        """
        self.reference_data_path = reference_data_path
        self.metrics_calculator = metrics_calculator
        
        # Carregar dados de referência
        self.reference_data = self._load_reference_data()
        
        # Extrair padrões de títulos e palavras-chave
        self.title_patterns, self.keywords = self._extract_patterns_and_keywords()
        
        # Palavras-chave específicas para o nicho de histórias de vingança
        self.revenge_keywords = [
            "revenge", "mother-in-law", "mil", "wife", "husband", "family", "karma",
            "betrayal", "cheating", "divorce", "marriage", "wedding", "relationship",
            "sister", "brother", "father", "mother", "dad", "mom", "parent", "child",
            "inheritance", "money", "property", "house", "apartment", "job", "work",
            "boss", "colleague", "friend", "neighbor", "ex", "girlfriend", "boyfriend"
        ]
        
        # Padrões narrativos comuns em histórias de vingança
        self.narrative_patterns = [
            r"my (mother|father|sister|brother|wife|husband|mil|fil|son|daughter|boss|colleague)",
            r"(she|he|they) had no idea",
            r"until (she|he|they) (saw|found|realized|discovered)",
            r"what (was|is) coming",
            r"told me to leave",
            r"changed my",
            r"emptied",
            r"banned me",
            r"left me",
            r"revenge",
            r"karma",
            r"payback",
            r"regret",
            r"shocked",
            r"surprised"
        ]
        
        logger.info("Filtro de nicho de vingança inicializado")
    
    def _load_reference_data(self) -> List[Dict]:
        """
        Carrega os dados de referência do CSV.
        
        Returns:
            Lista de dicionários com dados de referência
        """
        import pandas as pd
        
        try:
            df = pd.read_csv(self.reference_data_path)
            logger.info(f"Dados de referência carregados: {len(df)} vídeos")
            
            # Converter para lista de dicionários
            reference_data = df.to_dict('records')
            
            return reference_data
        except Exception as e:
            logger.error(f"Erro ao carregar dados de referência: {str(e)}")
            return []
    
    def _extract_patterns_and_keywords(self) -> Tuple[List[str], List[str]]:
        """
        Extrai padrões de títulos e palavras-chave dos dados de referência.
        
        Returns:
            Tupla com lista de padrões de títulos e lista de palavras-chave
        """
        # Padrões específicos para histórias de vingança
        title_patterns = [
            "mother-in-law", "mil", "revenge", "karma", "payback",
            "had no idea", "what was coming", "changed my", "left me",
            "told me to leave", "banned me", "emptied", "found out",
            "discovered", "realized", "shocked", "surprised"
        ]
        
        # Palavras-chave específicas para histórias de vingança
        keywords = [
            "revenge", "story", "stories", "karma", "family", "drama",
            "relationship", "marriage", "divorce", "betrayal", "cheating",
            "mother-in-law", "father-in-law", "sister", "brother", "parent",
            "child", "inheritance", "money", "property", "house", "job", "work",
            "boss", "colleague", "friend", "neighbor", "ex", "wife", "husband"
        ]
        
        # Extrair padrões adicionais dos títulos de referência
        all_titles = [video.get('title', '') for video in self.reference_data if 'title' in video]
        
        # Extrair frases comuns dos títulos
        for title in all_titles:
            # Extrair frases entre aspas
            quotes = re.findall(r'"([^"]*)"', title)
            for quote in quotes:
                if len(quote.split()) >= 3 and quote not in title_patterns:
                    title_patterns.append(quote)
            
            # Extrair frases após hífen ou travessão
            if '-' in title or '—' in title:
                parts = re.split(r'[-—]', title)
                if len(parts) > 1 and len(parts[1].strip().split()) >= 3:
                    phrase = parts[1].strip()
                    if phrase not in title_patterns:
                        title_patterns.append(phrase)
        
        # Extrair palavras-chave adicionais dos títulos
        title_words = ' '.join(all_titles).lower()
        title_words = re.sub(r'[^\w\s]', ' ', title_words)
        word_list = title_words.split()
        
        # Contar frequência das palavras
        from collections import Counter
        word_counts = Counter(word_list)
        
        # Adicionar palavras frequentes às palavras-chave
        for word, count in word_counts.most_common(30):
            if len(word) > 3 and word not in keywords and count > 1:
                keywords.append(word)
        
        # Remover duplicatas e limitar o tamanho das listas
        title_patterns = list(set(title_patterns))[:20]
        keywords = list(set(keywords))[:30]
        
        logger.info(f"Extraídos {len(title_patterns)} padrões de títulos e {len(keywords)} palavras-chave")
        return title_patterns, keywords
    
    def _parse_duration(self, duration_str: str) -> int:
        """
        Converte a duração no formato ISO 8601 para minutos.
        
        Args:
            duration_str: String de duração no formato ISO 8601 (PT1H30M15S)
            
        Returns:
            Duração em minutos
        """
        if not duration_str or not isinstance(duration_str, str):
            return 0
        
        # Remover o prefixo PT
        duration = duration_str.replace("PT", "")
        
        # Inicializar variáveis
        hours = 0
        minutes = 0
        seconds = 0
        
        # Extrair horas, minutos e segundos
        if "H" in duration:
            hours_part = duration.split("H")[0]
            duration = duration.split("H")[1]
            try:
                hours = int(hours_part)
            except ValueError:
                hours = 0
        
        if "M" in duration:
            minutes_part = duration.split("M")[0]
            duration = duration.split("M")[1]
            try:
                minutes = int(minutes_part)
            except ValueError:
                minutes = 0
        
        if "S" in duration:
            seconds_part = duration.split("S")[0]
            try:
                seconds = int(seconds_part)
            except ValueError:
                seconds = 0
        
        # Calcular duração total em minutos
        total_minutes = hours * 60 + minutes + seconds / 60
        
        return total_minutes
    
    def calculate_relevance_score(self, video: Dict) -> float:
        """
        Calcula uma pontuação de relevância para o vídeo com base no título e descrição.
        
        Args:
            video: Dicionário com dados do vídeo
            
        Returns:
            Pontuação de relevância (0-10)
        """
        if not video:
            return 0
        
        # Inicializar pontuação
        score = 0
        
        # Obter título e descrição
        title = video.get('title', '').lower()
        description = video.get('description', '').lower() if 'description' in video else ''
        
        # Verificar palavras-chave no título
        for keyword in self.revenge_keywords:
            if keyword.lower() in title:
                score += 1.5  # Peso maior para palavras-chave no título
            if description and keyword.lower() in description:
                score += 0.5  # Peso menor para palavras-chave na descrição
        
        # Verificar padrões narrativos no título
        for pattern in self.narrative_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                score += 2  # Peso significativo para padrões narrativos
        
        # Verificar padrões de títulos no título
        for pattern in self.title_patterns:
            pattern_lower = pattern.lower()
            if pattern_lower in title:
                score += 1.5  # Peso para padrões de títulos
        
        # Verificar duração (bônus para vídeos mais longos)
        duration_str = video.get('duration', '')
        duration_minutes = self._parse_duration(duration_str)
        if duration_minutes >= 30:
            score += 1  # Bônus para vídeos de 30+ minutos
        if duration_minutes >= 45:
            score += 0.5  # Bônus adicional para vídeos de 45+ minutos
        
        # Verificar canal (bônus para canais de referência)
        channel_id = video.get('channel_id', '')
        reference_channel_ids = [ref.get('channel_id', '') for ref in self.reference_data]
        if channel_id in reference_channel_ids:
            score += 3  # Bônus significativo para canais de referência
        
        # Normalizar pontuação para escala 0-10
        score = min(10, score)
        
        return score
    
    def filter_by_relevance(self, videos_df: pd.DataFrame, min_relevance_score: float = 5.0) -> pd.DataFrame:
        """
        Filtra vídeos com base na pontuação de relevância para o nicho de histórias de vingança.
        
        Args:
            videos_df: DataFrame com dados dos vídeos
            min_relevance_score: Pontuação mínima de relevância
            
        Returns:
            DataFrame filtrado
        """
        if videos_df.empty:
            return videos_df
        
        # Calcular pontuação de relevância para cada vídeo
        relevance_scores = []
        
        for _, row in videos_df.iterrows():
            video_dict = {
                'title': row['title'],
                'description': row.get('description', ''),
                'duration': row.get('duration', ''),
                'channel_id': row.get('channel_id', '')
            }
            relevance_score = self.calculate_relevance_score(video_dict)
            relevance_scores.append(relevance_score)
        
        # Adicionar pontuação de relevância ao DataFrame
        videos_df['relevance_score'] = relevance_scores
        
        # Filtrar por pontuação de relevância
        filtered_df = videos_df[videos_df['relevance_score'] >= min_relevance_score].copy()
        
        logger.info(f"Filtro por relevância: {len(filtered_df)} de {len(videos_df)} vídeos têm pontuação de relevância de pelo menos {min_relevance_score}")
        
        return filtered_df
    
    def filter_by_title_keywords(self, videos_df: pd.DataFrame, min_keywords: int = 2) -> pd.DataFrame:
        """
        Filtra vídeos com base na presença de palavras-chave no título.
        
        Args:
            videos_df: DataFrame com dados dos vídeos
            min_keywords: Número mínimo de palavras-chave que devem estar presentes
            
        Returns:
            DataFrame filtrado
        """
        if videos_df.empty:
            return videos_df
        
        # Função para contar palavras-chave no título
        def count_keywords(title):
            title_lower = title.lower()
            count = 0
            for keyword in self.revenge_keywords:
                if keyword.lower() in title_lower:
                    count += 1
            return count
        
        # Contar palavras-chave em cada título
        videos_df['keyword_count'] = videos_df['title'].apply(count_keywords)
        
        # Filtrar por número mínimo de palavras-chave
        filtered_df = videos_df[videos_df['keyword_count'] >= min_keywords].copy()
        
        logger.info(f"Filtro por palavras-chave no título: {len(filtered_df)} de {len(videos_df)} vídeos têm pelo menos {min_keywords} palavras-chave")
        
        return filtered_df
    
    def filter_by_narrative_patterns(self, videos_df: pd.DataFrame, min_patterns: int = 1) -> pd.DataFrame:
        """
        Filtra vídeos com base na presença de padrões narrativos no título.
        
        Args:
            videos_df: DataFrame com dados dos vídeos
            min_patterns: Número mínimo de padrões que devem estar presentes
            
        Returns:
            DataFrame filtrado
        """
        if videos_df.empty:
            return videos_df
        
        # Função para contar padrões narrativos no título
        def count_patterns(title):
            count = 0
            for pattern in self.narrative_patterns:
                if re.search(pattern, title, re.IGNORECASE):
                    count += 1
            return count
        
        # Contar padrões em cada título
        videos_df['pattern_count'] = videos_df['title'].apply(count_patterns)
        
        # Filtrar por número mínimo de padrões
        filtered_df = videos_df[videos_df['pattern_count'] >= min_patterns].copy()
        
        logger.info(f"Filtro por padrões narrativos: {len(filtered_df)} de {len(videos_df)} vídeos têm pelo menos {min_patterns} padrões")
        
        return filtered_df
    
    def process_videos_csv(self, input_path: str, output_path: str, min_duration_minutes: int = 20,
                         min_views: int = 50000, max_age_months: int = 3, min_relevance_score: float = 5.0,
                         target_count: int = 100) -> pd.DataFrame:
        """
        Processa um arquivo CSV de vídeos, aplicando filtros específicos para o nicho de vingança.
        
        Args:
            input_path: Caminho do arquivo CSV de entrada
            output_path: Caminho do arquivo CSV de saída
            min_duration_minutes: Duração mínima em minutos
            min_views: Número mínimo de visualizações
            max_age_months: Idade máxima em meses
            min_relevance_score: Pontuação mínima de relevância
            target_count: Número alvo de vídeos
            
        Returns:
            DataFrame com os vídeos selecionados
        """
        try:
            # Carregar dados
            videos_df = pd.read_csv(input_path)
            logger.info(f"Carregados {len(videos_df)} vídeos de {input_path}")
            
            # Aplicar filtragem adaptativa
            logger.info(f"Aplicando filtragem adaptativa para obter {target_count} vídeos...")
            
            # 1. Filtrar por duração (converter para minutos primeiro)
            videos_df['duration_minutes'] = videos_df['duration'].apply(self._parse_duration)
            filtered_df = videos_df[videos_df['duration_minutes'] >= min_duration_minutes].copy()
            logger.info(f"Filtro por duração: {len(filtered_df)} de {len(videos_df)} vídeos têm pelo menos {min_duration_minutes} minutos")
            
            # 2. Filtrar por visualizações
            filtered_df['view_count'] = pd.to_numeric(filtered_df['view_count'], errors='coerce')
            filtered_df = filtered_df[filtered_df['view_count'] >= min_views].copy()
            logger.info(f"Filtro por visualizações: {len(filtered_df)} de {len(videos_df)} vídeos têm pelo menos {min_views} visualizações")
            
            # 3. Filtrar por data - CORREÇÃO DO ERRO DE COMPARAÇÃO DE DATAS
            try:
                # Converter published_at para datetime sem fuso horário
                filtered_df['published_at'] = pd.to_datetime(filtered_df['published_at'], errors='coerce')
                
                # Remover o componente de fuso horário para evitar erros de comparação
                filtered_df['published_at'] = filtered_df['published_at'].dt.tz_localize(None)
                
                # Calcular data limite sem fuso horário
                cutoff_date = datetime.now() - timedelta(days=30 * max_age_months)
                
                # Aplicar filtro de data
                date_filtered_df = filtered_df[filtered_df['published_at'] >= cutoff_date].copy()
                
                logger.info(f"Filtro por data: {len(date_filtered_df)} de {len(filtered_df)} vídeos foram publicados nos últimos {max_age_months} meses")
                
                # Atualizar DataFrame filtrado
                filtered_df = date_filtered_df
                
            except Exception as e:
                logger.error(f"Erro ao filtrar por data (usando método alternativo): {str(e)}")
                
                # Método alternativo: extrair a data como string e comparar
                try:
                    # Extrair apenas a parte da data (YYYY-MM-DD)
                    filtered_df['published_date'] = filtered_df['published_at'].astype(str).str[:10]
                    
                    # Converter para datetime
                    filtered_df['published_date'] = pd.to_datetime(filtered_df['published_date'], errors='coerce')
                    
                    # Calcular data limite
                    cutoff_date = datetime.now().date() - timedelta(days=30 * max_age_months)
                    
                    # Aplicar filtro de data
                    date_filtered_df = filtered_df[filtered_df['published_date'] >= cutoff_date].copy()
                    
                    logger.info(f"Filtro por data (método alternativo): {len(date_filtered_df)} de {len(filtered_df)} vídeos foram publicados nos últimos {max_age_months} meses")
                    
                    # Atualizar DataFrame filtrado
                    filtered_df = date_filtered_df
                    
                except Exception as e2:
                    logger.error(f"Erro ao filtrar por data (método alternativo também falhou): {str(e2)}")
                    logger.warning("Ignorando filtro de data devido a erros persistentes")
            
            # 4. Filtrar por relevância (critério adaptativo)
            current_relevance_score = min_relevance_score
            relevance_filtered_df = self.filter_by_relevance(filtered_df, current_relevance_score)
            
            # Ajustar pontuação de relevância para atingir o número alvo
            while len(relevance_filtered_df) < target_count and current_relevance_score > 1.0:
                current_relevance_score -= 0.5
                relevance_filtered_df = self.filter_by_relevance(filtered_df, current_relevance_score)
            
            while len(relevance_filtered_df) > target_count * 2 and current_relevance_score < 9.0:
                current_relevance_score += 0.5
                relevance_filtered_df = self.filter_by_relevance(filtered_df, current_relevance_score)
            
            # 5. Aplicar filtros adicionais se ainda tivermos muitos vídeos
            if len(relevance_filtered_df) > target_count * 1.5:
                # Filtrar por palavras-chave no título
                keyword_filtered_df = self.filter_by_title_keywords(relevance_filtered_df, min_keywords=2)
                if len(keyword_filtered_df) >= target_count:
                    relevance_filtered_df = keyword_filtered_df
            
            if len(relevance_filtered_df) > target_count * 1.2:
                # Filtrar por padrões narrativos
                pattern_filtered_df = self.filter_by_narrative_patterns(relevance_filtered_df, min_patterns=1)
                if len(pattern_filtered_df) >= target_count:
                    relevance_filtered_df = pattern_filtered_df
            
            # Ordenar por pontuação de relevância e visualizações
            result_df = relevance_filtered_df.sort_values(by=['relevance_score', 'view_count'], ascending=[False, False])
            
            logger.info(f"Filtragem adaptativa: usando min_duration_minutes={min_duration_minutes}, min_views={min_views}, "
                      f"max_age_months={max_age_months}, min_relevance_score={current_relevance_score}")
            logger.info(f"Após filtragem adaptativa: {len(relevance_filtered_df)} vídeos")
            
            # Selecionar os melhores vídeos
            if len(result_df) > target_count:
                result_df = result_df.head(target_count)
            
            logger.info(f"Selecionados os {len(result_df)} melhores vídeos de {len(relevance_filtered_df)} disponíveis")
            
            # Salvar resultados
            result_df.to_csv(output_path, index=False)
            logger.info(f"Salvos {len(result_df)} vídeos em {output_path}")
            
            return result_df
            
        except Exception as e:
            logger.error(f"Erro ao processar vídeos: {str(e)}")
            return pd.DataFrame()
