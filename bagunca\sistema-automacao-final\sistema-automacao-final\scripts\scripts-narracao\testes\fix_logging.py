"""
Correção para problemas de logging com emojis no Windows

Este arquivo contém versões dos logs sem emojis para evitar
erros de UnicodeEncodeError no Windows com cp1252.
"""

import logging
import sys
import io

def setup_utf8_logging():
    """
    Configura logging para UTF-8 no Windows para suportar emojis.
    """
    if sys.platform.startswith('win'):
        # Força UTF-8 para stdout/stderr no Windows
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8')

def get_clean_message(message_with_emojis):
    """
    Remove emojis de mensagens de log para compatibilidade com Windows.
    
    Args:
        message_with_emojis: Mensagem original com emojis
        
    Returns:
        Mensagem sem emojis
    """
    emoji_replacements = {
        '🔑': '[CHAVES]',
        '🌐': '[PROXY]', 
        '🎵': '[TTS]',
        '📁': '[ARQUIVO]',
        '🚀': '[INICIO]',
        '📊': '[STATUS]',
        '📄': '[DOCUMENTO]',
        '✅': '[OK]',
        '❌': '[ERRO]',
        '⚠️': '[AVISO]',
        '🔄': '[ROTACAO]',
        '🚫': '[BLOQUEADO]',
        '💡': '[INFO]',
        '📋': '[RELATORIO]',
        '🎉': '[SUCESSO]',
        '🔧': '[CONFIG]',
        '🎭': '[SISTEMA]',
        '💥': '[FATAL]',
        '⏹️': '[PARADO]',
        '⏱️': '[TEMPO]',
        '📂': '[PASTA]',
        '🧪': '[TESTE]',
        '🔗': '[CONCAT]',
        '💰': '[CUSTO]',
        '🎯': '[FOCO]'
    }
    
    clean_message = message_with_emojis
    for emoji, replacement in emoji_replacements.items():
        clean_message = clean_message.replace(emoji, replacement)
    
    return clean_message

# Classe para logger compatível com Windows
class WindowsSafeLogger:
    """Logger que automaticamente remove emojis para compatibilidade com Windows."""
    
    def __init__(self, name):
        self.logger = logging.getLogger(name)
    
    def info(self, message):
        clean_msg = get_clean_message(str(message))
        self.logger.info(clean_msg)
    
    def warning(self, message):
        clean_msg = get_clean_message(str(message))
        self.logger.warning(clean_msg)
    
    def error(self, message):
        clean_msg = get_clean_message(str(message))
        self.logger.error(clean_msg)
    
    def debug(self, message):
        clean_msg = get_clean_message(str(message))
        self.logger.debug(clean_msg)
    
    def exception(self, message):
        clean_msg = get_clean_message(str(message))
        self.logger.exception(clean_msg)

def get_safe_logger(name):
    """
    Retorna um logger seguro para Windows que remove emojis automaticamente.
    
    Args:
        name: Nome do logger
        
    Returns:
        WindowsSafeLogger instance
    """
    return WindowsSafeLogger(name)

# Exemplo de uso
if __name__ == "__main__":
    # Configura logging básico
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Testa logger seguro
    logger = get_safe_logger("TestLogger")
    
    print("Testando logger compatível com Windows...")
    logger.info("🔑 Teste com emoji de chave")
    logger.warning("⚠️ Teste com emoji de aviso")
    logger.error("❌ Teste com emoji de erro")
    
    print("Logs enviados sem erros de Unicode!")
