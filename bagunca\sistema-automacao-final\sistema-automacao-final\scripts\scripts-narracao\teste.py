import requests
import urllib3
import json

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ClaudeBot:
    def __init__(self):
        self.session = requests.Session()
        self.session.verify = False
        
        self.session.headers.update({
            'anthropic-client-platform': 'web_claude_ai',
            'content-type': 'application/json',
            'origin': 'https://claude.ai',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'accept': 'text/event-stream',
        })
        
        self.session.cookies.update({
            'sessionKey': '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',
            'lastActiveOrg': 'a4a7451a-e92e-460a-ae5b-8f2c63eacd30',
        })
        
        self.base_url = "https://claude.ai/api/organizations/a4a7451a-e92e-460a-ae5b-8f2c63eacd30/chat_conversations/5c1cf506-df45-4e5f-8f64-5371e075f092"
    
    def send_message(self, message, show_typing=True):
        payload = {
            "prompt": message,
            "timezone": "America/Sao_Paulo",
            "locale": "pt-BR",
            "tools": [
                {"type": "web_search_v0", "name": "web_search"},
                {"type": "artifacts_v0", "name": "artifacts"}
            ],
            "attachments": [],
            "files": [],
            "rendering_mode": "messages"
        }
        
        response = self.session.post(f"{self.base_url}/completion", json=payload, stream=True)
        
        full_text = ""
        
        for line in response.iter_lines(decode_unicode=True):
            if line and line.startswith('data: '):
                try:
                    data = json.loads(line[6:])
                    
                    if data.get('type') == 'content_block_delta':
                        if 'delta' in data and 'text' in data['delta']:
                            text = data['delta']['text']
                            full_text += text
                            if show_typing:
                                print(text, end='', flush=True)
                                
                except:
                    continue
                    
        return full_text

# Teste
if __name__ == "__main__":
    bot = ClaudeBot()
    
    # Mensagem simples
    response = bot.send_message("Olá!")
    print(f"\n\nResposta: {response}")