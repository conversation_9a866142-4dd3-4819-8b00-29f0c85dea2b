"""
Teste para ver se proxies conseguem reativar contas bloqueadas
"""

import sys
from pathlib import Path
import time

sys.path.append(str(Path(__file__).parent))

from api_key_manager import APIKeyManager
from proxy_manager import WebshareProxyManager

def test_key_with_proxy(api_key, proxy_manager):
    """Testa uma chave específica com proxy"""
    import requests
    
    headers = {
        "xi-api-key": api_key,
        "Content-Type": "application/json"
    }
    
    # Teste simples - listar vozes
    url = "https://api.elevenlabs.io/v1/voices"
    
    try:
        # Usa proxy para fazer requisição
        response = proxy_manager.make_request_with_proxy(
            method="GET",
            url=url,
            headers=headers,
            max_retries=1
        )
        
        if response.status_code == 200:
            return True, "OK"
        else:
            return False, f"HTTP {response.status_code}"
            
    except Exception as e:
        return False, str(e)

def main():
    print("🧪 TESTE DE RESSURREIÇÃO: PROXY + CONTAS BLOQUEADAS")
    print("="*60)
    
    keys_file = "chaves-api-elevenlabs.txt"
    webshare_token = "mt643rsq556yqggigaa3emmmjmdycc2h6868bi7b"
    
    try:
        # 1. Inicializa managers
        print("1️⃣ Carregando chaves...")
        key_manager = APIKeyManager(keys_file)
        
        print("2️⃣ Inicializando proxy...")
        proxy_manager = WebshareProxyManager(
            api_token=webshare_token,
            use_rotating_endpoint=True,
            enable_fallback=True
        )
        
        # 3. Testa algumas chaves bloqueadas com proxy
        print("3️⃣ Testando chaves com proxy rotation...")
        
        # Pega as primeiras 5 chaves (que sabemos que estão bloqueadas)
        test_keys = key_manager.api_keys[:5]
        
        working_keys = []
        still_blocked = []
        
        for i, api_key in enumerate(test_keys, 1):
            key_preview = f"{api_key[:8]}..."
            print(f"\n   Testando {i}/5: {key_preview}")
            
            # Testa sem proxy primeiro
            print(f"      🌐 Sem proxy...", end=" ")
            success_no_proxy, error_no_proxy = test_key_with_proxy_simple(api_key)
            print("✅ OK" if success_no_proxy else f"❌ {error_no_proxy}")
            
            # Agora com proxy
            print(f"      🔄 Com proxy...", end=" ")
            success_with_proxy, error_with_proxy = test_key_with_proxy(api_key, proxy_manager)
            print("✅ OK" if success_with_proxy else f"❌ {error_with_proxy}")
            
            if success_with_proxy:
                working_keys.append(api_key)
                print(f"      🎉 RESSUSCITADA!")
            else:
                still_blocked.append(api_key)
            
            # Pausa entre testes
            time.sleep(2)
        
        # 4. Resultado
        print(f"\n4️⃣ RESULTADO DO TESTE:")
        print(f"   ✅ Chaves funcionando com proxy: {len(working_keys)}")
        print(f"   ❌ Chaves ainda bloqueadas: {len(still_blocked)}")
        
        if working_keys:
            print(f"\n🎯 CHAVES RESSUSCITADAS:")
            for key in working_keys:
                print(f"   • {key[:8]}...")
            
            print(f"\n✨ SUCESSO! O proxy conseguiu reativar algumas contas!")
            print(f"💡 Recomendação:")
            print(f"   - Use apenas as chaves ressuscitadas")
            print(f"   - Sempre use proxy para futuras requisições")
            print(f"   - Evite fazer muitas requisições seguidas")
            
        else:
            print(f"\n😞 Nenhuma chave foi ressuscitada.")
            print(f"💡 Opções:")
            print(f"   1. Aguardar algumas horas e tentar novamente")
            print(f"   2. Criar 1-2 contas novas + usar proxy desde o início")
            print(f"   3. Considerar plano pago da ElevenLabs")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ ERRO: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_key_with_proxy_simple(api_key):
    """Teste simples sem proxy"""
    import requests
    
    headers = {
        "xi-api-key": api_key,
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(
            "https://api.elevenlabs.io/v1/voices",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            return True, "OK"
        else:
            return False, f"HTTP {response.status_code}"
            
    except Exception as e:
        return False, str(e)

if __name__ == "__main__":
    sys.exit(main())
