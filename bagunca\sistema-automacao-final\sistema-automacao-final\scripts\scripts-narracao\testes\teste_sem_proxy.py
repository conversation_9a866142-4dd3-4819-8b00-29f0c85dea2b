#!/usr/bin/env python3
"""
Teste Sem Proxies - Verifica se o problema são os proxies

Este script testa TTS diretamente (sem proxies) para confirmar
se o bloqueio é devido ao uso de proxies.
"""

import requests
import json
from pathlib import Path

def test_without_proxy():
    """Testa TTS sem usar proxies."""
    print("=" * 60)
    print("TESTE SEM PROXIES - DIAGNÓSTICO DE BLOQUEIO")
    print("=" * 60)
    
    # Lê chaves
    keys_path = Path("config/chaves-api-elevenlabs.txt")
    
    if not keys_path.exists():
        print(f"[ERRO] Chaves não encontradas: {keys_path}")
        return False
    
    with open(keys_path, 'r') as f:
        keys = [line.strip() for line in f.readlines() if line.strip()]
    
    if not keys:
        print(f"[ERRO] Nenhuma chave no arquivo")
        return False
    
    print(f"[INFO] Testando {min(3, len(keys))} chaves SEM PROXY...")
    
    # Configuração funcionando
    voice_id = "pNInz6obpgDQGcFmaJgB"  # Adam
    model_id = "eleven_multilingual_v2"
    output_format = "mp3_22050_32"
    
    working_keys = []
    
    for i, api_key in enumerate(keys[:3]):  # Testa 3 primeiras chaves
        print(f"\n[TESTE {i+1}] Chave: {api_key[:8]}...")
        
        # Headers para requisição DIRETA (sem proxy)
        headers = {
            "xi-api-key": api_key,
            "Content-Type": "application/json",
            "Accept": "audio/mpeg"
        }
        
        payload = {
            "text": "Teste sem proxy funcionando.",
            "model_id": model_id,
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.5
            }
        }
        
        try:
            # Requisição DIRETA - SEM PROXY
            response = requests.post(
                f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}",
                headers=headers,
                params={"output_format": output_format},
                json=payload,
                timeout=30
                # NÃO usa proxies=... aqui!
            )
            
            print(f"[RESULTADO] Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"[SUCESSO] ✅ Chave funciona SEM PROXY!")
                print(f"[INFO] Áudio gerado: {len(response.content)} bytes")
                working_keys.append(api_key)
                
            elif response.status_code == 401:
                try:
                    error_data = response.json()
                    detail = error_data.get("detail", {})
                    status = detail.get("status", "unknown")
                    message = detail.get("message", "Unauthorized")
                    
                    if "unusual_activity" in status or "proxy" in message.lower():
                        print(f"[BLOQUEIO] ❌ Conta bloqueada por uso de proxy")
                        print(f"[DETALHE] {message[:100]}...")
                    else:
                        print(f"[ERRO] ❌ Chave inválida ou expirada")
                        print(f"[DETALHE] {status}: {message[:100]}...")
                        
                except:
                    print(f"[ERRO] ❌ Erro 401: {response.text[:100]}")
                    
            else:
                print(f"[ERRO] ❌ Erro {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"[DETALHE] {error_data}")
                except:
                    print(f"[DETALHE] {response.text[:100]}")
                    
        except Exception as e:
            print(f"[ERRO] ❌ Exceção: {e}")
    
    # Resumo
    print(f"\n" + "=" * 60)
    print("RESUMO DO TESTE SEM PROXY")
    print(f"=" * 60)
    print(f"Chaves testadas: {min(3, len(keys))}")
    print(f"Chaves funcionais (sem proxy): {len(working_keys)}")
    
    if working_keys:
        print(f"\n✅ CONFIRMADO: Chaves funcionam SEM PROXY!")
        print(f"❌ PROBLEMA: ElevenLabs bloqueou contas gratuitas que usam proxy")
        
        print(f"\n🔧 SOLUÇÕES DISPONÍVEIS:")
        print(f"  1. Executar sempre com --no-proxy")
        print(f"  2. Upgrade para plano pago ElevenLabs")
        print(f"  3. Aguardar desbloqueio (algumas horas)")
        print(f"  4. Usar contas que nunca usaram proxy")
        
        print(f"\n⚡ EXECUTE AGORA (sem proxy):")
        print(f"   python narrador_com_proxy_corrigido.py --no-proxy")
        
        return True
    else:
        print(f"\n❌ PROBLEMA MAIOR: Chaves não funcionam nem sem proxy")
        print(f"Possíveis causas:")
        print(f"  1. Todas as contas foram suspensas")
        print(f"  2. Chaves API expiradas")
        print(f"  3. Contas com créditos esgotados")
        
        print(f"\n🔍 NEXT STEPS:")
        print(f"  1. Verificar https://elevenlabs.io/")
        print(f"  2. Gerar novas chaves")
        print(f"  3. Criar nova conta ElevenLabs")
        
        return False

def create_no_proxy_config():
    """Cria configuração otimizada para uso sem proxy."""
    config_path = Path("config/config_narracao_sem_proxy.json")
    
    config = {
        "api": {
            "base_url": "https://api.elevenlabs.io",
            "max_retries": 5,
            "retry_delay": 3,
            "max_key_retries": 8
        },
        "voice": {
            "voice_id": "pNInz6obpgDQGcFmaJgB",
            "voice_name": "Adam",
            "model_id": "eleven_multilingual_v2",
            "output_format": "mp3_22050_32"
        },
        "processing": {
            "tamanho_minimo_bloco": 600,
            "tamanho_maximo_bloco": 800,
            "min_block_size_on_fallback": 50,
            "respect_punctuation": True,
            "default_language": "pt-BR"
        },
        "proxy": {
            "enabled": False,
            "webshare_api_token": "",
            "rotation_threshold": 0,
            "enable_fallback": True
        },
        "paths": {
            "output_directory": "audios_gerados",
            "log_file": "narracao_sem_proxy.log",
            "roteiros_directory": "roteiros_gerados",
            "config_directory": "config"
        },
        "logging": {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file_enabled": True,
            "console_enabled": True
        }
    }
    
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"[CRIADO] Configuração sem proxy salva: {config_path}")
    return config_path

if __name__ == "__main__":
    success = test_without_proxy()
    
    if success:
        config_path = create_no_proxy_config()
        print(f"\n📁 CONFIGURAÇÃO SEM PROXY CRIADA!")
        print(f"   Arquivo: {config_path}")
        print(f"\n🚀 COMANDO PARA USAR:")
        print(f"   python narrador_com_proxy_corrigido.py --no-proxy")
    
    print(f"\n💡 LIÇÃO APRENDIDA:")
    print(f"   ElevenLabs detecta e bloqueia contas gratuitas que usam proxies")
    print(f"   Para usar proxies é necessário plano pago")
