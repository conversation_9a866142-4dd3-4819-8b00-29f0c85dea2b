# -*- coding: utf-8 -*-
import os
import re
import time # Added for polling Assistant status
import sys
import openai # Use the official OpenAI library

# --- Configuration ---
ASSISTANT_ID = "asst_iNhFrVhxi7Kuj5bsYIvRrUDi" # User-provided Assistant ID
POLLING_INTERVAL = 2 # Seconds to wait between polling Assistant run status
MAX_WAIT_TIME = 300 # Maximum seconds to wait for Assistant run completion (5 minutes)

# Define project root relative to this script's location
# Assumes script is in sistema-automacao-total/scripts/scripts-titulos/
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# --- Paths based on the new structure relative to PROJECT_ROOT ---
PREMISES_FILE = os.path.join(PROJECT_ROOT, "generated_premises.md")
API_KEY_FILE = os.path.join(PROJECT_ROOT, "config", "api_key_openai.txt") # Corrected path based on user log
OUTPUT_FILE = os.path.join(PROJECT_ROOT, "generated_titles_agent.txt")
LOG_DIR = os.path.join(PROJECT_ROOT, "logs")
LOG_FILE = os.path.join(LOG_DIR, "titulo_assistant.log") # Updated log file name

# Ensure log directory exists
os.makedirs(LOG_DIR, exist_ok=True)

# --- Helper Functions ---
def log_message(message):
    """Logs a message to the console and the log file."""
    print(message)
    try:
        with open(LOG_FILE, "a", encoding="utf-8") as f:
            f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
    except Exception as e:
        print(f"Erro ao escrever no log: {e}")

def load_api_key(filepath):
    """Loads the OpenAI API key from a file."""
    # Check the corrected path first
    if not os.path.exists(filepath):
        # Fallback to the path from the user's log if the primary fails
        fallback_path = os.path.join(PROJECT_ROOT, "config", "chaves-api-openai.txt")
        if os.path.exists(fallback_path):
            filepath = fallback_path
            log_message(f"Aviso: Usando caminho alternativo para chave API: {filepath}")
        else:
            log_message(f"Erro: Arquivo de chave API não encontrado em {filepath} ou {fallback_path}")
            log_message(f"Por favor, crie o arquivo '{os.path.relpath(filepath, PROJECT_ROOT)}' ou '{os.path.relpath(fallback_path, PROJECT_ROOT)}' com sua chave da API OpenAI.")
            exit(1)
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            return f.read().strip()
    except Exception as e:
        log_message(f"Erro ao ler a chave API de {filepath}: {e}")
        exit(1)

def load_premises(filepath):
    """Loads premises from the new markdown file format."""
    premises_list = []
    if not os.path.exists(filepath):
        log_message(f"Erro: Arquivo de premissas não encontrado em {filepath}")
        return []
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read()
            # Regex for the new format: number. **Premissa X:** Body
            # It captures the premise number (group 1) and the premise body (group 2)
            matches = re.findall(
                r"^(\d+)\.\s+\*\*Premissa\s+\d+:\*\*\s+(.*)$",
                content,
                re.MULTILINE
            )

            # No need to sort if the file is already ordered by number
            # matches.sort(key=lambda x: int(x[0]))

            for match in matches:
                premise_number = int(match[0])
                premise_body = match[1].strip()
                # Store number, an empty string (as original title isn't needed), and body
                premises_list.append((premise_number, "", premise_body))

        if not premises_list:
            # Add a check for the previous formats just in case
            log_message(f"Aviso: Nenhuma premissa encontrada no formato novo em {filepath}. Tentando formatos antigos...")
            # Try original markdown format
            matches_old1 = re.findall(
                r"^#+\s+Premissa\s+(\d+)\s*\**\s*(.*?)\s*\**\s*:\s*(.*?)(?=\n\n#+|$)",
                content,
                re.MULTILINE | re.DOTALL,
            )
            # Try numbered list format
            matches_old2 = re.findall(
                r"^(\d+)\.\s+\*\*(.*?)\*\*:\s+(.*?)(?=\n\n\d+\.|$)",
                content,
                re.MULTILINE | re.DOTALL,
            )
            if matches_old1:
                 log_message("Formato antigo (Markdown Header) detectado.")
                 matches_old1.sort(key=lambda x: int(x[0]))
                 for match in matches_old1:
                     premises_list.append((int(match[0]), match[1].strip(), match[2].strip()))
            elif matches_old2:
                 log_message("Formato antigo (Numbered List) detectado.")
                 matches_old2.sort(key=lambda x: int(x[0]))
                 for match in matches_old2:
                     premises_list.append((int(match[0]), match[1].strip(), match[2].strip()))
            else:
                 log_message(f"Aviso Final: Nenhuma premissa encontrada em nenhum formato conhecido em {filepath}")

        return premises_list
    except Exception as e:
        log_message(f"Erro ao ler premissas: {e}")
        return []

# --- Title Generation with OpenAI Assistant ---
def generate_title_with_assistant(client, assistant_id, premise_body):
    """Generates a title using the specified OpenAI Assistant."""
    try:
        # 1. Create a Thread
        thread = client.beta.threads.create()
        log_message(f"    Criada Thread ID: {thread.id}")

        # 2. Add Message to Thread
        message = client.beta.threads.messages.create(
            thread_id=thread.id,
            role="user",
            content=f"Gere um título para a seguinte premissa:\n\n{premise_body}"
        )
        log_message(f"    Mensagem adicionada à Thread {thread.id}")

        # 3. Run the Assistant
        run = client.beta.threads.runs.create(
            thread_id=thread.id,
            assistant_id=assistant_id,
        )
        log_message(f"    Iniciado Run ID: {run.id} para Thread {thread.id}")

        # 4. Poll for Run Completion
        start_time = time.time()
        while run.status not in ["completed", "failed", "cancelled", "expired", "requires_action"]:
            if time.time() - start_time > MAX_WAIT_TIME:
                log_message(f"    Erro: Run {run.id} excedeu o tempo limite de {MAX_WAIT_TIME}s.")
                try:
                    client.beta.threads.runs.cancel(thread_id=thread.id, run_id=run.id)
                    log_message(f"    Run {run.id} cancelado.")
                except Exception as cancel_err:
                    log_message(f"    Aviso: Falha ao cancelar Run {run.id}: {cancel_err}")
                return "[ERRO: TIMEOUT]"

            time.sleep(POLLING_INTERVAL)
            run = client.beta.threads.runs.retrieve(thread_id=thread.id, run_id=run.id)
            log_message(f"    Status do Run {run.id}: {run.status}")

        # 5. Check Run Status and Retrieve Messages
        if run.status == "completed":
            messages = client.beta.threads.messages.list(thread_id=thread.id, order="desc")
            for msg in messages.data:
                if msg.role == "assistant":
                    assistant_response = ""
                    for content_block in msg.content:
                        if content_block.type == 'text':
                            assistant_response += content_block.text.value
                    
                    cleaned_title = assistant_response.strip()
                    cleaned_title = re.sub(r'^["\']+|["\']+$', '', cleaned_title)
                    log_message(f"    Título gerado pelo Assistant: '{cleaned_title}'")
                    # Consider deleting the thread here if desired
                    # client.beta.threads.delete(thread.id)
                    return cleaned_title
            log_message(f"    Erro: Nenhuma resposta do assistente encontrada na Thread {thread.id}.")
            return "[ERRO: SEM RESPOSTA DO ASSISTENTE]"
        elif run.status == "requires_action":
             log_message(f"    Erro: Run {run.id} requer ação (não suportado neste script): {run.required_action}")
             return "[ERRO: REQUIRES_ACTION]"
        else:
            log_message(f"    Erro: Run {run.id} falhou ou foi cancelado/expirado com status: {run.status}")
            error_details = run.last_error
            if error_details:
                log_message(f"      Código do Erro: {error_details.code}")
                log_message(f"      Mensagem de Erro: {error_details.message}")
            return f"[ERRO: RUN {run.status.upper()}]"

    except openai.APIError as e:
        log_message(f"    Erro na API OpenAI: Status={e.status_code}, Resposta={e.response}")
        return "[ERRO: API OPENAI]"
    except Exception as e:
        log_message(f"    Erro inesperado durante a geração com Assistant: {e.__class__.__name__}: {e}")
        return "[ERRO: INESPERADO]"

# --- Main Agent Logic ---
def run_agent():
    """Loads data, runs the title generation loop using Assistant, and saves the results."""
    log_message(f"--- Iniciando Agente de Títulos (Modo Assistant) ---")
    log_message(f"Usando Raiz do Projeto: {PROJECT_ROOT}")
    log_message(f"Lendo Premissas de: {PREMISES_FILE}")
    log_message(f"Lendo Chave API de: {API_KEY_FILE}")
    log_message(f"Usando Assistant ID: {ASSISTANT_ID}")
    log_message(f"Salvando Títulos em: {OUTPUT_FILE}")
    log_message(f"Salvando Logs em: {LOG_FILE}")

    api_key = load_api_key(API_KEY_FILE)
    try:
        client = openai.OpenAI(api_key=api_key)
        log_message("Cliente OpenAI inicializado com sucesso.")
    except Exception as e:
        log_message(f"Erro ao inicializar o cliente OpenAI: {e}")
        exit(1)

    premises_data = load_premises(PREMISES_FILE)
    if not premises_data:
        log_message("Nenhuma premissa carregada. Saindo.")
        exit(1)
    log_message(f"{len(premises_data)} premissas carregadas.")

    log_message("--- Iniciando Geração de Títulos via Assistant ---")
    generated_results = []

    # Use enumerate starting from 1 if premise_num from file isn't reliable
    for i, (premise_num, _, premise_body) in enumerate(premises_data):
        log_message(f"\nProcessando Premissa {premise_num}...") # Removed premise_title from log

        generated_title = generate_title_with_assistant(
            client, ASSISTANT_ID, premise_body
        )
        generated_results.append((premise_num, generated_title))

    log_message("\n--- Geração Concluída ---")

    try:
        with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
            f.write(f"# Títulos Gerados via Assistant ({ASSISTANT_ID}) - {len(generated_results)} títulos\n\n")
            for num, title in generated_results:
                f.write(f"## Premissa {num}\n")
                f.write(f"{title}\n\n")
        log_message(f"Resultados salvos em: {OUTPUT_FILE}")
    except Exception as e:
        log_message(f"Erro ao salvar resultados: {e}")

# --- Run the Agent ---
if __name__ == "__main__":
    run_agent()

