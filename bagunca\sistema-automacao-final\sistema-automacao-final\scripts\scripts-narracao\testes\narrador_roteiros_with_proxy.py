# --- Start of file ---
import os
import sys
import logging
import re
from pathlib import Path
import argparse
import json

# Importa os módulos desenvolvidos
from config_manager import ConfigManager
from api_key_manager import APIKeyManager
from proxy_manager import WebshareProxyManager  # NOVO: Gerenciador de proxies
from elevenlabs_api_atualizado import ElevenLabsAPI  # Versão V4 com proxy support
from processador_arquivos_atualizado import ProcessadorArquivos

# --- Define Project Root ---
SCRIPT_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = SCRIPT_DIR.parent.parent

# --- Configuração Inicial ---
CONFIG_FILE_PATH = PROJECT_ROOT / "config" / "config_narracao.json"
config_manager = ConfigManager(CONFIG_FILE_PATH)

# --- Configuração de Logging ---
LOG_DIR = PROJECT_ROOT / "logs"
LOG_DIR.mkdir(parents=True, exist_ok=True)
log_file_name = config_manager.get_config("paths", "log_file") or "narracao_v4_proxy.log"
log_file_path = LOG_DIR / log_file_name

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("NarradorRoteirosV4Proxy")

# --- Função clean_text (mantida) ---
def clean_text(raw_text):
    """Limpa o texto removendo símbolos e linhas indesejadas."""
    cleaned_lines = []
    for line in raw_text.splitlines():
        if re.match(r"^CENA \d+:|^ATO \d+:|^\*\*.+\*\*", line.strip(), re.IGNORECASE):
            cleaned_lines.append(line.strip())
            continue
        if line.strip().startswith(("#", "-")) or re.match(r"^\d+\. ", line.strip()):
             continue
        line = line.replace("*", "")
        cleaned_lines.append(line.strip())
    return "\n".join(filter(None, cleaned_lines))

# --- Função main V4 ---
def main():
    """Função principal do script V4 com proxy integration."""

    # --- Argument Parsing ---
    parser = argparse.ArgumentParser(description="Automatiza a narração de roteiros usando ElevenLabs com proxy rotation + gerenciamento de API aprimorado.")
    
    diretorio_roteiros_default = PROJECT_ROOT / "roteiros_gerados"
    diretorio_saida_default = PROJECT_ROOT / "narracao"
    arquivo_chaves_default = PROJECT_ROOT / "config" / "chaves-api-elevenlabs.txt"

    parser.add_argument(
        "--output",
        type=Path,
        default=diretorio_saida_default,
        help=f"Diretório de saída para os áudios gerados e relatório. Padrão: {diretorio_saida_default}"
    )
    parser.add_argument(
        "--keys",
        type=Path,
        default=arquivo_chaves_default,
        help=f"Caminho para o arquivo de chaves da API ElevenLabs. Padrão: {arquivo_chaves_default}"
    )
    parser.add_argument(
        "--proxy-token",
        type=str,
        default="mt643rsq556yqggigaa3emmmjmdycc2h6868bi7b",  # NOVO: Token da Webshare
        help="Token de API da Webshare.io para proxy rotation. Padrão: (seu token)"
    )
    parser.add_argument(
        "--no-proxy",
        action="store_true",
        help="Desabilita o uso de proxies (útil para debug ou se proxies não funcionarem)"
    )
    parser.add_argument(
        "--proxy-country",
        type=str,
        default=None,
        help="Código do país para proxies específicos (ex: 'US', 'BR', 'DE'). Se não especificado, usa rotação automática."
    )

    args = parser.parse_args()

    diretorio_roteiros = diretorio_roteiros_default
    diretorio_saida = args.output.resolve()
    arquivo_chaves = args.keys.resolve()
    proxy_token = args.proxy_token
    use_proxy = not args.no_proxy
    proxy_country = args.proxy_country

    diretorio_saida.mkdir(parents=True, exist_ok=True)

    logger.info(f"=== Iniciando Narração Automática V4 com Proxy ===")
    logger.info(f"Raiz do Projeto: {PROJECT_ROOT}")
    logger.info(f"Diretório de Roteiros: {diretorio_roteiros}")
    logger.info(f"Diretório de Saída: {diretorio_saida}")
    logger.info(f"Arquivo de Chaves: {arquivo_chaves}")
    logger.info(f"Proxy habilitado: {'SIM' if use_proxy else 'NÃO'}")
    if use_proxy and proxy_country:
        logger.info(f"País específico: {proxy_country}")

    # Verificações iniciais
    if not arquivo_chaves.is_file():
        logger.error(f"Arquivo de chaves não encontrado: {arquivo_chaves}")
        print(f"ERRO: Arquivo de chaves não encontrado: {arquivo_chaves}")
        return 1

    if not diretorio_roteiros.is_dir():
        logger.error(f"Diretório de roteiros não encontrado: {diretorio_roteiros}")
        print(f"ERRO: Diretório de roteiros não encontrado: {diretorio_roteiros}")
        return 1

    try:
        # --- Obtenção de Configurações ---
        api_config = config_manager.get_config("api") or {}
        voice_config = config_manager.get_config("voice") or {}
        processing_config = config_manager.get_config("processing") or {}
        
        base_url = api_config.get("base_url", "https://api.elevenlabs.io")
        max_retries = api_config.get("max_retries", 3)
        retry_delay = api_config.get("retry_delay", 5)
        voice_id = voice_config.get("voice_id", "21m00Tcm4TlvDq81kWAM")
        model_id = voice_config.get("model_id", "eleven_multilingual_v2")
        output_format = voice_config.get("output_format", "mp3_44100_128")
        
        tamanho_minimo_bloco = processing_config.get("tamanho_minimo_bloco", 900)
        tamanho_maximo_bloco = processing_config.get("tamanho_maximo_bloco", 1100)
        min_block_fallback = processing_config.get("min_block_size_on_fallback", 50)

        # --- Inicialização do Gerenciador de Chaves ---
        logger.info("🔑 Inicializando gerenciador de chaves API...")
        api_key_manager = APIKeyManager(keys_file_path=str(arquivo_chaves))
        
        working_count = len(api_key_manager.working_keys)
        blocked_count = len(api_key_manager.blocked_keys)
        
        if working_count == 0:
            logger.error(f"🚨 ERRO CRÍTICO: Todas as chaves estão bloqueadas!")
            print("❌ ERRO: Todas as chaves API estão bloqueadas pela ElevenLabs.")
            print("Possíveis soluções:")
            print("1. Aguardar algumas horas e tentar novamente")
            print("2. Verificar se está usando VPN/Proxy")
            print("3. Obter novas chaves API")
            print("4. Considerar upgrade para plano pago")
            return 1
            
        logger.info(f"✅ Chaves: {working_count} funcionais, {blocked_count} bloqueadas")

        # --- Inicialização do Gerenciador de Proxies (NOVO) ---
        proxy_manager = None
        if use_proxy:
            logger.info("🌐 Inicializando gerenciador de proxies Webshare.io...")
            try:
                proxy_manager = WebshareProxyManager(
                    api_token=proxy_token,
                    use_rotating_endpoint=True,
                    enable_fallback=True
                )
                
                # Testa conectividade
                test_results = proxy_manager.test_proxy_connection()
                if test_results.get("rotating_proxy", {}).get("success"):
                    proxy_ip = test_results["rotating_proxy"]["ip"]
                    logger.info(f"✅ Proxy funcionando - IP atual: {proxy_ip}")
                    
                    # Mostra status
                    status = proxy_manager.get_proxy_status()
                    total_proxies = status["proxy_config"]["total_proxies"]
                    countries = status["proxy_config"]["countries"]
                    logger.info(f"📊 {total_proxies} proxies em {len(countries)} países: {', '.join(countries[:5])}...")
                    
                else:
                    logger.warning("⚠️ Proxy não está funcionando corretamente, continuando sem proxy")
                    proxy_manager = None
                    
            except Exception as e:
                logger.error(f"❌ Erro ao inicializar proxy: {e}")
                logger.warning("Continuando sem proxy...")
                proxy_manager = None
        else:
            logger.info("🌐 Proxies desabilitados pelo usuário")

        # --- Inicialização do Cliente ElevenLabs V4 ---
        logger.info("🎵 Inicializando cliente ElevenLabs V4 (com proxy support)...")
        elevenlabs_api = ElevenLabsAPI(
            api_key_manager=api_key_manager,
            proxy_manager=proxy_manager,  # NOVO: pode ser None se proxies não funcionarem
            base_url=base_url,
            model_id=model_id,
            voice_id=voice_id,
            output_format=output_format,
            max_retries=max_retries,
            retry_delay=retry_delay,
            use_proxy_country=proxy_country  # NOVO: país específico se desejado
        )

        # --- Status Combinado ---
        combined_status = elevenlabs_api.get_combined_status()
        logger.info("📊 Status geral do sistema:")
        logger.info(f"   🔑 Chaves funcionais: {combined_status['api_keys']['working']}")
        
        if combined_status['proxies']:
            proxy_info = combined_status['proxies']['proxy_config']
            logger.info(f"   🌐 Proxies ativos: {proxy_info['total_proxies']}")
            logger.info(f"   📍 Rotação: {'País específico' if proxy_country else 'Automática'}")
        else:
            logger.info("   🌐 Proxies: Desabilitados")

        # --- Inicialização do Processador ---
        logger.info("📁 Inicializando processador de arquivos V4...")
        processador = ProcessadorArquivos(
            diretorio_roteiros=diretorio_roteiros,
            diretorio_saida=diretorio_saida,
            tamanho_minimo_bloco=tamanho_minimo_bloco,
            tamanho_maximo_bloco=tamanho_maximo_bloco,
            api_key_manager=api_key_manager,
            elevenlabs_api=elevenlabs_api,  # Agora com proxy support
            modelo_id=model_id,
            min_block_size_on_fallback=min_block_fallback
        )

        # --- Processamento ---
        logger.info("🚀 Iniciando processamento com proxy rotation + auto retry...")
        resultados = processador.processar_todos_arquivos()

        # --- Resumo Final ---
        sucessos = sum(1 for v in resultados.values() if isinstance(v, Path) and v.exists())
        erros = len(resultados) - sucessos

        logger.info(f"🏁 Processamento concluído!")
        logger.info(f"   Total: {len(resultados)} arquivos")
        logger.info(f"   ✅ Sucessos: {sucessos}")
        logger.info(f"   ❌ Erros: {erros}")

        # Status final detalhado
        final_status = elevenlabs_api.get_combined_status()
        final_keys = final_status['api_keys']
        logger.info(f"   🔑 Status final das chaves: {final_keys['working']} funcionais, {final_keys['blocked']} bloqueadas")
        
        if final_status['proxies']:
            proxy_stats = final_status['proxies']['statistics']
            logger.info(f"   🌐 Estatísticas de proxy: {proxy_stats['successful_requests']}/{proxy_stats['total_requests']} sucessos ({proxy_stats['success_rate_percent']}%)")

        # Relatório
        caminho_relatorio = diretorio_saida / 'relatorio_processamento_v4_proxy.txt'

        print(f"\n🎉 PROCESSAMENTO CONCLUÍDO!")
        print(f"📊 Total: {len(resultados)} arquivos processados")
        print(f"✅ Sucessos: {sucessos}")
        print(f"❌ Erros: {erros}")
        print(f"📈 Taxa de sucesso: {(sucessos/len(resultados)*100) if resultados else 0:.1f}%")
        
        if proxy_manager:
            proxy_stats = final_status['proxies']['statistics']
            print(f"🌐 Proxy performance: {proxy_stats['success_rate_percent']}% de sucessos")
        
        print(f"\n📁 Áudios gerados em: {diretorio_saida}")
        if caminho_relatorio.exists():
            print(f"📋 Relatório detalhado: {caminho_relatorio}")

        return 0

    except Exception as e:
        logger.exception("💥 Erro fatal durante a execução:")
        print(f"❌ ERRO FATAL: {str(e)}")
        return 1

# --- Bloco Principal ---
if __name__ == "__main__":
    sys.exit(main())
