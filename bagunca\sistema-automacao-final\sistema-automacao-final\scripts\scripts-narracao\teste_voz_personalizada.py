#!/usr/bin/env python3
"""
Teste da Configuração de Voz Personalizada
Testa se o voice ID e configurações personalizadas estão funcionando corretamente.
"""

import os
import sys
import json
import logging
from pathlib import Path

# Adiciona o diretório pai ao path para importar os módulos
sys.path.append(str(Path(__file__).parent))

from config_manager import ConfigManager
from api_key_manager_sequential import SequentialAPIKeyManager
from elevenlabs_api_atualizado import ElevenLabsAPI

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("TesteVozPersonalizada")

def main():
    """Função principal do teste."""
    print("🎤 TESTE DE VOZ PERSONALIZADA")
    print("=" * 50)
    
    try:
        # Inicializa o gerenciador de configurações
        config_manager = ConfigManager()
        
        # Obtém configurações
        voice_config = config_manager.get_config("voice") or {}
        api_config = config_manager.get_config("api") or {}
        
        # Exibe configurações atuais
        print(f"\n📋 CONFIGURAÇÕES ATUAIS:")
        print(f"   Voice ID: {voice_config.get('voice_id', 'N/A')}")
        print(f"   Voice Name: {voice_config.get('voice_name', 'N/A')}")
        print(f"   Model ID: {voice_config.get('model_id', 'N/A')}")
        print(f"   Output Format: {voice_config.get('output_format', 'N/A')}")
        
        voice_settings = voice_config.get('voice_settings', {})
        if voice_settings:
            print(f"\n🎛️  CONFIGURAÇÕES DE VOZ:")
            print(f"   Stability: {voice_settings.get('stability', 'N/A')}")
            print(f"   Similarity Boost: {voice_settings.get('similarity_boost', 'N/A')}")
            print(f"   Style: {voice_settings.get('style', 'N/A')}")
            print(f"   Use Speaker Boost: {voice_settings.get('use_speaker_boost', 'N/A')}")
        else:
            print(f"\n⚠️  Nenhuma configuração de voice_settings encontrada!")
        
        # Verifica se há chaves API
        keys_file = Path("api_keys.txt")
        if not keys_file.exists():
            print(f"\n❌ Arquivo de chaves API não encontrado: {keys_file}")
            return 1
        
        # Inicializa gerenciador de chaves
        api_key_manager = SequentialAPIKeyManager(str(keys_file))
        
        # Verifica status das chaves
        status = api_key_manager.get_status_summary()
        print(f"\n🔑 STATUS DAS CHAVES:")
        print(f"   Chaves ativas: {status['active_keys_count']}")
        print(f"   Caracteres disponíveis: {status['estimated_chars_available']:,}")
        
        if status['active_keys_count'] == 0:
            print(f"\n❌ Nenhuma chave API ativa disponível!")
            return 1
        
        # Inicializa cliente ElevenLabs
        voice_id = voice_config.get("voice_id", "21m00Tcm4TlvDq81kWAM")
        model_id = voice_config.get("model_id", "eleven_multilingual_v2")
        output_format = voice_config.get("output_format", "mp3_44100_128")
        base_url = api_config.get("base_url", "https://api.elevenlabs.io")
        
        elevenlabs_api = ElevenLabsAPI(
            api_key_manager=api_key_manager,
            base_url=base_url,
            model_id=model_id,
            voice_id=voice_id,
            output_format=output_format
        )
        
        # Texto de teste
        texto_teste = "Olá! Este é um teste da sua voz personalizada. As configurações incluem estabilidade alta, similaridade elevada e sem exagero de estilo."
        
        # Arquivo de saída
        output_dir = Path("teste_voz_personalizada")
        output_dir.mkdir(exist_ok=True)
        output_file = output_dir / "teste_voz_personalizada.mp3"
        
        print(f"\n🎯 INICIANDO TESTE DE GERAÇÃO DE ÁUDIO...")
        print(f"   Texto: {texto_teste[:50]}...")
        print(f"   Arquivo de saída: {output_file}")
        
        # Obtém uma chave API
        api_key, remaining_chars = api_key_manager.get_available_key(len(texto_teste))
        print(f"   Usando chave: {api_key[:8]}... ({remaining_chars} caracteres restantes)")
        
        # Gera áudio com configurações personalizadas
        resultado = elevenlabs_api.text_to_speech(
            text=texto_teste,
            output_path=str(output_file),
            api_key=api_key,
            voice_id=voice_id,
            model_id=model_id,
            output_format=output_format,
            voice_settings=voice_settings
        )
        
        if resultado and Path(resultado).exists():
            file_size = Path(resultado).stat().st_size
            print(f"\n✅ TESTE CONCLUÍDO COM SUCESSO!")
            print(f"   Arquivo gerado: {resultado}")
            print(f"   Tamanho: {file_size:,} bytes")
            
            # Deduz o uso da chave
            api_key_manager.deduct_usage(api_key, len(texto_teste))
            print(f"   Caracteres deduzidos: {len(texto_teste)}")
            
            print(f"\n🎵 Reproduza o arquivo para verificar a qualidade da voz!")
            
        else:
            print(f"\n❌ FALHA NA GERAÇÃO DO ÁUDIO!")
            return 1
            
    except Exception as e:
        logger.exception("Erro durante o teste:")
        print(f"\n❌ ERRO: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
