# 🎤 Sistema de Geração de Áudio

Sistema organizado para geração automática de áudio usando ElevenLabs API.

## 📁 Estrutura do Projeto

```
audio-generator/
├── scripts/                          # Scripts principais
│   ├── api_key_manager_sequential.py # Gerenciador de chaves API
│   ├── processador_sequencial.py     # Processador de arquivos
│   ├── arquivo_3_script_principal.py # Script principal
│   ├── utils_sequencial.py           # Utilitários
│   ├── narrador_sequencial_main.py   # Main alternativo
│   ├── elevenlabs_api_atualizado.py  # API ElevenLabs
│   └── config_manager.py             # Gerenciador de configuração
├── config/                           # Configurações
│   ├── config_narracao.json          # Configuração principal
│   ├── chaves-api-elevenlabs.txt     # Chaves API ativas
│   ├── chaves-usadas.txt             # Chaves esgotadas
│   └── processing_state.json         # Estado do processamento
├── roteiros_gerados/                 # Arquivos de entrada (roteiros)
├── narracao_sequencial/              # Arquivos de saída (áudios)
├── logs/                             # Logs do sistema
├── run_audio_generator.py            # Script principal simplificado
└── README.md                         # Este arquivo
```

## 🚀 Como Usar

### Execução Rápida
```bash
python run_audio_generator.py
```

### Execução Avançada
```bash
python scripts/arquivo_3_script_principal.py
```

## ⚙️ Configuração

1. **Chaves API**: Adicione suas chaves ElevenLabs em `config/chaves-api-elevenlabs.txt`
2. **Configuração**: Ajuste parâmetros em `config/config_narracao.json`
3. **Roteiros**: Coloque arquivos de texto em `roteiros_gerados/`

## 📋 Funcionalidades

- ✅ Processamento sequencial de roteiros
- ✅ Gerenciamento automático de chaves API
- ✅ Retomada de processamento interrompido
- ✅ Rotação de IP automática
- ✅ Logs detalhados
- ✅ Configuração flexível

## 🔧 Dependências

- Python 3.7+
- requests
- pathlib
- json

## 📝 Logs

Os logs são salvos em `logs/` e contêm informações detalhadas sobre o processamento.
