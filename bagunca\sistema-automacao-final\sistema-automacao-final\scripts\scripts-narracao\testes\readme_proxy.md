# 🎭 Narrador de Roteiros com Rotação de Proxy

Sistema automatizado para narração de roteiros usando ElevenLabs API com rotação inteligente de proxies WebShare.io para evitar bloqueios de IP.

## 🚀 Principais Funcionalidades

- ✅ **Rotação Automática de Proxies**: Troca de IP a cada 15 requisições (configurável)
- ✅ **Fallback Inteligente**: Requisições diretas se proxies falharem
- ✅ **Detecção de Bloqueios**: Identifica e remove proxies problemáticos automaticamente
- ✅ **Gerenciamento de Chaves**: Sistema robusto de controle de chaves API ElevenLabs
- ✅ **Logs Detalhados**: Monitoramento completo de performance e estatísticas
- ✅ **Configuração Flexível**: Controle total via arquivo JSON

## 📋 Pré-requisitos

### 1. Contas e Tokens
- **ElevenLabs**: Conta com chaves API (gratuitas ou pagas)
- **WebShare.io**: Conta com token API (10 proxies gratuitos disponíveis)

### 2. Dependências Python
```bash
pip install requests pydub
```

## 🛠️ Configuração Inicial

### 1. Obter Token WebShare.io

1. Acesse [WebShare.io](https://www.webshare.io/)
2. Crie uma conta gratuita (recebe 10 proxies)
3. Vá para [Dashboard > API Keys](https://dashboard.webshare.io/userapi/keys)
4. Gere um novo token de API
5. Copie o token gerado

### 2. Configurar o Sistema

1. **Arquivo de Configuração**: Edite `config/config_narracao_com_proxy.json`
   ```json
   {
     "proxy": {
       "enabled": true,
       "webshare_api_token": "SEU_TOKEN_WEBSHARE_AQUI",
       "rotation_threshold": 15,
       "enable_fallback": true
     }
   }
   ```

2. **Chaves ElevenLabs**: Coloque suas chaves em `config/chaves-api-elevenlabs.txt`
   ```
   sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   sk-yyyyyyyyyyyyyyyyyyyyyyyyyyy
   # Uma chave por linha
   ```

### 3. Estrutura de Diretórios
```
projeto/
├── config/
│   ├── config_narracao_com_proxy.json
│   └── chaves-api-elevenlabs.txt
├── roteiros_gerados/          # Arquivos .txt para narrar
├── narracao/                  # Áudios gerados
├── logs/                      # Logs do sistema
└── scripts/
    ├── proxy_manager.py
    ├── elevenlabs_api_with_proxy.py
    └── narrador_com_proxy.py
```

## 🎯 Como Usar

### Uso Básico
```bash
python narrador_com_proxy.py
```

### Opções Avançadas
```bash
# Desabilitar proxies (usar apenas requisições diretas)
python narrador_com_proxy.py --no-proxy

# Testar todos os proxies antes de começar
python narrador_com_proxy.py --test-proxies

# Configurar threshold de rotação personalizado
python narrador_com_proxy.py --proxy-threshold 10

# Diretório de saída personalizado
python narrador_com_proxy.py --output /caminho/para/audios

# Arquivo de chaves personalizado
python narrador_com_proxy.py --keys /caminho/para/chaves.txt
```

## ⚙️ Configurações Detalhadas

### Configurações de Proxy (`config.json`)

```json
{
  "proxy": {
    "enabled": true,                    // Habilitar/desabilitar proxies
    "webshare_api_token": "token",      // Token da API WebShare.io
    "rotation_threshold": 15,           // Requisições antes de trocar proxy
    "max_retries_per_proxy": 3,         // Tentativas por proxy antes de marcar como problemático
    "connection_timeout": 30,           // Timeout de conexão em segundos
    "enable_fallback": true,            // Permitir requisições diretas se proxies falharem
    "test_proxies_on_startup": false,   // Testar proxies ao iniciar
    "refresh_interval_hours": 24        // Intervalo para renovar lista de proxies
  }
}
```

### Configurações de Processamento

```json
{
  "processing": {
    "tamanho_minimo_bloco": 900,        // Tamanho mínimo de bloco em caracteres
    "tamanho_maximo_bloco": 1100,       // Tamanho máximo de bloco em caracteres
    "min_block_size_on_fallback": 50,   // Tamanho mínimo para blocos pequenos
    "default_language": "pt-BR"         // Idioma padrão
  }
}
```

## 📊 Monitoramento e Logs

### Logs do Sistema
- **Localização**: `logs/narracao_com_proxy.log`
- **Informações**: Rotações de proxy, falhas, estatísticas, etc.

### Relatório de Processamento
- **Localização**: `narracao/relatorio_processamento_v3.txt`
- **Conteúdo**: Resumo detalhado de sucessos/falhas por arquivo

### Estatísticas em Tempo Real
Durante o processamento, o sistema exibe:
- Status dos proxies (funcionais/bloqueados)
- Número de rotações realizadas
- Taxa de sucesso por proxy
- Requisições via proxy vs diretas

## 🔧 Solução de Problemas

### Problema: "Token WebShare.io inválido"
**Soluções**:
1. Verifique se o token está correto no arquivo de configuração
2. Certifique-se de que a conta WebShare.io está ativa
3. Use `--no-proxy` para desabilitar proxies temporariamente

### Problema: "Todos os proxies falharam"
**Soluções**:
1. Execute com `--test-proxies` para identificar proxies problemáticos
2. Verifique sua conexão de internet
3. Se `enable_fallback: true`, o sistema continuará com requisições diretas

### Problema: "Chaves API bloqueadas"
**Soluções**:
1. Aguarde algumas horas antes de tentar novamente
2. Use uma VPN ou proxy para mudar seu IP
3. Adicione novas chaves ao arquivo de chaves

### Problema: Performance lenta
**Soluções**:
1. Reduza `rotation_threshold` para rotacionar proxies com mais frequência
2. Ative `test_proxies_on_startup` para filtrar proxies lentos
3. Aumente `connection_timeout` se conexões estão falhando

## 📈 Otimizações Recomendadas

### Para Contas Gratuitas WebShare.io (10 proxies)
```json
{
  "proxy": {
    "rotation_threshold": 10,           // Rotação mais frequente
    "max_retries_per_proxy": 2,         // Menos tentativas por proxy
    "test_proxies_on_startup": true     // Filtra proxies problemáticos
  }
}
```

### Para Contas Premium WebShare.io (mais proxies)
```json
{
  "proxy": {
    "rotation_threshold": 20,           // Pode usar proxies por mais tempo
    "max_retries_per_proxy": 5,         // Mais tolerante a falhas temporárias
    "test_proxies_on_startup": false    // Não precisa testar se tem muitos
  }
}
```

## 🔄 Fluxo de Rotação de Proxies

1. **Início**: Sistema carrega lista de proxies da WebShare.io
2. **Uso**: Faz requisições com proxy atual
3. **Contagem**: Incrementa contador a cada requisição
4. **Rotação**: Ao atingir threshold, troca para próximo proxy
5. **Detecção de Falha**: Se proxy falha, marca como problemático
6. **Fallback**: Se todos proxies falharem, usa requisição direta (se habilitado)

## 📞 Suporte

- **Logs**: Sempre verifique `logs/narracao_com_proxy.log` para erros detalhados
- **Estatísticas**: Use as estatísticas exibidas para otimizar configurações
- **Teste**: Execute com `--test-proxies` para validar setup

## 🎯 Exemplo de Execução Bem-Sucedida

```
===============================================================================
🎭 NARRADOR DE ROTEIROS COM ROTAÇÃO DE PROXY
   Sistema automatizado de narração usando ElevenLabs + WebShare.io
===============================================================================

🔧 Configurações carregadas:
   - Rotação de proxy: ✅ Habilitada
   - Threshold de rotação: 15 requisições
   - Fallback direto: ✅ Habilitado

✅ Chaves API: 5 funcionais, 2 bloqueadas
✅ Proxies: 8 disponíveis

🚀 Iniciando processamento de arquivos...

📄 Processando arquivo 1/3: roteiro_capitulo1.txt
🔄 Rotação de proxy: 1.2.3.4 → 5.6.7.8 (Rotação #1)
✅ Sucesso: roteiro_capitulo1.txt -> roteiro_capitulo1.mp3

===============================================================================
📊 PROCESSAMENTO CONCLUÍDO
===============================================================================
⏱️  Tempo total: 45.2 segundos
📁 Arquivos processados: 3
✅ Sucessos: 3
❌ Erros: 0

🌐 Estatísticas de Proxy:
   Total de requisições: 47
   Via proxy: 45
   Diretas (fallback): 2
   Falhas de proxy: 3
   Taxa de uso de proxy: 95.7%

📂 Áudios gerados em: /projeto/narracao
📋 Relatório detalhado: /projeto/narracao/relatorio_processamento_v3.txt

🎉 Processamento finalizado com sucesso!
```

Este sistema oferece uma solução robusta para evitar bloqueios de IP ao usar APIs de TTS com rotação inteligente de proxies!