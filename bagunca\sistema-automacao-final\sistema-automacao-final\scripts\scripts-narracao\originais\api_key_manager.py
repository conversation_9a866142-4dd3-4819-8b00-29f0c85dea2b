'''
Módulo de Gerenciamento de Chaves API do ElevenLabs (Versão Melhorada)

Este módulo é responsável por:
1. <PERSON>egar as chaves API do arquivo de chaves.
2. Consultar a API da ElevenLabs para obter o status real de uso de caracteres de cada chave.
3. Selecionar a chave API mais adequada com base nos caracteres necessários e na disponibilidade real.
4. Sincronizar o status das chaves periodicamente ou sob demanda.
'''

import os
import json
import requests
import time
import logging # Adicionado
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__) # Adicionado

class APIKeyManager:
    '''
    Gerenciador de chaves API do ElevenLabs com consulta de status real via API.
    Controla o uso de múltiplas chaves API, selecionando a melhor com base na disponibilidade.
    '''

    # Corrigido: Indentação consistente de 4 espaços para membros da classe
    ELEVENLABS_API_ENDPOINT = "https://api.elevenlabs.io/v1/user/subscription"

    def __init__(self, keys_file_path: str):
        '''
        Inicializa o gerenciador de chaves API.

        Args:
            keys_file_path: Caminho para o arquivo contendo as chaves API.
        '''
        self.keys_file_path = keys_file_path
        self.api_keys: List[str] = []
        # Estrutura para armazenar o status sincronizado: {key: {"limit": int, "count": int, "remaining": int, "last_sync": float}}
        self.key_status: Dict[str, Dict] = {}
        self._load_api_keys()
        # Realiza uma sincronização inicial
        print("Realizando sincronização inicial do status das chaves...")
        self.sync_all_keys_usage()

    def _load_api_keys(self) -> None:
        '''Carrega as chaves API do arquivo.'''
        try:
            if not os.path.exists(self.keys_file_path):
                raise FileNotFoundError(f"Arquivo de chaves não encontrado: {self.keys_file_path}")
            with open(self.keys_file_path, 'r') as file:
                self.api_keys = [line.strip() for line in file.readlines() if line.strip()]
            if not self.api_keys:
                raise ValueError("Nenhuma chave API encontrada no arquivo.")
            print(f"Carregadas {len(self.api_keys)} chaves API.")
            # Inicializa o status para chaves recém-carregadas (será preenchido na sincronização)
            for key in self.api_keys:
                if key not in self.key_status:
                    self.key_status[key] = {"limit": 0, "count": 0, "remaining": 0, "last_sync": 0}
            # Remove chaves do status que não estão mais no arquivo
            keys_in_status = list(self.key_status.keys())
            for key in keys_in_status:
                if key not in self.api_keys:
                    del self.key_status[key]

        except Exception as e:
            print(f"Erro crítico ao carregar chaves API: {str(e)}")
            raise

    def _fetch_key_subscription_info(self, api_key: str) -> Optional[Dict]:
        '''
        Consulta a API da ElevenLabs para obter informações da assinatura de uma chave.

        Args:
            api_key: A chave API a ser consultada.

        Returns:
            Dicionário com informações da assinatura ou None em caso de erro.
        '''
        headers = {"xi-api-key": api_key}
        try:
            response = requests.get(self.ELEVENLABS_API_ENDPOINT, headers=headers)
            response.raise_for_status()  # Levanta exceção para erros HTTP (4xx ou 5xx)
            data = response.json()
            # Verifica se os campos esperados estão presentes
            if "character_limit" in data and "character_count" in data:
                return {
                    "limit": data["character_limit"],
                    "count": data["character_count"],
                    "remaining": data["character_limit"] - data["character_count"]
                }
            else:
                print(f"Aviso: Resposta da API para a chave {api_key[:8]}... não contém os campos esperados.")
                return None
        except requests.exceptions.RequestException as e:
            print(f"Erro ao consultar API da ElevenLabs para a chave {api_key[:8]}...: {str(e)}")
            # Considerar a chave como indisponível temporariamente em caso de erro de rede/API?
            # Por enquanto, apenas retorna None e mantém o status antigo.
            return None
        except Exception as e:
            print(f"Erro inesperado ao processar resposta da API para a chave {api_key[:8]}...: {str(e)}")
            return None

    def sync_all_keys_usage(self, force_sync: bool = False, min_sync_interval_seconds: int = 300) -> None:
        '''
        Sincroniza o status de uso de todas as chaves API consultando a ElevenLabs.

        Args:
            force_sync: Se True, força a sincronização de todas as chaves, ignorando o intervalo mínimo.
            min_sync_interval_seconds: Intervalo mínimo em segundos para resincronizar uma chave (padrão: 5 minutos).
        '''
        print("Iniciando sincronização do status das chaves...")
        current_time = time.time()
        keys_synced = 0
        for key in self.api_keys:
            last_sync = self.key_status.get(key, {}).get("last_sync", 0)
            # Sincroniza se forçado ou se o intervalo mínimo passou
            if force_sync or (current_time - last_sync > min_sync_interval_seconds):
                print(f"Sincronizando chave: {key[:8]}...", end=" ")
                subscription_info = self._fetch_key_subscription_info(key)
                if subscription_info:
                    self.key_status[key] = {
                        **subscription_info, # limit, count, remaining
                        "last_sync": current_time
                    }
                    print(f"OK (Restantes: {self.key_status[key]['remaining']})")
                    keys_synced += 1
                else:
                    # Mantém o status antigo, mas atualiza o tempo de sync para evitar tentativas repetidas muito rápidas
                    # Ou marca como inválida/erro?
                    self.key_status[key]["last_sync"] = current_time
                    print("Falha na sincronização.")
                # Pequena pausa para evitar sobrecarregar a API (opcional)
                time.sleep(0.5)
            # else: # Descomente para depuração
            #     print(f"Chave {key[:8]}... já sincronizada recentemente.")

        print(f"Sincronização concluída. {keys_synced} chaves atualizadas.")

    def get_available_key(self, required_chars: int) -> Tuple[str, int]:
        '''
        Retorna a melhor chave API disponível com base nos caracteres necessários e no status real.
        Prioriza chaves com caracteres suficientes. Se nenhuma tiver, retorna a com mais caracteres restantes (fallback).

        Args:
            required_chars: Número de caracteres necessários para a próxima operação.

        Returns:
            Tupla contendo a chave API selecionada e seus caracteres restantes reais.

        Raises:
            ValueError: Se nenhuma chave utilizável for encontrada (sem caracteres restantes ou todas inválidas).
        '''
        if not self.api_keys:
            raise ValueError("Nenhuma chave API configurada.")

        # Garante que os dados estejam razoavelmente atualizados antes de decidir
        # Poderia chamar sync_all_keys_usage() aqui, mas o relatório sugere chamar no processador.
        # Vamos confiar que a sincronização foi chamada recentemente.

        best_key_found = None
        max_remaining_fallback = -1  # Usado para o fallback
        best_key_fallback = None

        # Ordena as chaves pelo maior número de caracteres restantes (opcional, mas pode ser útil)
        sorted_keys = sorted(self.api_keys, key=lambda k: self.key_status.get(k, {}).get("remaining", 0), reverse=True)

        for key in sorted_keys:
            status = self.key_status.get(key)
            # Verifica se temos status válido para a chave
            if status and status.get("last_sync", 0) > 0: # Garante que a chave foi sincronizada pelo menos uma vez
                remaining = status.get("remaining", 0)
                
                # 1. Verifica se a chave tem caracteres suficientes
                if remaining >= required_chars:
                    print(f"Chave encontrada com caracteres suficientes: {key[:8]}... (Restantes: {remaining})")
                    best_key_found = key
                    break # Encontrou a melhor opção

                # 2. Se não tem suficiente, guarda como candidata para fallback
                if remaining > max_remaining_fallback:
                    max_remaining_fallback = remaining
                    best_key_fallback = key
            # else: # Descomente para depuração
            #     print(f"Chave {key[:8]}... sem status sincronizado válido, ignorando.")

        # Se encontrou uma chave com caracteres suficientes
        if best_key_found:
            return best_key_found, self.key_status[best_key_found]["remaining"]

        # Se não encontrou suficiente, mas encontrou uma chave de fallback com *algum* caractere
        if best_key_fallback is not None and max_remaining_fallback > 0:
            print(f"Nenhuma chave com {required_chars} caracteres. Usando fallback: {best_key_fallback[:8]}... (Restantes: {max_remaining_fallback})")
            return best_key_fallback, max_remaining_fallback

        # Se chegou aqui, nenhuma chave tem caracteres restantes ou todas falharam na sincronização
        raise ValueError("Nenhuma chave API disponível com caracteres restantes ou todas as chaves falharam na sincronização.")


    def deduct_usage(self, api_key: str, characters_used: int):
        '''Deduz localmente o uso de caracteres do status da chave.'''
        if api_key in self.key_status:
            current_remaining = self.key_status[api_key].get("remaining", 0)
            # Garante que o saldo local não fique negativo
            new_remaining = max(0, current_remaining - characters_used)
            self.key_status[api_key]["remaining"] = new_remaining
            # Opcional: Atualizar a contagem local também
            # current_count = self.key_status[api_key].get("count", 0)
            # self.key_status[api_key]["count"] = current_count + characters_used
            logger.info(f"Uso local deduzido: {characters_used} caracteres da chave {api_key[:8]}... Saldo local restante: {new_remaining}")
        else:
            logger.warning(f"Tentativa de deduzir uso de chave não rastreada: {api_key}")

    def get_status_summary(self) -> Dict[str, Dict]:
        '''
        Retorna um resumo do status sincronizado de todas as chaves.

        Returns:
            Dicionário com o status de cada chave.
        '''
        summary = {}
        for key in self.api_keys:
            status = self.key_status.get(key, {})
            summary[key] = {
                "limit": status.get("limit", "N/A"),
                "count": status.get("count", "N/A"),
                "remaining": status.get("remaining", "N/A"),
                "last_sync_timestamp": status.get("last_sync", 0),
                "last_sync_readable": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(status.get("last_sync", 0))) if status.get("last_sync", 0) > 0 else "Nunca"
            }
        return summary

# Exemplo de uso (requer um arquivo 'chaves-api-elevenlabs.txt' com chaves válidas)
if __name__ == "__main__":
    keys_file = "chaves-api-elevenlabs.txt"
    # Cria o arquivo se não existir para teste
    if not os.path.exists(keys_file):
        print(f"Criando arquivo de exemplo {keys_file}. Adicione suas chaves API nele.")
        with open(keys_file, 'w') as f:
            f.write("# Adicione suas chaves API da ElevenLabs aqui, uma por linha\n")
            f.write("sua_chave_api_1\n")
            f.write("sua_chave_api_2\n")

    try:
        key_manager = APIKeyManager(keys_file)

        print("\n--- Status Inicial das Chaves ---")
        print(json.dumps(key_manager.get_status_summary(), indent=2))

        # Tenta obter chave para 5000 caracteres
        print("\n--- Tentando obter chave para 5000 caracteres ---")
        try:
            key1, remaining1 = key_manager.get_available_key(required_chars=5000)
            print(f"Chave obtida: {key1[:8]}... com {remaining1} caracteres restantes.")
        except ValueError as e:
            print(f"Erro ao obter chave para 5000: {e}")

        # Tenta obter chave para 50000 caracteres (provavelmente usará fallback)
        print("\n--- Tentando obter chave para 50000 caracteres ---")
        try:
            key2, remaining2 = key_manager.get_available_key(required_chars=50000)
            print(f"Chave obtida: {key2[:8]}... com {remaining2} caracteres restantes (Fallback?).")
        except ValueError as e:
            print(f"Erro ao obter chave para 50000: {e}")

        # Força resincronização
        print("\n--- Forçando resincronização --- ")
        key_manager.sync_all_keys_usage(force_sync=True)
        print("\n--- Status Após Resincronização ---")
        print(json.dumps(key_manager.get_status_summary(), indent=2))

    except (FileNotFoundError, ValueError) as e:
        print(f"Erro na inicialização: {e}")
    except Exception as e:
        print(f"Erro inesperado: {e}")

