"""
Teste completo de TTS com proxy usando as chaves ressuscitadas
"""

import sys
from pathlib import Path
import time
import json

sys.path.append(str(Path(__file__).parent))

from api_key_manager import APIKeyManager
from proxy_manager import <PERSON>shareProxyManager

def test_tts_with_proxy(api_key, proxy_manager, voice_id):
    """Faz teste completo de TTS com proxy"""
    import requests
    
    # Configuração da requisição TTS
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
    
    headers = {
        "xi-api-key": api_key,
        "Content-Type": "application/json",
        "Accept": "audio/mpeg"
    }
    
    payload = {
        "text": "Olá! Este é um teste com proxy funcionando.",
        "model_id": "eleven_multilingual_v2"
    }
    
    params = {"output_format": "mp3_44100_128"}
    
    try:
        # Usa proxy para fazer TTS
        response = proxy_manager.make_request_with_proxy(
            method="POST",
            url=url,
            headers=headers,
            json=payload,
            params=params,
            max_retries=1
        )
        
        if response.status_code == 200:
            # Sucesso! Salva o arquivo de teste
            output_path = f"teste_tts_proxy_{api_key[:8]}.mp3"
            with open(output_path, "wb") as f:
                f.write(response.content)
            
            file_size = len(response.content)
            return True, f"Sucesso! Arquivo: {output_path} ({file_size} bytes)"
        else:
            try:
                error_data = response.json()
                error_msg = error_data.get("detail", {}).get("message", "Erro desconhecido")
                return False, f"HTTP {response.status_code}: {error_msg}"
            except:
                return False, f"HTTP {response.status_code}: {response.text[:100]}"
            
    except Exception as e:
        return False, f"Erro: {str(e)}"

def main():
    print("🎵 TESTE COMPLETO: TTS COM PROXY")
    print("="*50)
    
    keys_file = "chaves-api-elevenlabs.txt"
    webshare_token = "mt643rsq556yqggigaa3emmmjmdycc2h6868bi7b"
    voice_id = "9BWtsMINqrJLrRacOk9x"  # Aria
    
    try:
        # 1. Inicializa managers
        print("1️⃣ Inicializando managers...")
        key_manager = APIKeyManager(keys_file)
        proxy_manager = WebshareProxyManager(
            api_token=webshare_token,
            use_rotating_endpoint=True,
            enable_fallback=True
        )
        
        # 2. Testa as 5 chaves "ressuscitadas"
        print("2️⃣ Testando TTS com as chaves ressuscitadas...")
        
        ressuscitated_keys = [
            "sk_af71d" + key_manager.api_keys[0][8:],  # Reconstrói a chave completa
            "sk_47125" + key_manager.api_keys[1][8:],
            "sk_d01b2" + key_manager.api_keys[2][8:],
            "sk_30132" + key_manager.api_keys[3][8:],
            "sk_cb880" + key_manager.api_keys[4][8:]
        ]
        
        # Ou melhor ainda, usa as primeiras 5 da lista
        test_keys = key_manager.api_keys[:5]
        
        working_tts_keys = []
        
        for i, api_key in enumerate(test_keys, 1):
            key_preview = f"{api_key[:8]}..."
            print(f"\n   🧪 Teste {i}/5: {key_preview}")
            
            # Primeiro testa sem proxy
            print(f"      🌐 TTS sem proxy...", end=" ")
            success_no_proxy, result_no_proxy = test_tts_without_proxy(api_key, voice_id)
            if success_no_proxy:
                print("✅ FUNCIONOU!")
            else:
                print(f"❌ {result_no_proxy[:50]}...")
            
            # Agora testa com proxy
            print(f"      🔄 TTS com proxy...", end=" ")
            success_with_proxy, result_with_proxy = test_tts_with_proxy(api_key, proxy_manager, voice_id)
            if success_with_proxy:
                print("✅ FUNCIONOU!")
                working_tts_keys.append(api_key)
                print(f"         {result_with_proxy}")
            else:
                print(f"❌ {result_with_proxy[:50]}...")
            
            # Pausa entre testes
            time.sleep(3)
        
        # 3. Resultado final
        print(f"\n3️⃣ RESULTADO FINAL:")
        print(f"   ✅ Chaves funcionando para TTS: {len(working_tts_keys)}/5")
        
        if working_tts_keys:
            print(f"\n🎉 SUCESSO TOTAL! TTS funcionando com proxy!")
            print(f"📁 Arquivos de teste gerados:")
            
            for key in working_tts_keys:
                test_file = f"teste_tts_proxy_{key[:8]}.mp3"
                if Path(test_file).exists():
                    size_kb = Path(test_file).stat().st_size / 1024
                    print(f"   • {test_file} ({size_kb:.1f} KB)")
            
            # Salva configuração de sucesso
            success_config = {
                "working_keys": [key[:8] + "..." for key in working_tts_keys],
                "voice_id": voice_id,
                "voice_name": "Aria",
                "proxy_enabled": True,
                "status": "TTS funcionando com proxy",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            with open("tts_success_config.json", "w") as f:
                json.dump(success_config, f, indent=2)
            
            print(f"\n💾 Configuração de sucesso salva em: tts_success_config.json")
            
            print(f"\n🚀 PRÓXIMOS PASSOS:")
            print(f"   1. ✅ Sistema funcionando - pode processar roteiros!")
            print(f"   2. ✅ Usar sempre proxy nas requisições")
            print(f"   3. ✅ Atualizar config principal com voice_id: {voice_id}")
            print(f"   4. ✅ Executar sistema completo de narração")
            
        else:
            print(f"\n😞 Nenhuma chave funcionou para TTS ainda.")
            print(f"💡 Opções:")
            print(f"   - Aguardar mais tempo (bloqueio temporário)")
            print(f"   - Tentar com diferentes países de proxy")
            print(f"   - Criar novas contas com proxy desde o início")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ ERRO: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_tts_without_proxy(api_key, voice_id):
    """Teste TTS sem proxy para comparação"""
    import requests
    
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
    
    headers = {
        "xi-api-key": api_key,
        "Content-Type": "application/json",
        "Accept": "audio/mpeg"
    }
    
    payload = {
        "text": "Teste sem proxy.",
        "model_id": "eleven_multilingual_v2"
    }
    
    params = {"output_format": "mp3_44100_128"}
    
    try:
        response = requests.post(
            url,
            headers=headers,
            json=payload,
            params=params,
            timeout=15
        )
        
        if response.status_code == 200:
            return True, f"Sucesso ({len(response.content)} bytes)"
        else:
            try:
                error_data = response.json()
                error_msg = error_data.get("detail", {}).get("message", "Erro")
                return False, f"HTTP {response.status_code}: {error_msg}"
            except:
                return False, f"HTTP {response.status_code}"
            
    except Exception as e:
        return False, str(e)

if __name__ == "__main__":
    sys.exit(main())
