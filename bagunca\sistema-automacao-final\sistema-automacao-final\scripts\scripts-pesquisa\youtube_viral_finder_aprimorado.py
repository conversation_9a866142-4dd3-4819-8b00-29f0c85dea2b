#!/usr/bin/env python3
"""
Script aprimorado para busca de vídeos virais do YouTube no nicho de histórias de vingança.

Este script implementa uma abordagem em duas fases:
1. Coleta ampla de 500-1000 vídeos que se encaixam no nicho de histórias de vingança
2. Filtragem refinada para selecionar os 100 vídeos mais virais com duração mínima de 20 minutos

Critérios específicos:
- Foco no nicho de histórias de vingança (como nos canais de referência)
- Duração mínima de 20 minutos
- Mínimo de 50.000 visualizações
- Publicados nos últimos 3 meses
"""

import os
import sys
import json
import pandas as pd
import numpy as np
import logging
import time
import gc
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/pesquisa.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("youtube_viral_finder")

class YouTubeViralFinderAprimorado:
    """
    Classe principal aprimorada para busca de vídeos virais do YouTube no nicho de histórias de vingança.
    """
    
    def __init__(self, reference_data_path: str, api_keys_path: str, project_root: str = "."):
        """
        Inicializa o buscador de vídeos virais aprimorado.

        Args:
            reference_data_path: Caminho relativo à raiz do projeto para o arquivo CSV com dados de referência (ex: "youtube_data_2025-04-25.csv")
            api_keys_path: Caminho relativo à raiz do projeto para o arquivo com chaves API (ex: "config/chaves-api-youtube.txt")
            project_root: Caminho absoluto para a raiz do projeto (ex: "/home/<USER>/sistema-automacao-total")
        """
        # Construir caminhos absolutos baseados na raiz do projeto
        self.reference_data_path = os.path.join(project_root, reference_data_path)
        self.api_keys_path = os.path.join(project_root, api_keys_path)
        self.temp_csv_path = f"/tmp/temp_videos_pesquisa_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv" # Usar /tmp para temporários
        self.final_results_path = os.path.join(project_root, "top_100_videos_virais.csv") # Saída fixa na raiz

        # Diretório de saída não é mais necessário aqui
        # self.output_dir = output_dir
        # if not os.path.exists(output_dir):
        #     os.makedirs(output_dir)

        # Carregar chaves API
        self.api_keys = self._load_api_keys()

        # Importar módulos personalizados (assumindo que estão no mesmo diretório ou PYTHONPATH está configurado)
        # sys.path.append(os.path.dirname(os.path.abspath(__file__))) # Evitar depender de __file__
        try:
            from youtube_search_algorithm import YouTubeSearchAlgorithm
            from youtube_metrics import YouTubeMetricsCalculator
            from youtube_channel_finder import YouTubeChannelFinder
            from youtube_title_patterns import YouTubeTitlePatternExtractor
            from youtube_bulk_collector import YouTubeBulkCollector
            from youtube_revenge_filter import YouTubeRevengeFilter
        except ImportError as e:
            logger.error(f"Erro ao importar módulos: {e}. Verifique se os scripts estão no PYTHONPATH.")
            raise

        # Inicializar componentes
        self.search_algorithm = YouTubeSearchAlgorithm(self.api_keys)
        self.metrics_calculator = YouTubeMetricsCalculator() # Não precisa de caminho
        self.channel_finder = YouTubeChannelFinder(self.search_algorithm, self.reference_data_path)
        self.pattern_extractor = YouTubeTitlePatternExtractor(self.reference_data_path)
        self.bulk_collector = YouTubeBulkCollector(
            self.search_algorithm,
            self.channel_finder,
            self.pattern_extractor
            # output_dir foi removido do construtor do BulkCollector
        )
        self.revenge_filter = YouTubeRevengeFilter(self.reference_data_path, self.metrics_calculator)
        
        logger.info(f"YouTubeViralFinderAprimorado inicializado com {len(self.api_keys)} chaves API")
    
    def _load_api_keys(self) -> List[str]:
        """
        Carrega as chaves API do YouTube do arquivo.
        
        Returns:
            Lista de chaves API
        """
        try:
            with open(self.api_keys_path, 'r') as f:
                api_keys = [line.strip() for line in f if line.strip()]
            
            logger.info(f"Carregadas {len(api_keys)} chaves API do YouTube")
            return api_keys
        except Exception as e:
            logger.error(f"Erro ao carregar chaves API: {str(e)}")
            raise
    
    def collect_videos(self) -> None:
        """
        Fase 1: Coleta ampla de vídeos (500-1000) que se encaixam no nicho de histórias de vingança.
        """
        logger.info("Iniciando fase 1: Coleta ampla de vídeos...")
        
        # Usar o coletor em massa para coletar vídeos
        self.bulk_collector.run(self.temp_csv_path)
        
        # Forçar coleta de lixo após operação intensiva
        gc.collect()
        
        logger.info(f"Fase 1 concluída: dados salvos em {self.temp_csv_path}")
    
    def filter_and_select_videos(self, min_views: int = 50000, min_duration_minutes: int = 20,
                               max_age_months: int = 3, target_count: int = 100) -> pd.DataFrame:
        """
        Fase 2: Filtragem refinada para selecionar os 100 vídeos mais virais no nicho de histórias de vingança.
        
        Args:
            min_views: Número mínimo de visualizações
            min_duration_minutes: Duração mínima em minutos
            max_age_months: Idade máxima em meses
            target_count: Número alvo de vídeos
            
        Returns:
            DataFrame com os vídeos selecionados
        """
        logger.info(f"Iniciando fase 2: Filtragem refinada (min_views={min_views}, min_duration={min_duration_minutes}, max_age={max_age_months})...")
        
        # Usar o filtro de nicho de vingança para processar os vídeos
        result_df = self.revenge_filter.process_videos_csv(
            self.temp_csv_path,
            self.final_results_path,
            min_duration_minutes=min_duration_minutes,
            min_views=min_views,
            max_age_months=max_age_months,
            target_count=target_count
        )
        
        # Forçar coleta de lixo após operação intensiva
        gc.collect()
        
        logger.info(f"Fase 2 concluída: {len(result_df)} vídeos selecionados")
        
        return result_df
    
    def run(self, min_views: int = 50000, min_duration_minutes: int = 20,
          max_age_months: int = 3, target_count: int = 100) -> pd.DataFrame:
        """
        Executa o processo completo de busca, filtragem e seleção de vídeos virais.
        
        Args:
            min_views: Número mínimo de visualizações
            min_duration_minutes: Duração mínima em minutos
            max_age_months: Idade máxima em meses
            target_count: Número alvo de vídeos
            
        Returns:
            DataFrame com os vídeos selecionados
        """
        try:
            # Fase 1: Coleta ampla de vídeos
            self.collect_videos()
            
            # Fase 2: Filtragem refinada
            result_df = self.filter_and_select_videos(
                min_views=min_views,
                min_duration_minutes=min_duration_minutes,
                max_age_months=max_age_months,
                target_count=target_count
            )
            
            # Verificar se atingimos o número alvo
            if len(result_df) < target_count:
                logger.warning(f"Não foi possível atingir o número alvo de {target_count} vídeos. "
                              f"Apenas {len(result_df)} vídeos atendem aos critérios.")
            
            return result_df
            
        except Exception as e:
            logger.error(f"Erro ao executar busca de vídeos virais: {str(e)}")
            raise

def main():
    """Função principal para execução do script."""
    # Configurações relativas à raiz do projeto
    PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")) # Assumindo a raiz aqui, idealmente passada como argumento
    REFERENCE_DATA_PATH_REL = "youtube_data_2025-04-25.csv" # Relativo à raiz
    API_KEYS_PATH_REL = "config/chaves-api-youtube.txt" # Relativo à raiz

    # Criar diretórios necessários (logs) se não existirem
    log_dir = os.path.join(PROJECT_ROOT, "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
        logger.info(f"Diretório de logs criado em: {log_dir}")

    # Parâmetros de filtragem
    MIN_VIEWS = 50000
    MIN_DURATION_MINUTES = 20
    MAX_AGE_MONTHS = 3
    TARGET_COUNT = 100

    # Diretório de saída não é mais criado aqui
    # if not os.path.exists(OUTPUT_DIR):
    #     os.makedirs(OUTPUT_DIR)

    try:
        # Inicializar o buscador de vídeos virais passando a raiz e caminhos relativos
        finder = YouTubeViralFinderAprimorado(
            reference_data_path=REFERENCE_DATA_PATH_REL,
            api_keys_path=API_KEYS_PATH_REL,
            project_root=PROJECT_ROOT
        )

        # Executar busca de vídeos virais
        result_df = finder.run(
            min_views=MIN_VIEWS,
            min_duration_minutes=MIN_DURATION_MINUTES,
            max_age_months=MAX_AGE_MONTHS,
            target_count=TARGET_COUNT
        )

        # Exibir resultados
        print(f"\nEncontrados {len(result_df)} vídeos virais no nicho de histórias de vingança.")

        # Mostrar caminho do arquivo de resultados
        print(f"\nResultados salvos em: {finder.final_results_path}")
        print(f"Dados temporários (se mantidos) em: {finder.temp_csv_path}") # Informar sobre temp

        # Mostrar estatísticas
        if not result_df.empty:
            print("\nEstatísticas dos vídeos selecionados:")
            # Garantir que as colunas são numéricas antes de calcular a média
            result_df['view_count'] = pd.to_numeric(result_df['view_count'], errors='coerce')
            result_df['duration_minutes'] = pd.to_numeric(result_df['duration_minutes'], errors='coerce')
            result_df['relevance_score'] = pd.to_numeric(result_df['relevance_score'], errors='coerce')

            print(f"Média de visualizações: {result_df['view_count'].mean():.2f}")
            print(f"Média de duração (minutos): {result_df['duration_minutes'].mean():.2f}")
            print(f"Média de relevância para o nicho: {result_df['relevance_score'].mean():.2f}")

    except Exception as e:
        logger.error(f"Erro na execução principal: {str(e)}")
        print(f"Erro: {str(e)}")

if __name__ == "__main__":
    main()
