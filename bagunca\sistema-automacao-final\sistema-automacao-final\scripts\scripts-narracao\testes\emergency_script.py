#!/usr/bin/env python3
"""
Script de emergência para filtrar apenas chaves funcionais
e continuar o processamento com as chaves que ainda funcionam
"""

import requests
import json
import time
from pathlib import Path

def emergency_filter_working_keys(input_file: str, output_file: str):
    """
    Filtra apenas as chaves que ainda funcionam e salva em novo arquivo
    """
    print("🚨 MODO EMERGÊNCIA - Filtrando chaves funcionais")
    print("=" * 50)
    
    if not Path(input_file).exists():
        print(f"❌ Arquivo não encontrado: {input_file}")
        return False
    
    # Carrega chaves
    with open(input_file, 'r') as f:
        all_keys = [line.strip() for line in f.readlines() 
                   if line.strip() and not line.startswith('#')]
    
    if not all_keys:
        print("❌ Nenhuma chave encontrada")
        return False
    
    print(f"🔍 Testando {len(all_keys)} chaves...")
    
    working_keys = []
    blocked_keys = []
    
    for i, key in enumerate(all_keys, 1):
        print(f"[{i:2d}/{len(all_keys)}] Testando {key[:8]}...", end=" ")
        
        try:
            response = requests.get(
                "https://api.elevenlabs.io/v1/user/subscription",
                headers={"xi-api-key": key},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                remaining = data.get("character_limit", 0) - data.get("character_count", 0)
                if remaining > 0:
                    working_keys.append(key)
                    print(f"✅ OK ({remaining} chars)")
                else:
                    print(f"⚠️  OK mas sem créditos ({remaining})")
                    
            elif response.status_code == 401:
                blocked_keys.append(key)
                try:
                    error_detail = response.json().get("detail", {})
                    if "unusual_activity" in str(error_detail).lower():
                        print("🚫 BLOQUEADA (atividade incomum)")
                    else:
                        print("🚫 BLOQUEADA (unauthorized)")
                except:
                    print("🚫 BLOQUEADA")
            else:
                print(f"❌ ERRO HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ ERRO: {str(e)[:30]}...")
        
        # Pausa para evitar rate limiting
        time.sleep(0.7)
    
    # Resultados
    print("\n" + "=" * 50)
    print("📊 RESULTADOS:")
    print(f"✅ Chaves funcionais: {len(working_keys)}")
    print(f"🚫 Chaves bloqueadas: {len(blocked_keys)}")
    print(f"❌ Chaves com erro: {len(all_keys) - len(working_keys) - len(blocked_keys)}")
    
    if working_keys:
        # Salva chaves funcionais
        with open(output_file, 'w') as f:
            f.write("# Chaves funcionais após filtro de emergência\n")
            f.write(f"# Gerado em: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# {len(working_keys)} de {len(all_keys)} chaves funcionais\n\n")
            for key in working_keys:
                f.write(f"{key}\n")
        
        print(f"\n💾 Chaves funcionais salvas em: {output_file}")
        print(f"📝 Use este arquivo com o sistema atualizado")
        
        # Calcula total de caracteres disponíveis
        total_chars = 0
        for key in working_keys:
            try:
                response = requests.get(
                    "https://api.elevenlabs.io/v1/user/subscription",
                    headers={"xi-api-key": key},
                    timeout=5
                )
                if response.status_code == 200:
                    data = response.json()
                    remaining = data.get("character_limit", 0) - data.get("character_count", 0)
                    total_chars += remaining
            except:
                pass
        
        print(f"💡 Total de caracteres disponíveis: {total_chars:,}")
        
        return True
    else:
        print("\n❌ NENHUMA CHAVE FUNCIONAL ENCONTRADA!")
        print("\nOpções:")
        print("1. Aguardar 24-48 horas e tentar novamente")
        print("2. Desativar VPN/Proxy se estiver usando")
        print("3. Upgrade para plano pago ($5/mês)")
        print("4. Obter novas chaves API de contas diferentes")
        
        return False

def emergency_test_single_tts(key: str):
    """
    Testa uma única chave com uma requisição TTS pequena
    """
    print(f"\n🧪 Testando TTS com chave {key[:8]}...")
    
    headers = {
        "xi-api-key": key,
        "Content-Type": "application/json",
        "Accept": "audio/mpeg"
    }
    
    payload = {
        "text": "Teste rápido.",
        "model_id": "eleven_multilingual_v2"
    }
    
    try:
        response = requests.post(
            "https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq81kWAM",
            headers=headers,
            params={"output_format": "mp3_44100_128"},
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            # Salva arquivo de teste
            with open("teste_emergencia.mp3", "wb") as f:
                f.write(response.content)
            print("✅ TTS funcionou! Arquivo: teste_emergencia.mp3")
            return True
        else:
            print(f"❌ TTS falhou: HTTP {response.status_code}")
            try:
                error = response.json()
                print(f"   Erro: {error}")
            except:
                print(f"   Resposta: {response.text[:100]}...")
            return False
            
    except Exception as e:
        print(f"❌ Erro na requisição TTS: {e}")
        return False

if __name__ == "__main__":
    print("🚨 SCRIPT DE EMERGÊNCIA - ElevenLabs")
    print("Filtra chaves funcionais após bloqueio por atividade incomum")
    print("=" * 60)
    
    # Configurações
    input_file = "config/chaves-api-elevenlabs.txt"
    output_file = "config/chaves-funcionais-emergencia.txt"
    
    # Filtra chaves
    success = emergency_filter_working_keys(input_file, output_file)
    
    if success:
        print(f"\n🎯 PRÓXIMOS PASSOS:")
        print(f"1. Use o arquivo: {output_file}")
        print(f"2. Substitua seus módulos pelos códigos melhorados")
        print(f"3. Execute: python narrador_roteiros_final.py --keys {output_file}")
        
        # Teste opcional de TTS
        print(f"\n❓ Testar TTS com uma chave? (y/n): ", end="")
        test_choice = input().lower().strip()
        
        if test_choice == 'y':
            with open(output_file, 'r') as f:
                working_keys = [line.strip() for line in f.readlines() 
                              if line.strip() and not line.startswith('#')]
            
            if working_keys:
                emergency_test_single_tts(working_keys[0])
    else:
        print(f"\n💡 RECOMENDAÇÕES:")
        print(f"1. Aguarde 24-48 horas")
        print(f"2. Verifique se está usando VPN/Proxy")
        print(f"3. Considere upgrade para plano pago")
        print(f"4. Entre em contato: <EMAIL>")
