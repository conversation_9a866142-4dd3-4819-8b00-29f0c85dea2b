#!/usr/bin/env python3
"""
Utilitários para Sistema Sequencial de Narração
Ferramentas para gerenciar chaves, estado e progresso do sistema
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, List

# Adiciona o diretório do projeto ao PATH
SCRIPT_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = SCRIPT_DIR.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from api_key_manager_sequential import SequentialAPIKeyManager

def show_status(keys_file: Path, project_root: Path):
    """Mostra status detalhado do sistema."""
    try:
        manager = SequentialAPIKeyManager(str(keys_file), str(project_root))
        status = manager.get_status_summary()
        
        print("\n" + "="*60)
        print("📊 STATUS DO SISTEMA SEQUENCIAL")
        print("="*60)
        
        print(f"🔑 CHAVE ATUAL:")
        if status['current_key']:
            print(f"   ID: {status['current_key']}")
            print(f"   Caracteres restantes: {status['current_key_remaining']:,}")
            print(f"   Status: {'⚠️  Próximo do limite' if status['current_key_remaining'] <= 200 else '✅ OK'}")
        else:
            print("   Nenhuma chave ativa")
        
        print(f"\n📈 RESUMO GERAL:")
        print(f"   Chaves ativas restantes: {status['active_keys_count']}")
        print(f"   Total de caracteres estimados: {status['estimated_chars_available']:,}")
        print(f"   Limite para pausa: {status['threshold']} caracteres")

        print(f"\n🌐 CONTROLE DE IP:")
        print(f"   Chaves usadas no IP atual: {status.get('keys_used_current_ip', 0)}")
        print(f"   Limite por IP: {status.get('keys_per_ip_limit', 3)} chaves")
        print(f"   Chaves restantes no IP atual: {status.get('keys_remaining_current_ip', 3)}")
        
        print(f"\n📁 ARQUIVOS DE CONTROLE:")
        print(f"   Estado: {status['processing_state_file']}")
        print(f"   Backup: {status['backup_file']}")
        
        # Verifica arquivos processados
        if 'processed_files' in manager.processing_state:
            processed_count = len(manager.processing_state['processed_files'])
            print(f"   Arquivos processados: {processed_count}")
        
        # Verifica chaves usadas
        used_keys_file = project_root / "config" / "chaves-usadas.txt"
        used_count = 0
        if used_keys_file.exists():
            with open(used_keys_file, 'r') as f:
                used_count = len([line for line in f.readlines() if line.strip()])
        
        print(f"\n🗂️  HISTÓRICO:")
        print(f"   Chaves já utilizadas: {used_count}")
        
        print("="*60)
        
    except Exception as e:
        print(f"❌ Erro ao verificar status: {e}")

def list_processed_files(project_root: Path):
    """Lista arquivos já processados."""
    state_file = project_root / "config" / "processing_state.json"
    
    if not state_file.exists():
        print("📄 Nenhum arquivo processado ainda")
        return
    
    try:
        with open(state_file, 'r') as f:
            state = json.load(f)
        
        processed = state.get('processed_files', [])
        
        if not processed:
            print("📄 Nenhum arquivo processado ainda")
            return
        
        print(f"\n📄 ARQUIVOS PROCESSADOS ({len(processed)}):")
        print("-" * 50)
        for i, file_path in enumerate(processed, 1):
            file_name = Path(file_path).name
            print(f"{i:3d}. {file_name}")
        
    except Exception as e:
        print(f"❌ Erro ao listar arquivos: {e}")

def reset_state(project_root: Path, confirm: bool = False):
    """Reseta o estado de processamento."""
    state_file = project_root / "config" / "processing_state.json"
    
    if not confirm:
        print("⚠️  Esta operação irá:")
        print("   • Resetar o progresso de processamento")
        print("   • Limpar lista de arquivos processados") 
        print("   • Manter as chaves ativas intactas")
        print("   • Criar backup do estado atual")
        
        resposta = input("\nTem certeza? Digite 'confirmo' para prosseguir: ")
        if resposta.lower() != 'confirmo':
            print("❌ Operação cancelada")
            return
    
    try:
        # Cria backup se existir estado
        if state_file.exists():
            backup_file = state_file.with_suffix('.backup')
            import shutil
            shutil.copy2(state_file, backup_file)
            print(f"💾 Backup criado: {backup_file}")
        
        # Reseta o estado
        new_state = {
            'current_key': None,
            'current_key_remaining': 0,
            'processed_files': [],
            'current_file_progress': None,
            'reset_timestamp': __import__('time').time()
        }
        
        state_file.parent.mkdir(parents=True, exist_ok=True)
        with open(state_file, 'w') as f:
            json.dump(new_state, f, indent=2)
        
        print("✅ Estado resetado com sucesso!")
        print("🚀 Próxima execução iniciará do zero")
        
    except Exception as e:
        print(f"❌ Erro ao resetar estado: {e}")

def add_keys(keys_file: Path, new_keys: List[str]):
    """Adiciona novas chaves ao arquivo."""
    if not new_keys:
        print("❌ Nenhuma chave fornecida")
        return
    
    try:
        # Lê chaves existentes
        existing_keys = set()
        if keys_file.exists():
            with open(keys_file, 'r') as f:
                existing_keys = {line.strip() for line in f.readlines() if line.strip() and not line.startswith('#')}
        
        # Filtra chaves novas
        unique_new_keys = []
        for key in new_keys:
            key = key.strip()
            if key and key not in existing_keys:
                unique_new_keys.append(key)
            elif key in existing_keys:
                print(f"⚠️  Chave já existe (ignorada): {key[:8]}...")
        
        if not unique_new_keys:
            print("❌ Nenhuma chave nova para adicionar")
            return
        
        # Adiciona novas chaves
        keys_file.parent.mkdir(parents=True, exist_ok=True)
        with open(keys_file, 'a') as f:
            for key in unique_new_keys:
                f.write(f"{key}\n")
        
        print(f"✅ {len(unique_new_keys)} chave(s) adicionada(s) com sucesso!")
        for key in unique_new_keys:
            print(f"   + {key[:8]}...")
            
    except Exception as e:
        print(f"❌ Erro ao adicionar chaves: {e}")

def show_usage_summary(project_root: Path):
    """Mostra resumo de uso das chaves."""
    used_keys_file = project_root / "config" / "chaves-usadas.txt"
    
    print("\n💰 RESUMO DE USO DAS CHAVES")
    print("="*50)
    
    if not used_keys_file.exists():
        print("📊 Nenhuma chave foi utilizada ainda")
        return
    
    try:
        with open(used_keys_file, 'r') as f:
            used_keys = [line.strip() for line in f.readlines() if line.strip()]
        
        if not used_keys:
            print("📊 Nenhuma chave foi utilizada ainda")
            return
        
        total_chars_used = len(used_keys) * 10000  # Cada chave = 10k chars
        
        print(f"🔑 Chaves utilizadas: {len(used_keys)}")
        print(f"📈 Caracteres consumidos: {total_chars_used:,}")
        print(f"💵 Custo estimado: ${len(used_keys) * 1:.2f} (assumindo $1/chave)")
        
        print(f"\n📋 CHAVES UTILIZADAS:")
        for i, key in enumerate(used_keys, 1):
            print(f"{i:3d}. {key[:8]}... (10,000 chars)")
            
    except Exception as e:
        print(f"❌ Erro ao mostrar resumo: {e}")

def cleanup_temp_files(project_root: Path):
    """Remove arquivos temporários antigos."""
    temp_dir = project_root / "narracao_sequencial" / "temp_processing"
    
    if not temp_dir.exists():
        print("📁 Nenhum diretório temporário encontrado")
        return
    
    try:
        import shutil
        import time
        
        temp_folders = list(temp_dir.glob("*"))
        if not temp_folders:
            print("🧹 Nenhum arquivo temporário para limpar")
            return
        
        # Lista arquivos antigos (mais de 1 hora)
        current_time = time.time()
        old_folders = []
        
        for folder in temp_folders:
            if folder.is_dir():
                folder_time = folder.stat().st_mtime
                if current_time - folder_time > 3600:  # 1 hora
                    old_folders.append(folder)
        
        if not old_folders:
            print("🧹 Nenhum arquivo temporário antigo encontrado")
            return
        
        print(f"🧹 Removendo {len(old_folders)} pasta(s) temporária(s) antiga(s)...")
        
        for folder in old_folders:
            shutil.rmtree(folder, ignore_errors=True)
            print(f"   ✅ Removido: {folder.name}")
        
        print("✅ Limpeza concluída!")
        
    except Exception as e:
        print(f"❌ Erro na limpeza: {e}")

def main():
    """Função principal dos utilitários."""
    parser = argparse.ArgumentParser(
        description="Utilitários para o Sistema Sequencial de Narração",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:
  python utils_sequencial.py status                    # Mostra status do sistema
  python utils_sequencial.py files                     # Lista arquivos processados
  python utils_sequencial.py reset                     # Reseta estado (com confirmação)
  python utils_sequencial.py add-keys sk_abc123...     # Adiciona novas chaves
  python utils_sequencial.py usage                     # Mostra resumo de uso
  python utils_sequencial.py cleanup                   # Remove arquivos temporários
        """
    )
    
    # Caminhos padrão
    projeto_root = PROJECT_ROOT
    arquivo_chaves = PROJECT_ROOT / "config" / "chaves-api-elevenlabs.txt"
    
    parser.add_argument(
        "comando",
        choices=["status", "files", "reset", "add-keys", "usage", "cleanup"],
        help="Comando a ser executado"
    )
    parser.add_argument(
        "chaves",
        nargs="*",
        help="Chaves API para adicionar (usar com add-keys)"
    )
    parser.add_argument(
        "--keys-file",
        type=Path,
        default=arquivo_chaves,
        help=f"Arquivo de chaves. Padrão: {arquivo_chaves}"
    )
    parser.add_argument(
        "--project-root",
        type=Path,
        default=projeto_root,
        help=f"Raiz do projeto. Padrão: {projeto_root}"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Força operação sem confirmação"
    )

    args = parser.parse_args()

    print("🛠️  UTILITÁRIOS DO SISTEMA SEQUENCIAL")
    
    if args.comando == "status":
        show_status(args.keys_file, args.project_root)
    
    elif args.comando == "files":
        list_processed_files(args.project_root)
    
    elif args.comando == "reset":
        reset_state(args.project_root, args.force)
    
    elif args.comando == "add-keys":
        if not args.chaves:
            print("❌ Erro: Forneça pelo menos uma chave para adicionar")
            print("Exemplo: python utils_sequencial.py add-keys sk_abc123...")
            return 1
        add_keys(args.keys_file, args.chaves)
    
    elif args.comando == "usage":
        show_usage_summary(args.project_root)
    
    elif args.comando == "cleanup":
        cleanup_temp_files(args.project_root)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
