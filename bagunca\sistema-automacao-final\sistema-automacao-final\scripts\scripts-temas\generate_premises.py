#!/usr/bin/env python3.11
# -*- coding: utf-8 -*-

import os
import pandas as pd
import sys
import time
from openai import OpenAI # Import the OpenAI library

# --- Project Structure Setup ---
# Get the directory where the script is located
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# Project root is two levels up from the script directory
PROJECT_ROOT = os.path.abspath(os.path.join(SCRIPT_DIR, os.pardir, os.pardir))

# --- Configuration ---
# Use paths relative to the project root
CSV_FILE = os.path.join(PROJECT_ROOT, "top_100_videos_virais.csv")
OUTPUT_FILE = os.path.join(PROJECT_ROOT, "generated_premises.md")
API_KEY_FILE = os.path.join(PROJECT_ROOT, "config", "chaves-api-openai.txt")

# OpenAI Assistant Configuration
ASSISTANT_ID = "asst_ZUlnrdqy1192GixbdWvCHi5d" # Provided Assistant ID
POLLING_INTERVAL = 5 # Seconds to wait between checking run status
MAX_WAIT_TIME = 300 # Maximum seconds to wait for the assistant run to complete

# Generation Parameters
NUM_PREMISES = 30
NUM_VIRAL_VIDEOS = 50 # Number of top viral videos to consider for themes

# --- Helper Functions ---
def get_openai_api_key(filepath):
    """Reads the OpenAI API key from a local file."""
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            key = f.read().strip()
            if key:
                print(f"Chave API OpenAI lida com sucesso de {filepath}")
                return key
            else:
                print(f"Erro: O arquivo {filepath} está vazio.")
                return None
    except FileNotFoundError:
        print(f"Erro: Arquivo de chave API não encontrado em {filepath}.")
        print(f"Por favor, crie o arquivo \'{os.path.basename(filepath)}\' na pasta \'{os.path.basename(os.path.dirname(filepath))}\' dentro da raiz do projeto e cole sua chave API OpenAI nele.")
        return None
    except Exception as e:
        print(f"Erro ao ler o arquivo de chave API {filepath}: {e}")
        return None

def get_csv_file(filepath):
    """Checks if the specified CSV file exists."""
    print(f"Verificando a existência do arquivo CSV em: {filepath}")
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"Arquivo CSV não encontrado em: {filepath}. Certifique-se de que o arquivo \'top_100_videos_virais.csv\' existe na raiz do projeto.")
    print(f"Usando arquivo CSV: {filepath}")
    return filepath

# --- Main Logic ---
print("Iniciando script de geração de premissas usando OpenAI Assistant...")

# Read API Key from file
OPENAI_API_KEY = get_openai_api_key(API_KEY_FILE)
if not OPENAI_API_KEY:
    sys.exit(1)

# Initialize OpenAI Client
try:
    client = OpenAI(api_key=OPENAI_API_KEY)
    print("Cliente OpenAI inicializado com sucesso.")
except Exception as e:
    print(f"Erro ao inicializar o cliente OpenAI: {e}")
    sys.exit(1)

# 1. Load and Process CSV
print("Carregando e processando CSV...")
top_titles_string = "[Erro ao processar CSV]"
try:
    csv_file_path = get_csv_file(CSV_FILE)
    df = pd.read_csv(csv_file_path)
    # Sort by view_count and get top titles
    top_videos = df.nlargest(NUM_VIRAL_VIDEOS, 'view_count')
    # Format titles as a simple list string for the prompt
    top_titles_list = [f"- {title}" for title in top_videos['title']]
    top_titles_string = "\n".join(top_titles_list)
    print(f"Extraídos os {NUM_VIRAL_VIDEOS} títulos de vídeos virais principais do arquivo {os.path.basename(csv_file_path)}.")
except FileNotFoundError as e:
    print(f"Erro: {e}")
    sys.exit(1)
except KeyError as e:
    print(f"Erro: Coluna {e} não encontrada no CSV. Verifique se as colunas 'title' e 'view_count' existem.")
    sys.exit(1)
except Exception as e:
    print(f"Erro ao processar CSV: {e}")
    sys.exit(1) # Exit if CSV processing fails

# 2. Prepare Prompt for Assistant
print("Preparando prompt para o Assistant...")
prompt_content = f"""Com base no seu conhecimento sobre a persona 'A Injustiçada Resiliente Feminina' (dores, desejos, motivações) e as estruturas de premissas que você possui, e considerando os seguintes temas virais recentes (títulos de vídeos populares):

Temas Virais Recentes:
----------
{top_titles_string}
----------

Tarefa:
Gere EXATAMENTE {NUM_PREMISES} novas premissas de histórias únicas e atraentes.

Requisitos para cada premissa:
- Deve ser detalhada e impactante (3-4 frases).
- Deve refletir um ou mais dos temas virais recentes (ex: traição financeira, injustiça familiar/profissional, superação, vingança por sucesso).
- Deve estar profundamente alinhada com a persona 'A Injustiçada Resiliente Feminina'.
- Deve preferencialmente adaptar ou se inspirar nas estruturas narrativas que você conhece (ex: A Herdeira Preterida, O Crédito Roubado, A Transformação Silenciosa).
- Deve focar em protagonistas femininas enfrentando e superando injustiças.

Liste as {NUM_PREMISES} premissas numeradas:"""

# 3. Interact with OpenAI Assistant
print(f"Interagindo com o Assistant (ID: {ASSISTANT_ID})...")
generated_premises = f"Erro inicial ao interagir com o Assistant."
run = None
thread = None
start_time = time.time()
try:
    # Step 1: Create a Thread
    print("Passo 1: Criando Thread...")
    thread = client.beta.threads.create()
    print(f"Thread criada com ID: {thread.id}")

    # Step 2: Add Message to Thread
    print("Passo 2: Adicionando mensagem à Thread...")
    message = client.beta.threads.messages.create(
        thread_id=thread.id,
        role="user",
        content=prompt_content
    )

    # Step 3: Run the Assistant
    print(f"Passo 3: Executando Assistant {ASSISTANT_ID} na Thread {thread.id}...")
    run = client.beta.threads.runs.create(
        thread_id=thread.id,
        assistant_id=ASSISTANT_ID,
        # instructions="Instruções adicionais se necessário, mas o prompt principal está na mensagem"
    )
    print(f"Run iniciado com ID: {run.id}")

    # Step 4: Poll for Run Completion
    print("Passo 4: Aguardando conclusão do Run...")
    while run.status in ['queued', 'in_progress']:
        time.sleep(POLLING_INTERVAL)
        run = client.beta.threads.runs.retrieve(thread_id=thread.id, run_id=run.id)
        print(f"Status do Run: {run.status}")
        if time.time() - start_time > MAX_WAIT_TIME:
            print("Erro: Tempo máximo de espera excedido.")
            client.beta.threads.runs.cancel(thread_id=thread.id, run_id=run.id)
            raise TimeoutError("Assistant run timed out")

    # Step 5: Check Run Status and Retrieve Messages
    if run.status == 'completed':
        print("Run concluído com sucesso. Recuperando mensagens...")
        messages = client.beta.threads.messages.list(thread_id=thread.id, order='desc') # Get messages, newest first
        # Find the first message from the assistant
        assistant_message = None
        for msg in messages.data:
            if msg.role == 'assistant':
                assistant_message = msg
                break

        if assistant_message and assistant_message.content:
            # Extract text content
            message_content = assistant_message.content[0]
            if message_content.type == 'text':
                generated_premises = message_content.text.value
                print("Premissas extraídas com sucesso da resposta do Assistant.")
            else:
                generated_premises = f"Erro: Conteúdo inesperado na mensagem do Assistant (tipo: {message_content.type})."
                print(generated_premises)
        else:
            generated_premises = "Erro: Nenhuma resposta do Assistant encontrada na Thread."
            print(generated_premises)

    else:
        generated_premises = f"Erro: Run do Assistant falhou ou foi cancelado. Status final: {run.status}. Detalhes: {run.last_error or 'N/A'}"
        print(generated_premises)

except Exception as e:
    print(f"Erro durante a interação com o OpenAI Assistant: {e}")
    generated_premises = f"Erro ao gerar premissas via Assistant: {e}. Run Status: {run.status if run else 'N/A'}"
    # Attempt to cancel the run if it's still active and an error occurred
    if run and run.status in ['queued', 'in_progress']:
        try:
            client.beta.threads.runs.cancel(thread_id=thread.id, run_id=run.id)
            print(f"Tentativa de cancelar Run {run.id}.")
        except Exception as cancel_err:
            print(f"Erro ao tentar cancelar Run {run.id}: {cancel_err}")

# 4. Save Output
print(f"Salvando premissas em {OUTPUT_FILE}...")
try:
    output_filename = OUTPUT_FILE # Overwrite the same file in the project root

    with open(output_filename, "w", encoding="utf-8") as f:
        f.write(f"# Premissas Geradas via Assistant ({pd.Timestamp.now()})\n\n")
        f.write(f"Assistant ID: {ASSISTANT_ID}\n")
        f.write(f"Baseado nos temas de: {os.path.basename(csv_file_path)}\n\n")
        if isinstance(generated_premises, str):
            f.write(generated_premises)
        else:
            print(f"Erro: generated_premises não é uma string antes de escrever. Tipo: {type(generated_premises)}")
            f.write(str(generated_premises))
    print(f"Saída salva em: {output_filename}")
except Exception as e:
    print(f"Erro ao salvar arquivo de saída: {e}")

print("Script finalizado.")