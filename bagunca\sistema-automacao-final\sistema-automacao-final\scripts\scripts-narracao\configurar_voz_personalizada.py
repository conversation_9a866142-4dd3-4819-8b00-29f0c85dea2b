#!/usr/bin/env python3
"""
Configurador de Voz Personalizada
Permite configurar facilmente o voice ID e ajustes de voz personalizados.
"""

import os
import sys
import json
import logging
from pathlib import Path

# Adiciona o diretório pai ao path para importar os módulos
sys.path.append(str(Path(__file__).parent))

from config_manager import ConfigManager

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ConfiguradorVoz")

def exibir_configuracao_atual(config_manager):
    """Exibe a configuração atual de voz."""
    voice_config = config_manager.get_config("voice") or {}
    
    print(f"\n📋 CONFIGURAÇÃO ATUAL DE VOZ:")
    print(f"   Voice ID: {voice_config.get('voice_id', 'N/A')}")
    print(f"   Voice Name: {voice_config.get('voice_name', 'N/A')}")
    print(f"   Model ID: {voice_config.get('model_id', 'N/A')}")
    print(f"   Output Format: {voice_config.get('output_format', 'N/A')}")
    
    voice_settings = voice_config.get('voice_settings', {})
    if voice_settings:
        print(f"\n🎛️  CONFIGURAÇÕES DE VOZ:")
        print(f"   Stability: {voice_settings.get('stability', 'N/A')}")
        print(f"   Similarity Boost: {voice_settings.get('similarity_boost', 'N/A')}")
        print(f"   Style: {voice_settings.get('style', 'N/A')}")
        print(f"   Use Speaker Boost: {voice_settings.get('use_speaker_boost', 'N/A')}")
    else:
        print(f"\n⚠️  Nenhuma configuração de voice_settings encontrada!")

def obter_input_float(prompt, valor_atual=None, min_val=0.0, max_val=1.0):
    """Obtém um valor float do usuário com validação."""
    while True:
        if valor_atual is not None:
            entrada = input(f"{prompt} (atual: {valor_atual}): ").strip()
        else:
            entrada = input(f"{prompt}: ").strip()
        
        if not entrada and valor_atual is not None:
            return valor_atual
        
        try:
            valor = float(entrada)
            if min_val <= valor <= max_val:
                return valor
            else:
                print(f"❌ Valor deve estar entre {min_val} e {max_val}")
        except ValueError:
            print(f"❌ Por favor, digite um número válido")

def obter_input_bool(prompt, valor_atual=None):
    """Obtém um valor booleano do usuário."""
    while True:
        if valor_atual is not None:
            entrada = input(f"{prompt} (atual: {valor_atual}) [s/n]: ").strip().lower()
        else:
            entrada = input(f"{prompt} [s/n]: ").strip().lower()
        
        if not entrada and valor_atual is not None:
            return valor_atual
        
        if entrada in ['s', 'sim', 'y', 'yes', 'true', '1']:
            return True
        elif entrada in ['n', 'não', 'nao', 'no', 'false', '0']:
            return False
        else:
            print(f"❌ Por favor, digite 's' para sim ou 'n' para não")

def configurar_voz_personalizada():
    """Configura uma voz personalizada."""
    print(f"\n🎤 CONFIGURAÇÃO DE VOZ PERSONALIZADA")
    print(f"=" * 50)
    
    # Solicita informações da voz
    voice_id = input("Digite o Voice ID da sua voz personalizada: ").strip()
    if not voice_id:
        print("❌ Voice ID é obrigatório!")
        return False
    
    voice_name = input("Digite um nome para sua voz (opcional): ").strip()
    if not voice_name:
        voice_name = "Voz Personalizada"
    
    print(f"\n🎛️  CONFIGURAÇÕES DE VOZ")
    print(f"Configure os parâmetros da sua voz (valores entre 0.0 e 1.0):")
    
    stability = obter_input_float(
        "Stability (estabilidade - menos variação = mais estável)", 
        min_val=0.0, max_val=1.0
    )
    
    similarity_boost = obter_input_float(
        "Similarity Boost (similaridade com voz original)", 
        min_val=0.0, max_val=1.0
    )
    
    style = obter_input_float(
        "Style (exagero do estilo - mais dramático)", 
        min_val=0.0, max_val=1.0
    )
    
    use_speaker_boost = obter_input_bool("Use Speaker Boost (melhora a clareza)")
    
    # Configurações adicionais
    model_id = input("Model ID (pressione Enter para 'eleven_multilingual_v2'): ").strip()
    if not model_id:
        model_id = "eleven_multilingual_v2"
    
    output_format = input("Output Format (pressione Enter para 'mp3_44100_128'): ").strip()
    if not output_format:
        output_format = "mp3_44100_128"
    
    return {
        "voice_id": voice_id,
        "voice_name": voice_name,
        "model_id": model_id,
        "output_format": output_format,
        "voice_settings": {
            "stability": stability,
            "similarity_boost": similarity_boost,
            "style": style,
            "use_speaker_boost": use_speaker_boost
        }
    }

def main():
    """Função principal."""
    print("🎤 CONFIGURADOR DE VOZ PERSONALIZADA")
    print("=" * 50)
    
    try:
        # Inicializa o gerenciador de configurações
        config_manager = ConfigManager()
        
        # Exibe configuração atual
        exibir_configuracao_atual(config_manager)
        
        print(f"\n🔧 OPÇÕES:")
        print(f"1. Configurar nova voz personalizada")
        print(f"2. Usar configuração pré-definida (ID: Ir1QNHvhaJXbAGhT50w3)")
        print(f"3. Sair")
        
        opcao = input("\nEscolha uma opção (1-3): ").strip()
        
        if opcao == "1":
            nova_config = configurar_voz_personalizada()
            if not nova_config:
                return 1
                
        elif opcao == "2":
            # Configuração pré-definida fornecida pelo usuário
            nova_config = {
                "voice_id": "Ir1QNHvhaJXbAGhT50w3",
                "voice_name": "Sua Voz Personalizada",
                "model_id": "eleven_multilingual_v2",
                "output_format": "mp3_44100_128",
                "voice_settings": {
                    "stability": 0.9,
                    "similarity_boost": 0.95,
                    "style": 0.0,
                    "use_speaker_boost": True
                }
            }
            print(f"\n✅ Usando configuração pré-definida!")
            
        elif opcao == "3":
            print("Saindo...")
            return 0
            
        else:
            print("❌ Opção inválida!")
            return 1
        
        # Confirma a configuração
        print(f"\n📋 NOVA CONFIGURAÇÃO:")
        print(f"   Voice ID: {nova_config['voice_id']}")
        print(f"   Voice Name: {nova_config['voice_name']}")
        print(f"   Model ID: {nova_config['model_id']}")
        print(f"   Output Format: {nova_config['output_format']}")
        print(f"\n🎛️  VOICE SETTINGS:")
        for key, value in nova_config['voice_settings'].items():
            print(f"   {key}: {value}")
        
        confirma = input(f"\nDeseja salvar esta configuração? [s/N]: ").strip().lower()
        if confirma not in ['s', 'sim', 'y', 'yes']:
            print("Configuração cancelada.")
            return 0
        
        # Salva a configuração
        for key, value in nova_config.items():
            config_manager.update_config("voice", key, value)
        
        print(f"\n✅ CONFIGURAÇÃO SALVA COM SUCESSO!")
        print(f"🎯 Execute o teste para verificar: python teste_voz_personalizada.py")
        
    except Exception as e:
        logger.exception("Erro durante a configuração:")
        print(f"\n❌ ERRO: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
