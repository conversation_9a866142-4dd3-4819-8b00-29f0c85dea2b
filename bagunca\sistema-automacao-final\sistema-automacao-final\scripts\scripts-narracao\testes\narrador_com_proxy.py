#!/usr/bin/env python3
"""
Narrador de Roteiros com Rotação Automática de Proxies WebShare.io

Funcionalidades:
1. Rotação automática de proxies a cada 15 requisições
2. Fallback para requisições diretas se proxies falharem
3. Integração transparente com sistema existente
4. Logs detalhados de performance de proxies
5. Configuração flexível via arquivo JSON
"""

import os
import sys
import logging
import re
from pathlib import Path
import argparse
import json
import time

# Importa os módulos existentes
from config_manager import ConfigManager
from api_key_manager import APIKeyManager
from proxy_manager import ProxyManager
from elevenlabs_api_with_proxy import ElevenLabsAPIWithProxy
from processador_arquivos_atualizado import ProcessadorArquivos

# --- Define Project Root ---
SCRIPT_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = SCRIPT_DIR.parent.parent

# --- Configuração Inicial ---
CONFIG_FILE_PATH = PROJECT_ROOT / "config" / "config_narracao_com_proxy.json"
config_manager = ConfigManager(CONFIG_FILE_PATH)

# --- Configuração de Logging ---
def setup_logging():
    """Configura o sistema de logging."""
    LOG_DIR = PROJECT_ROOT / "logs"
    LOG_DIR.mkdir(parents=True, exist_ok=True)
    
    log_config = config_manager.get_config("logging") or {}
    log_file_name = config_manager.get_config("paths", "log_file") or "narracao_com_proxy.log"
    log_file_path = LOG_DIR / log_file_name
    
    # Configuração do logging
    log_level = getattr(logging, log_config.get("level", "INFO"))
    log_format = log_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    
    handlers = []
    
    # Handler para arquivo
    if log_config.get("file_enabled", True):
        handlers.append(logging.FileHandler(log_file_path))
    
    # Handler para console
    if log_config.get("console_enabled", True):
        handlers.append(logging.StreamHandler())
    
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=handlers
    )
    
    return logging.getLogger("NarradorComProxy")

logger = setup_logging()

def clean_text(raw_text):
    """Limpa o texto removendo símbolos e linhas indesejadas."""
    cleaned_lines = []
    for line in raw_text.splitlines():
        if re.match(r"^CENA \d+:|^ATO \d+:|^\*\*.+\*\*", line.strip(), re.IGNORECASE):
            cleaned_lines.append(line.strip())
            continue
        if line.strip().startswith(("#", "-")) or re.match(r"^\d+\. ", line.strip()):
             continue
        line = line.replace("*", "")
        cleaned_lines.append(line.strip())
    return "\n".join(filter(None, cleaned_lines))

def validate_webshare_token(token: str) -> bool:
    """
    Valida se o token da WebShare.io está configurado e é válido.
    
    Args:
        token: Token da API WebShare
        
    Returns:
        True se token é válido, False caso contrário
    """
    if not token or token == "YOUR_WEBSHARE_TOKEN_HERE":
        return False
    
    try:
        import requests
        headers = {"Authorization": f"Token {token}"}
        response = requests.get(
            "https://proxy.webshare.io/api/v2/profile/",
            headers=headers,
            timeout=10
        )
        return response.status_code == 200
    except:
        return False

def display_startup_banner():
    """Exibe banner de inicialização."""
    print("=" * 80)
    print("🎭 NARRADOR DE ROTEIROS COM ROTAÇÃO DE PROXY")
    print("   Sistema automatizado de narração usando ElevenLabs + WebShare.io")
    print("=" * 80)
    print()

def main():
    """Função principal do script."""
    display_startup_banner()

    # --- Argument Parsing ---
    parser = argparse.ArgumentParser(
        description="Automatiza a narração de roteiros com rotação de proxies WebShare.io"
    )
    
    # Diretórios relativos à raiz do projeto
    diretorio_roteiros_default = PROJECT_ROOT / "roteiros_gerados"
    diretorio_saida_default = PROJECT_ROOT / "narracao"
    arquivo_chaves_default = PROJECT_ROOT / "config" / "chaves-api-elevenlabs.txt"

    parser.add_argument(
        "--output",
        type=Path,
        default=diretorio_saida_default,
        help=f"Diretório de saída para os áudios. Padrão: {diretorio_saida_default}"
    )
    parser.add_argument(
        "--keys",
        type=Path,
        default=arquivo_chaves_default,
        help=f"Arquivo de chaves ElevenLabs. Padrão: {arquivo_chaves_default}"
    )
    parser.add_argument(
        "--no-proxy",
        action="store_true",
        help="Desabilita o uso de proxies (apenas requisições diretas)"
    )
    parser.add_argument(
        "--test-proxies",
        action="store_true",
        help="Testa todos os proxies antes de iniciar o processamento"
    )
    parser.add_argument(
        "--proxy-threshold",
        type=int,
        help="Número de requisições antes de rotacionar proxy (padrão: 15)"
    )

    args = parser.parse_args()

    # Configurações de caminhos
    diretorio_roteiros = diretorio_roteiros_default
    diretorio_saida = args.output.resolve()
    arquivo_chaves = args.keys.resolve()
    
    diretorio_saida.mkdir(parents=True, exist_ok=True)

    logger.info("=== Iniciando Narração com Rotação de Proxy ===")
    logger.info(f"Raiz do Projeto: {PROJECT_ROOT}")
    logger.info(f"Diretório de Roteiros: {diretorio_roteiros}")
    logger.info(f"Diretório de Saída: {diretorio_saida}")
    logger.info(f"Arquivo de Chaves: {arquivo_chaves}")

    # Verificações básicas
    if not arquivo_chaves.is_file():
        logger.error(f"Arquivo de chaves não encontrado: {arquivo_chaves}")
        print(f"❌ ERRO: Arquivo de chaves não encontrado: {arquivo_chaves}")
        return 1

    if not diretorio_roteiros.is_dir():
        logger.error(f"Diretório de roteiros não encontrado: {diretorio_roteiros}")
        print(f"❌ ERRO: Diretório de roteiros não encontrado: {diretorio_roteiros}")
        return 1

    try:
        # --- Obtenção de Configurações ---
        api_config = config_manager.get_config("api") or {}
        voice_config = config_manager.get_config("voice") or {}
        processing_config = config_manager.get_config("processing") or {}
        proxy_config = config_manager.get_config("proxy") or {}
        
        # Configurações da API
        base_url = api_config.get("base_url", "https://api.elevenlabs.io")
        max_retries = api_config.get("max_retries", 3)
        retry_delay = api_config.get("retry_delay", 2)
        max_key_retries = api_config.get("max_key_retries", 5)
        
        # Configurações de voz
        voice_id = voice_config.get("voice_id", "21m00Tcm4TlvDq81kWAM")
        model_id = voice_config.get("model_id", "eleven_multilingual_v2")
        output_format = voice_config.get("output_format", "mp3_44100_128")
        
        # Configurações de processamento
        tamanho_minimo_bloco = processing_config.get("tamanho_minimo_bloco", 900)
        tamanho_maximo_bloco = processing_config.get("tamanho_maximo_bloco", 1100)
        min_block_fallback = processing_config.get("min_block_size_on_fallback", 50)
        
        # Configurações de proxy
        proxy_enabled = proxy_config.get("enabled", True) and not args.no_proxy
        webshare_token = proxy_config.get("webshare_api_token", "")
        rotation_threshold = args.proxy_threshold or proxy_config.get("rotation_threshold", 15)
        max_retries_per_proxy = proxy_config.get("max_retries_per_proxy", 3)
        connection_timeout = proxy_config.get("connection_timeout", 30)
        enable_fallback = proxy_config.get("enable_fallback", True)
        test_proxies_startup = args.test_proxies or proxy_config.get("test_proxies_on_startup", False)

        print(f"🔧 Configurações carregadas:")
        print(f"   - Rotação de proxy: {'✅ Habilitada' if proxy_enabled else '❌ Desabilitada'}")
        print(f"   - Threshold de rotação: {rotation_threshold} requisições")
        print(f"   - Fallback direto: {'✅ Habilitado' if enable_fallback else '❌ Desabilitado'}")
        print()

        # --- Inicialização do Gerenciador de Chaves API ---
        logger.info("🔑 Inicializando gerenciador de chaves API...")
        api_key_manager = APIKeyManager(keys_file_path=str(arquivo_chaves))
        
        working_count = len(api_key_manager.working_keys)
        blocked_count = len(api_key_manager.blocked_keys)
        
        if working_count == 0:
            logger.error("🚨 ERRO CRÍTICO: Todas as chaves estão bloqueadas!")
            print("❌ ERRO: Todas as chaves API estão bloqueadas.")
            print("Possíveis soluções:")
            print("1. Aguardar algumas horas e tentar novamente")
            print("2. Verificar conexão/VPN")
            print("3. Obter novas chaves API")
            return 1
        
        print(f"✅ Chaves API: {working_count} funcionais, {blocked_count} bloqueadas")
        logger.info(f"Status das chaves: {working_count} funcionais, {blocked_count} bloqueadas")

        # --- Inicialização do Gerenciador de Proxies ---
        proxy_manager = None
        if proxy_enabled:
            logger.info("🌐 Inicializando gerenciador de proxies WebShare.io...")
            
            # Valida token WebShare
            if not validate_webshare_token(webshare_token):
                logger.error("Token WebShare.io inválido ou não configurado!")
                print("❌ ERRO: Token WebShare.io inválido!")
                print("Soluções:")
                print("1. Configure o token no arquivo de configuração")
                print("2. Verifique se o token está correto")
                print("3. Use --no-proxy para desabilitar proxies")
                return 1
            
            try:
                proxy_manager = ProxyManager(
                    webshare_api_token=webshare_token,
                    rotation_threshold=rotation_threshold,
                    max_retries_per_proxy=max_retries_per_proxy,
                    connection_timeout=connection_timeout
                )
                
                available_proxies = len(proxy_manager.available_proxies)
                if available_proxies == 0:
                    logger.warning("Nenhum proxy disponível na conta WebShare.io")
                    print("⚠️  Nenhum proxy encontrado na conta WebShare.io")
                    
                    if not enable_fallback:
                        print("❌ Fallback desabilitado - processo não pode continuar")
                        return 1
                    else:
                        print("🔄 Continuando com requisições diretas...")
                        proxy_manager = None
                else:
                    print(f"✅ Proxies: {available_proxies} disponíveis")
                    logger.info(f"Proxies carregados: {available_proxies} disponíveis")
                    
                    # Teste de proxies se solicitado
                    if test_proxies_startup:
                        print("🧪 Testando proxies...")
                        proxy_manager.test_all_proxies()
                        remaining = len(proxy_manager.available_proxies)
                        print(f"📊 Proxies após teste: {remaining} funcionais")
                        
                        if remaining == 0:
                            print("❌ Todos os proxies falharam no teste")
                            if not enable_fallback:
                                return 1
                            proxy_manager = None
                
            except Exception as e:
                logger.error(f"Erro ao inicializar proxy manager: {e}")
                print(f"❌ Erro nos proxies: {e}")
                
                if enable_fallback:
                    print("🔄 Continuando sem proxies...")
                    proxy_manager = None
                else:
                    return 1
        else:
            print("ℹ️  Proxies desabilitados - usando apenas requisições diretas")

        # --- Inicialização do Cliente ElevenLabs ---
        logger.info("🎵 Inicializando cliente ElevenLabs API...")
        elevenlabs_api = ElevenLabsAPIWithProxy(
            api_key_manager=api_key_manager,
            proxy_manager=proxy_manager,
            base_url=base_url,
            model_id=model_id,
            voice_id=voice_id,
            output_format=output_format,
            max_retries=max_retries,
            retry_delay=retry_delay,
            max_key_retries=max_key_retries,
            enable_proxy_fallback=enable_fallback
        )

        # --- Inicialização do Processador ---
        logger.info("📁 Inicializando processador de arquivos...")
        
        # NOTA: O ProcessadorArquivos original espera o ElevenLabsAPI antigo
        # Para compatibilidade, criamos um wrapper que simula a interface antiga
        class ElevenLabsAPIWrapper:
            def __init__(self, new_api):
                self.new_api = new_api
                
            def text_to_speech_with_auto_retry(self, **kwargs):
                return self.new_api.text_to_speech_with_auto_retry(**kwargs)
                
            def concatenate_audio_files(self, **kwargs):
                return self.new_api.concatenate_audio_files(**kwargs)
        
        processador = ProcessadorArquivos(
            diretorio_roteiros=diretorio_roteiros,
            diretorio_saida=diretorio_saida,
            tamanho_minimo_bloco=tamanho_minimo_bloco,
            tamanho_maximo_bloco=tamanho_maximo_bloco,
            api_key_manager=api_key_manager,
            elevenlabs_api=ElevenLabsAPIWrapper(elevenlabs_api),
            modelo_id=model_id,
            min_block_size_on_fallback=min_block_fallback
        )

        # --- Processamento ---
        logger.info("🚀 Iniciando processamento...")
        print("🚀 Iniciando processamento de arquivos...")
        print()
        
        start_time = time.time()
        resultados = processador.processar_todos_arquivos()
        end_time = time.time()
        
        processing_time = end_time - start_time

        # --- Resumo Final ---
        sucessos = sum(1 for v in resultados.values() if isinstance(v, Path) and v.exists())
        erros = len(resultados) - sucessos

        logger.info("=" * 60)
        logger.info("PROCESSAMENTO CONCLUÍDO")
        logger.info(f"Tempo total: {processing_time:.1f} segundos")
        logger.info(f"Arquivos processados: {len(resultados)}")
        logger.info(f"Sucessos: {sucessos}")
        logger.info(f"Erros: {erros}")

        # Estatísticas de proxies
        if proxy_manager:
            proxy_stats = elevenlabs_api.get_combined_statistics()
            api_stats = proxy_stats["api_requests"]
            proxy_mgr_stats = proxy_stats["proxy_manager"]
            
            logger.info("--- Estatísticas de Proxy ---")
            logger.info(f"Total de requisições: {api_stats['total_requests']}")
            logger.info(f"Via proxy: {api_stats['proxy_requests']}")
            logger.info(f"Requisições diretas: {api_stats['direct_requests']}")
            logger.info(f"Falhas de proxy: {api_stats['proxy_failures']}")
            logger.info(f"Rotações realizadas: {proxy_mgr_stats['total_rotations']}")

        # Relatório para usuário
        print("=" * 80)
        print("📊 PROCESSAMENTO CONCLUÍDO")
        print("=" * 80)
        print(f"⏱️  Tempo total: {processing_time:.1f} segundos")
        print(f"📁 Arquivos processados: {len(resultados)}")
        print(f"✅ Sucessos: {sucessos}")
        print(f"❌ Erros: {erros}")
        
        if proxy_manager:
            api_stats = elevenlabs_api.get_combined_statistics()["api_requests"]
            print(f"\n🌐 Estatísticas de Proxy:")
            print(f"   Total de requisições: {api_stats['total_requests']}")
            print(f"   Via proxy: {api_stats['proxy_requests']}")
            print(f"   Diretas (fallback): {api_stats['direct_requests']}")
            print(f"   Falhas de proxy: {api_stats['proxy_failures']}")
            
            if api_stats['total_requests'] > 0:
                proxy_rate = (api_stats['proxy_requests'] / api_stats['total_requests']) * 100
                print(f"   Taxa de uso de proxy: {proxy_rate:.1f}%")

        print(f"\n📂 Áudios gerados em: {diretorio_saida}")
        
        # Caminho do relatório
        caminho_relatorio = diretorio_saida / 'relatorio_processamento_v3.txt'
        if caminho_relatorio.exists():
            print(f"📋 Relatório detalhado: {caminho_relatorio}")

        print("\n🎉 Processamento finalizado com sucesso!")
        return 0

    except KeyboardInterrupt:
        logger.info("Processamento interrompido pelo usuário")
        print("\n⏹️  Processamento interrompido pelo usuário")
        return 0
        
    except Exception as e:
        logger.exception("Erro fatal durante a execução:")
        print(f"\n💥 ERRO FATAL: {str(e)}")
        print("Verifique os logs para mais detalhes.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
